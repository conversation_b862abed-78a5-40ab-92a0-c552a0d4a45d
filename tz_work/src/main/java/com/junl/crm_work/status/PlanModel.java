package com.junl.crm_work.status;

/**
 * @description: 计划模式定义
 * @author: daiqimeng
 * @date: 2021/7/169:30
 */
public enum PlanModel {

    BIDDING_AND_AMOUNT("竞价竟量模式",1),
    RETAIL("零售模式",2),
    BIDDING("竞价模式",3),
    PRICING("定价模式",4);

    private String name;
    private Integer code;
    PlanModel(String name, Integer code){
        this.name=name;
        this.code=code;
    }

    public String getName(){
        return this.name;
    }
    public Integer getCode(){
        return this.code;
    }
}
