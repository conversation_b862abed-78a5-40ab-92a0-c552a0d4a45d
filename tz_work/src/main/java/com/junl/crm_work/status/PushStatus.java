package com.junl.crm_work.status;

/**
 * @description: 推送标识
 * @author: daiqimeng
 * @date: 2021/8/417:51
 */
public enum  PushStatus {
    //询价邀请
    INQUIRE(0),
    //提前开始询价
    START_INQUIRE(1),
    //提前结束询价
    OVER_INQUIRE(2),
    //竞价邀请
    BIDDING(3),
    //提前竞价通知
    START_BIDDING(4),
    //提前结束竞价通知
    OVER_BIDDING(5),
    //成功完成交易通知
    SUCCESS(6);

    private Integer code;
    PushStatus(Integer code){
        this.code=code;
    }

    public Integer getCode() {
        return this.code;
    }
}
