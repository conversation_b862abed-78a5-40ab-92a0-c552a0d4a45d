package com.junl.crm_work.status;

/**
 * @description: 业务类型定义 配合消息队列使用
 * @author: daiqimeng
 * @date: 2021/7/1619:02
 */
public enum  BusCode {

    //提前开始询价
    START_INQUIRE(8),
    //提前结束询价
    OVER_INQUIRE(7),
    //提前竞价通知
    START_BIDDING(6),
    //提前结束竞价通知
    OVER_BIDDING(5),
    //通知公司参与竞价
    BIDDING_COMPANY(4),
    //通知公司交易完成
    TRANSACTION_SUCCESS_COMPANY(3),
    //公告通知
    NOTICE(2),
    //通知用户审批
    USER_CHECK(1),
    //通知公司参加询价
    INFORM_COMPANY(0);


    private Integer code;
    BusCode(Integer code){
        this.code=code;
    }

    public Integer getCode() {
        return this.code;
    }
}
