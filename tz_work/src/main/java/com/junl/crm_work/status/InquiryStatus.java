package com.junl.crm_work.status;

/**
 * @description: 询价状态定义
 * @author: daiqimeng
 * @date: 2021/7/1617:15
 */
public enum InquiryStatus {

    NOT_ISSUE("未发布",1),
    ISSUE("已发布",2),
    INQUIRY("询价中",3),
    INQUIRY_OVER("询价结束",4),
    SUBMIT("已提交竞价",5);


    private String name;
    private Integer code;

    InquiryStatus(String name,Integer code){
        this.code=code;
        this.name=name;
    }

    public String getName(){
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }
}
