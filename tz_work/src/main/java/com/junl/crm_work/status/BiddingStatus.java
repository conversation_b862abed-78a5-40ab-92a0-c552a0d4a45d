package com.junl.crm_work.status;

/**
 * @description: 竞价状态定义
 * @author: daiqimeng
 * @date: 2021/7/1917:42
 */
public enum  BiddingStatus {
    REVOCATION("撤回",0),
    AUDIT("审核中",1),
    AUDIT_ERROR("审核失败",2),
    AUDIT_SUCCESS("审核成功",3),
    BIDDING("竞价中",4),
    BIDDING_OVER("竞价结束",5),
    BIDDING_AUDIT_OVER("交易成功审核",6),
    BIDDING_AUDIT_OVER_ERROR("交易成功审核未通过",7),
    SUCCESS("交易结束(审核通过)",8),
    SUCCESS_REVOCATION("交易成功审批撤回",9);



    private String name;
    private Integer code;

    BiddingStatus (String name,Integer code){
        this.code=code;
        this.name=name;
    }

    public String getName(){
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }


}
