package com.junl.crm_work.status;

/**
 * @description: 计划状态值定义
 * @author: daiqimeng
 * @date: 2021/7/1617:07
 */
public enum  PlanStatus {
    UNCOMMITTED("未提交",1),
    SUBMITTED("已提交",2),
    PASS("审核通过",3),
    REJECT("已驳回",4),
    SUBMIT("已提交竞价",5),
    REVOCATION("已撤销",6);


    private String name;
    private Integer code;

    PlanStatus(String name,Integer code){
        this.code=code;
        this.name=name;
    }

    public String getName(){
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }
}
