package com.junl.crm_work.status;

/**
 * @description: 审批业务类型定义
 * @author: daiqimeng
 * @date: 2021/7/2011:36
 */
public enum  ApproveStatus {
    /** 销售计划审核 */
    JH_SH(0),
    /** 提交竞价审核 */
    ZJ_SH(1),
    /** 交易完成审核 */
    JY_WC(2);

    private Integer code;

    ApproveStatus(Integer code){
        this.code=code;
    }

    public Integer getCode() {
        return this.code;
    }

    //判断是否是审批流程的节点
    public static boolean getApprove(Integer code){
        for (ApproveStatus value : ApproveStatus.values()) {
            if (value.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
