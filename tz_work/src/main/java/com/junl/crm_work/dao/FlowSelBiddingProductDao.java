package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.FlowSelBiddingProductEntity;
import com.junl.crm_work.vo.FlowSelBiddingProductVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
/**
* @description: flow_sel_bidding_productdao接口
* @author: daiqimeng
**/
@Mapper
public interface FlowSelBiddingProductDao extends BaseMapper<FlowSelBiddingProductEntity> {
    List<FlowSelBiddingProductEntity> queryList(FlowSelBiddingProductVo flowSelBiddingProductVo);
}