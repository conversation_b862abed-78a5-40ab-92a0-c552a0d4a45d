package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_work.vo.SelInquiryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
* @description: sel_inquirydao接口
* @author: daiqimeng
**/

public interface SelInquiryDao extends BaseMapper<SelInquiryEntity> {
    List<SelInquiryEntity> queryList(SelInquiryVo selInquiryVo);

    List<SelInquiryCustomerEntity> queryCustomerOffer(SelInquiryVo selInquiryVo);

    List<SelInquiryEntity> queryAppList(SelInquiryVo selInquiryVo);

    SelInquiryEntity infoInquiry(String id);

    List<SelInquiryOfferEntity> getOfferList(@Param("productId")String productId,@Param("relevancy") List<String> re);

    List<SelInquiryCustomerEntity> getInquiryCustomer(String id);
    int queryInquiryCount(@Param("ids") List<String> planIds,@Param("customerId")String customerId);
    List<String> queryInquiryCustomerIds(@Param("ids") List<String> planIds,@Param("customerId")String customerId);
    int queryInquiryCustomerOfferNum(@Param("ids")List<String> ids,@Param("productId")String productId);


}