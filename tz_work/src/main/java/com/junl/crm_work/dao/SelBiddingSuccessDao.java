package com.junl.crm_work.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelBiddingSuccessEntity;
import com.junl.crm_common.pojo.work.SelBiddingSuccessProductEntity;
import com.junl.crm_work.vo.SelBiddingSuccessVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: sel_bidding_successdao接口
 * @author: daiqimeng
 **/

public interface SelBiddingSuccessDao extends BaseMapper<SelBiddingSuccessEntity> {

    List<SelBiddingSuccessEntity> queryList(SelBiddingSuccessVo selBiddingSuccessVo);


    List<SelBiddingSuccessEntity> queryAppList(SelBiddingSuccessVo selBiddingSuccessVo);

    List<SelBiddingSuccessProductEntity> getAppList(SelBiddingSuccessVo selBiddingSuccessVo);

    List<SelBiddingSuccessProductEntity> querySuccessCount(@Param("ids")List<String> biddingIds,@Param("productId") String productId,@Param("customerId")String customerId);
}