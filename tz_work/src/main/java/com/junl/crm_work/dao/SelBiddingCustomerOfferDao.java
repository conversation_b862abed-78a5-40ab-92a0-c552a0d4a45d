package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_work.vo.SelBiddingCustomerOfferVo;
import java.util.List;
/**
* @description: sel_bidding_customer_offerdao接口
* @author: daiqimeng
**/

public interface SelBiddingCustomerOfferDao extends BaseMapper<SelBiddingCustomerOfferEntity> {
    List<SelBiddingCustomerOfferEntity> queryList(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo);

    SelBiddingCustomerOfferEntity selectOfferOne(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo);
}