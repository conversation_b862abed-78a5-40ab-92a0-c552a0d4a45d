package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_work.vo.SelInquiryCustomerVo;
import java.util.List;
/**
* @description: sel_inquiry_customerdao接口
* @author: daiqimeng
**/

public interface SelInquiryCustomerDao extends BaseMapper<SelInquiryCustomerEntity> {
    List<SelInquiryCustomerEntity> queryList(SelInquiryCustomerVo selInquiryCustomerVo);
}