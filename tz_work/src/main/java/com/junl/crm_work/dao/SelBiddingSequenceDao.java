package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelBiddingSequenceEntity;
import com.junl.crm_work.vo.SelBiddingSequenceVo;
import java.util.List;
/**
* @description: sel_bidding_sequencedao接口
* @author: daiqimeng
**/

public interface SelBiddingSequenceDao extends BaseMapper<SelBiddingSequenceEntity> {
    List<SelBiddingSequenceEntity> queryList(SelBiddingSequenceVo selBiddingSequenceVo);

    List<SelBiddingSequenceEntity> queryAppList(SelBiddingSequenceVo selBiddingSequenceVo);

    SelBiddingSequenceEntity queryAppOne(SelBiddingSequenceVo selBiddingSequenceVo);
}