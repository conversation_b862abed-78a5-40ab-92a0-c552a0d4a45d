package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.FlowSelBiddingCustomerEntity;
import com.junl.crm_work.vo.FlowSelBiddingCustomerVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
/**
* @description: flow_sel_bidding_customerdao接口
* @author: daiqimeng
**/
@Mapper
public interface FlowSelBiddingCustomerDao extends BaseMapper<FlowSelBiddingCustomerEntity> {
    List<FlowSelBiddingCustomerEntity> queryList(FlowSelBiddingCustomerVo flowSelBiddingCustomerVo);
}