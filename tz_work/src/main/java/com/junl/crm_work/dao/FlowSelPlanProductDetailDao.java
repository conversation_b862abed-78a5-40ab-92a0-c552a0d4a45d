package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.FlowSelPlanProductDetailEntity;
import com.junl.crm_work.vo.FlowSelPlanProductDetailVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
/**
* @description: flow_sel_plan_product_detaildao接口
* @author: daiqimeng
**/
@Mapper
public interface FlowSelPlanProductDetailDao extends BaseMapper<FlowSelPlanProductDetailEntity> {
    List<FlowSelPlanProductDetailEntity> queryList(FlowSelPlanProductDetailVo flowSelPlanProductDetailVo);
}