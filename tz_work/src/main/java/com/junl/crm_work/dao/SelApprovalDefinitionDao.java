package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelApprovalDefinitionEntity;
import com.junl.crm_common.pojo.work.SelApprovalUnderwayEntity;
import com.junl.crm_work.vo.SelApprovalFlowVo;

import java.util.List;
/**
* @description: sel_approval_definitiondao接口
* @author: daiqimeng
**/

public interface SelApprovalDefinitionDao extends BaseMapper<SelApprovalDefinitionEntity> {
    List<SelApprovalDefinitionEntity> getList(String companyId);

    List<SelApprovalUnderwayEntity> getAuditRecord(SelApprovalFlowVo selApprovalFlowVo);
}