package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_work.vo.ReportPage;
import com.junl.crm_work.vo.SelBiddingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
* @description: sel_biddingdao接口
* @author: daiqimeng
**/

public interface SelBiddingDao extends BaseMapper<SelBiddingEntity> {
    List<SelBiddingEntity> queryList(SelBiddingVo selBiddingVo);

    List<SelBiddingCustomerEntity> queryBiddingPage(String id);

    SelBiddingEntity getAppInfo(String id);

    List<SelBiddingSequenceEntity> querySequence(String id);


    FlowSelBiddingEntity getFlowBiddingInfo(String id);

    SelBiddingEntity getSuccessInfo(SelBiddingVo selBiddingVo);

    List<SelBiddingCustomerOfferEntity> getOfferList(@Param("productId") String productId,@Param("relevanceList") List<String> relevanceIds);

    List<SelBiddingSuccessEntity> getSuccessCustomer(String biddingId);

    List<SelInquiryOfferEntity> queryInquiry(SelBiddingVo selBiddingVo);

    List<SelBiddingEntity> getSelectList(ReportPage reportPage);
    List<Integer> getListTime(ReportPage reportPage);

    List<SelBiddingCustomerEntity> getBiddingCustomer(String sequenceId);

    int queryMutualNum(@Param("ids")List<String> biddingIds,@Param("productId")String productId,@Param("customerId")String customerId);

    List<String> queryCustomerIds(@Param("ids")List<String> biddingIds,@Param("productId")String productId,@Param("customerId")String customerId);
    int queryCustomerOfferCount(@Param("ids")List<String> ids,@Param("productId")String productId);

}