package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.FlowSelBiddingEntity;
import com.junl.crm_work.vo.FlowSelBiddingVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
/**
* @description: flow_sel_biddingdao接口
* @author: daiqimeng
**/
@Mapper
public interface FlowSelBiddingDao extends BaseMapper<FlowSelBiddingEntity> {
    List<FlowSelBiddingEntity> queryList(FlowSelBiddingVo flowSelBiddingVo);
}