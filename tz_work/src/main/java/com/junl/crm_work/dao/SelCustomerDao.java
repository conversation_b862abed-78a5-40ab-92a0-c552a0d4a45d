package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_work.vo.SelCustomerVo;
import java.util.List;
/**
* @description: sel_customerdao接口
* @author: daiqimeng
**/

public interface SelCustomerDao extends BaseMapper<SelCustomerEntity> {
    List<SelCustomerEntity> queryList(SelCustomerVo selCustomerVo);

    int updateAppCustomerById(SelCustomerEntity customerEntity);
    boolean updateWxAuth(String id);
}