package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelCustomerGroupEntity;
import com.junl.crm_common.pojo.work.SelGroupRelevanceEntity;
import com.junl.crm_work.vo.SelCustomerGroupVo;
import java.util.List;
/**
 * @description: sel_customer_groupdao接口
 * @author: daiqimeng
 **/

public interface SelCustomerGroupDao extends BaseMapper<SelCustomerGroupEntity> {
    List<SelCustomerGroupEntity> queryList(SelCustomerGroupVo selCustomerGroupVo);
    SelCustomerGroupEntity getInfo(String id);

    List<SelGroupRelevanceEntity> queryGroupCustomer(String id);
}