package com.junl.crm_work.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelBiddingProductEntity;
import com.junl.crm_work.vo.SelBiddingProductVo;
import java.util.List;
/**
* @description: sel_bidding_productdao接口
* @author: daiqimeng
**/

public interface SelBiddingProductDao extends BaseMapper<SelBiddingProductEntity> {
    List<SelBiddingProductEntity> queryList(SelBiddingProductVo selBiddingProductVo);

    List<SelBiddingProductEntity> queryAppList(SelBiddingProductVo selBiddingProductVo);
}