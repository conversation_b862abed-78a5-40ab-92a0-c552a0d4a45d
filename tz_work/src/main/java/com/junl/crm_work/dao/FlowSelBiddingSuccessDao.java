package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.FlowSelBiddingSuccessEntity;
import com.junl.crm_work.vo.FlowSelBiddingSuccessVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
/**
* @description: flow_sel_bidding_successdao接口
* @author: daiqimeng
**/
@Mapper
public interface FlowSelBiddingSuccessDao extends BaseMapper<FlowSelBiddingSuccessEntity> {
    List<FlowSelBiddingSuccessEntity> queryList(FlowSelBiddingSuccessVo flowSelBiddingSuccessVo);
}