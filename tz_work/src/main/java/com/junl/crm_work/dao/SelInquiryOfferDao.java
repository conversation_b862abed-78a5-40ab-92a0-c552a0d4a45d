package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_work.vo.SelInquiryOfferVo;
import java.util.List;
/**
* @description: sel_inquiry_offerdao接口
* @author: daiqimeng
**/

public interface SelInquiryOfferDao extends BaseMapper<SelInquiryOfferEntity> {
    List<SelInquiryOfferEntity> queryList(SelInquiryOfferVo selInquiryOfferVo);
}