package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.FlowSelPlanEntity;
import com.junl.crm_common.pojo.work.SelPlanEntity;
import com.junl.crm_work.vo.QuarterVo;
import com.junl.crm_work.vo.SelPlanVo;
import java.util.List;
/**
* @description: sel_plandao接口
* @author: daiqimeng
**/

public interface SelPlanDao extends BaseMapper<SelPlanEntity> {
    List<SelPlanEntity> queryList(SelPlanVo selPlanVo);
    FlowSelPlanEntity getPlanInfo(String id);
    List<String> getIds(QuarterVo quarterVo);
}