package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.FlowSelPlanEntity;
import com.junl.crm_work.vo.FlowSelPlanVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
/**
* @description: flow_sel_plandao接口
* @author: daiqimeng
**/
@Mapper
public interface FlowSelPlanDao extends BaseMapper<FlowSelPlanEntity> {
    List<FlowSelPlanEntity> queryList(FlowSelPlanVo flowSelPlanVo);
}