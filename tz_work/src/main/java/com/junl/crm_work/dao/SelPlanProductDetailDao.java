package com.junl.crm_work.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_work.vo.SelPlanProductDetailVo;
import java.util.List;
/**
* @description: sel_plan_product_detaildao接口
* @author: daiqimeng
**/

public interface SelPlanProductDetailDao extends BaseMapper<SelPlanProductDetailEntity> {
    List<SelPlanProductDetailEntity> queryList(SelPlanProductDetailVo selPlanProductDetailVo);

    List<SelPlanProductDetailEntity> queryAppList(SelPlanProductDetailVo selPlanProductDetailVo);
}