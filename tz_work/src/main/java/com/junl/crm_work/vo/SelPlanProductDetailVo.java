package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SelPlanProductDetailVo
* @author:      daiqimeng
**/
@ApiModel(description = "销售计划明细表-Vo类")
@Data
public class SelPlanProductDetailVo extends ParentDto {

    @ApiModelProperty("企业id")
    private String customerId;

    @ApiModelProperty("计划主ID")
    private String planId;
}