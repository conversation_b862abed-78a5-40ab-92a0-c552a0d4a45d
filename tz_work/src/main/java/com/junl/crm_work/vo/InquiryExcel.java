package com.junl.crm_work.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.tools.excel.SheetName;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 询价excel导出对象
 * @author: daiqimeng
 * @date: 2021/8/1014:57
 */
@Data
@ContentRowHeight(18)
@ColumnWidth(30)
@SheetName("询价报表")
public class InquiryExcel {

    @ExcelProperty(index = 0)
    private String productName;
    @ExcelProperty(index = 1)
    private String customerName;
    @ExcelProperty(index = 2)
    @NumberFormat("#.##%")
    private Double count;
    @ExcelProperty(index = 3)
    @NumberFormat("#.##%")
    private Double price;
    @ExcelProperty(index = 4)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date submitDate;

    private Integer size;

    private Integer flag=0;


    /**
     * @describe:  根据开始时间和结束时间获取表头
     * <AUTHOR>
     * @date 2021/8/10 15:12
     * @param start
     * @param end
     * @return: {@link List< List< String>>}
     */
    public static List<List<String>> getHead(String name,String start,String end){
        List<List<String>> head=new ArrayList<>();

        List<String> list1=new ArrayList<>();
        list1.add(name+"询价报表");
        list1.add("开始时间："+start);
        list1.add("产品名称");

        List<String> list2=new ArrayList<>();
        list2.add(name+"询价报表");
        list2.add("开始时间："+start);
        list2.add("客户名称");

        List<String> list3=new ArrayList<>();
        list3.add(name+"询价报表");
        list3.add("开始时间："+start);
        list3.add("报量");

        List<String> list4=new ArrayList<>();
        list4.add(name+"询价报表");
        list4.add("结束时间："+end);
        list4.add("报价");

        List<String> list5=new ArrayList<>();
        list5.add(name+"询价报表");
        list5.add("结束时间："+end);
        list5.add("报价时间");


        head.add(list1);
        head.add(list2);
        head.add(list3);
        head.add(list4);
        head.add(list5);
        return head;
    }

    public static void main(String[] args)throws Exception {
//        List<InquiryExcel> list=new ArrayList<>();
//
//        List<InquiryExcel> temp1=new ArrayList<>();
//        List<InquiryExcel> temp2=new ArrayList<>();
//        for(int i=0;i<10;i++){
//            InquiryExcel inquiryExcel=new InquiryExcel();
//            inquiryExcel.setCount(i);
//            inquiryExcel.setPrice(i);
//            inquiryExcel.setCustomerName("客户"+i);
//            inquiryExcel.setProductName("产品1号");
//            inquiryExcel.setSubmitDate(new Date());
//            temp1.add(inquiryExcel);
//
//            InquiryExcel tempEntity=new InquiryExcel();
//            tempEntity.setCount(i);
//            tempEntity.setPrice(i);
//            tempEntity.setCustomerName("客户"+i);
//            tempEntity.setProductName("产品2号");
//            tempEntity.setSubmitDate(new Date());
//            temp2.add(tempEntity);
//        }
//
//
//        InquiryExcel inquiryExcel=new InquiryExcel();
//        inquiryExcel.setCount(temp1.stream().mapToInt(InquiryExcel::getCount).sum());
//        inquiryExcel.setPrice(temp1.stream().mapToInt(InquiryExcel::getPrice).sum());
//        inquiryExcel.setProductName("小计:");
//
//        InquiryExcel inquiry2=new InquiryExcel();
//        inquiry2.setCount(temp2.stream().mapToInt(InquiryExcel::getCount).sum());
//        inquiry2.setPrice(temp2.stream().mapToInt(InquiryExcel::getPrice).sum());
//        inquiry2.setProductName("小计:");
//
//
//        list.addAll(temp1);
//        list.add(inquiryExcel);
//        list.addAll(temp2);
//        list.add(inquiry2);
//
//        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
//
//
//
//        ExcelUtils excelUtils=new ExcelUtils();
//        FileOutputStream fileOutputStream= new FileOutputStream("D:\\测试.xlsx");
//        excelUtils.write(list,InquiryExcel.class,fileOutputStream,
//                InquiryExcel.getHead("2020年石油拍卖",simpleDateFormat.format(new Date()),simpleDateFormat.format(new Date())),
//        3,new int[]{0,4});

    }
}
