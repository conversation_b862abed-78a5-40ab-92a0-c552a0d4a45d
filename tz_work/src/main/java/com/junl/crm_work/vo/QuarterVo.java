package com.junl.crm_work.vo;

import com.junl.crm_common.annotation.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * @description: 季度入参实体类
 * @date: 2021/9/2616:08
 */
@Data
public class QuarterVo {
    //年份
    @NotNull
    private int year;
    //产品Id
    @NotNull
    private String productId;
    //季度
    @NotNull
    private int quarter;
    //模式
    @NotNull
    private int modeType;
    //公司Id
    private String companyId;

    private Date startDate;
    private Date endDate;
}
