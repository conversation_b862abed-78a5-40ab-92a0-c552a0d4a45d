package com.junl.crm_work.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.ParentDto;
import com.junl.crm_common.pojo.work.SelBiddingCustomerEntity;
import com.junl.crm_common.pojo.work.SelBiddingProductEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
* @description: SelBiddingVo
* @author:      daiqimeng
**/
@ApiModel(description = "销售竞价表-Vo类")
@Data
public class SelBiddingVo extends ParentDto {
    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式", allowEmptyValue = true, required = false)
    private  Integer modeType;
    @ApiModelProperty(notes = "竞价状态1-竞价待发布2-竞价审核中3-竞价已发布4-竞价中5-竞价结束6-成交待审核7-成交审核中8-已完成9-成交已通过10-已完成11-已关闭12-已超时", allowEmptyValue = true, required = false)
    private  Integer biddingStatus;
    @ApiModelProperty(notes = "竞价名称", allowEmptyValue = true, required = false)
    private  String planName;

    @ApiModelProperty("公司id")
    private String companyId;

    @ApiModelProperty("竞价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date biddingStartDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("竞价结束时间")
    @NotNull
    private Date biddingEndTime;


    @ApiModelProperty("竞价id")
    @NotNull
    private String id;
    @NotNull
    @ApiModelProperty("关联表实体")
    private List<SelBiddingCustomerEntity> customerList;

    @ApiModelProperty("商品基价")
    private List<SelBiddingProductEntity> productList;

    @ApiModelProperty("用户id")
    private List<String> userIds;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("轮次")
    private Integer round;

    @ApiModelProperty("关联表Id")
    private String relevancyId;

    @ApiModelProperty("客户Id")
    private String customerId;

    @ApiModelProperty(notes = "0 未结束 1已结束")
    private Integer flag;

}