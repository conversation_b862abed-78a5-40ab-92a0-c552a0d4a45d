package com.junl.crm_work.vo;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.ParentDto;
import com.junl.crm_common.pojo.work.SelBiddingCustomerEntity;
import com.junl.crm_common.pojo.work.SelBiddingProductEntity;
import com.junl.crm_common.status.IdentityStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
* @description: SelInquiryVo
* @author:      daiqimeng
**/
@ApiModel(description = "销售询价主表表-Vo类")
@Data
public class SelInquiryVo extends ParentDto {

    @ApiModelProperty("公司id")
    private String companyId;

    @ApiModelProperty("企业id")
    private String customerId;

    @ApiModelProperty(notes = "询价名称")
    private String inquiryName;

    @ApiModelProperty(notes = "询价开始时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date inquiryStartDate;

    @ApiModelProperty(notes = "询价结束时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date inquiryEndTime;

    @ApiModelProperty(notes = "询价状态1-待发布2-询价中3-询价结束")
    private Integer inquiryStatus;


    @ApiModelProperty("id")
    @NotNull
    private String id;

    @ApiModelProperty("竞价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date biddingStartDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("竞价结束时间")
    @NotNull
    private Date biddingEndTime;

    @ApiModelProperty("商品基价")
    @NotNull
    private List<SelBiddingProductEntity> productList;

    @ApiModelProperty("竞价企业")
    @NotNull
    private List<SelBiddingCustomerEntity> customerList;

    @ApiModelProperty("用户id")
    private List<String> userIds;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty(notes = "运输方式")
    private String trafficMode;

    @ApiModelProperty(notes = "交货方式")
    private Integer deliveryMode;


    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "交货日期开始")
    private Date deliveryStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "交货日期结束")
    private Date deliveryEndTime;

    @ApiModelProperty(notes = "质量标准")
    private String quality;

    @ApiModelProperty(notes = "交易描述")
    @TableField("`describe`")
    private String describe;

    @ApiModelProperty(notes = "0 未结束 1已结束")
    private Integer flag;
}