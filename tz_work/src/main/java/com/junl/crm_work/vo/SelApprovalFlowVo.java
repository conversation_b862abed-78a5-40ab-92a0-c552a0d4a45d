package com.junl.crm_work.vo;

import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.ParentDto;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: SelApprovalFlowVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "审批流程进度表表-Vo类")
@Data
public class SelApprovalFlowVo extends ParentDto {
    @ApiModelProperty(notes = "进行中的审批流程主健", allowEmptyValue = true, required = false)
    private  String underwayId;

    @ApiModelProperty(notes = "结果 1同意 2驳回")
    @NotNull
    private Integer result;

    @ApiModelProperty(notes = "审批留言")
    private String remark;

    @ApiModelProperty(notes = "消息体")
    @NotNull
    private SysUserMsgEntity sysUserMsgEntity;

    @ApiModelProperty(name = "业务主键id")
    private String id;

    @ApiModelProperty(notes = "业务类型")
    private List<Integer> busType;
}