package com.junl.crm_work.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.ParentDto;
import com.junl.crm_common.pojo.work.SelBiddingCustomerEntity;
import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_common.pojo.work.SelBiddingProductEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
* @description: SelPlanVo
* @author:      daiqimeng
**/
@ApiModel(description = "销售计划表-Vo类")
@Data
public class SelPlanVo extends ParentDto {

    @ApiModelProperty("公司id")
    private String companyId;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty(notes = "周期开始时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private Date planStartDate;

    @ApiModelProperty(notes = "周期结束时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private Date planEndTime;

    @ApiModelProperty("商品")
    private List<SelPlanProductDetailEntity> product;

    @ApiModelProperty("用户id")
    private List<String> userIds;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("业务主键id")
    private String id;

    @ApiModelProperty("商品基价")
    @NotNull
    private List<SelBiddingProductEntity> productList;

    @ApiModelProperty("竞价企业")
    @NotNull
    private List<SelBiddingCustomerEntity> customerList;


    @ApiModelProperty("竞价企业报价信息")
    private List<SelBiddingCustomerOfferEntity> customerOfferList;

    @ApiModelProperty("竞价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date biddingStartDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("竞价结束时间")
    @NotNull
    private Date biddingEndTime;

    @ApiModelProperty(notes = "运输方式")
    private String trafficMode;

    @ApiModelProperty(notes = "交货方式")
    private Integer deliveryMode;


    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "交货日期开始")
    private Date deliveryStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "交货日期结束")
    private Date deliveryEndTime;

    @ApiModelProperty(notes = "质量标准")
    private String quality;

    @ApiModelProperty(notes = "交易描述")
    @TableField("`describe`")
    private String describe;

    @ApiModelProperty(notes = "0 未结束 1已结束")
    private Integer flag;
}