package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @description: SelApprovalUnderwayVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "正在进行中的流程表-Vo类")
@Data
public class SelApprovalUnderwayVo extends ParentDto {
    @ApiModelProperty(notes = "公司id", allowEmptyValue = true, required = false)
    private  String companyId;
    @ApiModelProperty(notes = "创建人", allowEmptyValue = true, required = false)
    private  String createBy;

}