package com.junl.crm_work.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.junl.crm_common.tools.excel.SheetName;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 季度报表excelc操作对象
 * @author: daiqimeng
 * @date: 2021/9/2615:36
 */
@Data
@ContentRowHeight(18)
@ColumnWidth(30)
@SheetName("季度报表")
public class QuarterReport {
    //客户名称
    @ExcelProperty(index = 0)
    private String customerName;
    //询价次数
    @ExcelProperty(index = 1)
    private int planNum;
    //成交次数
    @ExcelProperty(index = 2)
    private int successNum;
    //交互次数
    @ExcelProperty(index = 3)
    private int mutualNum;
    //参与次数
    @ExcelProperty(index = 4)
    private int takeNum;
    //交易总量
    @ExcelProperty(index = 5)
    @NumberFormat("#.##%")
    private double successCount;
    //购买量
    @ExcelProperty(index = 6)
    @NumberFormat("#.##%")
    private double buyCount;
    //成交量占比
    @ExcelProperty(index = 7)
    @NumberFormat("#.##%")
    private double successCountRatio;
    //活跃度
    @ExcelProperty(index = 8)
    @NumberFormat("#.##%")
    private double liveness;
    //成交率
    @ExcelProperty(index = 9)
    @NumberFormat("#.##%")
    private double successRatio;





    public static List<List<String>> getHead(String name){
        List<List<String>> head=new ArrayList<>();

        List<String> list1=new ArrayList<>();
        list1.add(name);
        list1.add("客户名称");

        List<String> list2=new ArrayList<>();
        list2.add(name);
        list2.add("询价次数");

        List<String> list3=new ArrayList<>();
        list3.add(name);
        list3.add("成交次数");

        List<String> list4=new ArrayList<>();
        list4.add(name);
        list4.add("交互次数");

        List<String> list5=new ArrayList<>();
        list5.add(name);
        list5.add("参与次数");

        List<String> list6=new ArrayList<>();
        list6.add(name);
        list6.add("交易总量");

        List<String> list7=new ArrayList<>();
        list7.add(name);
        list7.add("购买量");

        List<String> list8=new ArrayList<>();
        list8.add(name);
        list8.add("成交占比%");

        List<String> list9=new ArrayList<>();
        list9.add(name);
        list9.add("活跃度%");

        List<String> list10=new ArrayList<>();
        list10.add(name);
        list10.add("成交率%");



        head.add(list1);
        head.add(list2);
        head.add(list3);
        head.add(list4);
        head.add(list5);
        head.add(list6);
        head.add(list7);
        head.add(list8);
        head.add(list9);
        head.add(list10);

        return head;
    }

}
