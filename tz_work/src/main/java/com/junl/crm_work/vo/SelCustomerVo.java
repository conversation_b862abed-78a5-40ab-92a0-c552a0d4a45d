package com.junl.crm_work.vo;

import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SelCustomerVo
* @author:      daiqimeng
**/
@ApiModel(description = "企业表表-Vo类")
@Data
public class SelCustomerVo extends ParentDto {
    @ApiModelProperty(notes = "客户名称", allowEmptyValue = true, required = false)
    private  String customerName;
    @ApiModelProperty(notes = "经营产品类型:1燃料油类 2化工类 3苯类 4其它", allowEmptyValue = true, required = false)
    private  String manageProduct;
    @ApiModelProperty(notes = "身份证号", allowEmptyValue = true, required = false)
    private  String deputyCardNo;
    @ApiModelProperty(notes = "法人电话", allowEmptyValue = true, required = false)
    private  String deputyPhone;
    @ApiModelProperty(notes = "是否是否企业认证0-否1-是", allowEmptyValue = true, required = false)
    private  Integer isAuth;

    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty("code码")
    @NotNull
    public String code;
    @NotNull
    @ApiModelProperty("手机号码")
    private String phone;
    @NotNull
    @ApiModelProperty("短信code")
    private String smsCode;

}