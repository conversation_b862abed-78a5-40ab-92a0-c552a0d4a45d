package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @description: SelCustomerGroupVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "企业分组表-Vo类")
@Data
public class SelCustomerGroupVo extends ParentDto {
    @ApiModelProperty(notes = "公司Id", allowEmptyValue = true, required = false)
    private  String companyId;
    @ApiModelProperty(notes = "组名称", allowEmptyValue = true, required = false)
    private  String groupName;

    @ApiModelProperty(notes = "用户Id")
    private String userId;

}