package com.junl.crm_work.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.ParentDto;
import com.junl.crm_common.tools.DateUtils;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/8/2016:00
 */
@Data
public class ReportPage extends ParentDto {

    public ReportPage(){
        super.setLimit(1);
    }

    @NotNull
    //多少期
    private String time;
    //导出页码(为null导出全部)
    private Integer number;
    //啥模式
    @NotNull
    private Integer modeType;

    @DateTimeFormat(pattern = "yyyy")
    @JsonFormat(pattern = "yyyy")
    @NotNull
    private Date date;

    private String companyId;


}
