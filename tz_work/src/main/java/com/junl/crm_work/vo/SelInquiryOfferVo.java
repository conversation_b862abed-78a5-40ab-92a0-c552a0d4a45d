package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SelInquiryOfferVo
* @author:      daiqimeng
**/
@ApiModel(description = "企业对于询价的报价报量记录表表-Vo类")
@Data
public class SelInquiryOfferVo extends ParentDto {

    @ApiModelProperty(notes = "询价ID", required = false)
    private String inquiryId;

    @ApiModelProperty(notes = "关联表id", required = false)
    private String relevancyId;

    @ApiModelProperty(notes = "客户id", required = false)
    private String customerId;
}