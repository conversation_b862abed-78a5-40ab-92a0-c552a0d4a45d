package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SelBiddingSuccessVo
* @author:      daiqimeng
**/
@ApiModel(description = "竞价企业交易成功表表-Vo类")
@Data
public class SelBiddingSuccessVo extends ParentDto {

    @ApiModelProperty("企业ID")
    private String offerId;

    @ApiModelProperty("轮次id")
    private String sequenceId;

}