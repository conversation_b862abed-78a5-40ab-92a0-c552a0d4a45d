package com.junl.crm_work.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
* @description: SelBiddingSequenceVo
* @author:      daiqimeng
**/
@ApiModel(description = "竞价轮次关联表表-Vo类")
@Data
public class SelBiddingSequenceVo extends ParentDto {

    @ApiModelProperty("企业id")
    private String customerId;

    @ApiModelProperty("竞价id")
    private String biddingId;

    @ApiModelProperty(notes = "竞价名称")
    private String planName;

    @ApiModelProperty(notes = "竞价状态1-待竞价2-竞价中3-竞价结束")
    private Integer biddingStatus;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planStartDate;


    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planEndTime;





}