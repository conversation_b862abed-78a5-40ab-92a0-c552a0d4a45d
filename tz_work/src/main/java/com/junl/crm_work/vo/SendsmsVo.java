package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SendsmsVo
* @author:      daiqimeng
**/
@ApiModel(description = "发送短信表表-Vo类")
@Data
public class SendsmsVo extends ParentDto {
    @ApiModelProperty(notes = "手机号码，一次最多发送100个手机号码", allowEmptyValue = true, required = false)
    private  String phonenumber;

}