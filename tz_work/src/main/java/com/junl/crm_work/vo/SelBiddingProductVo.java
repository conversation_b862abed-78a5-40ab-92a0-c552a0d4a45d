package com.junl.crm_work.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.common.ParentDto;
import com.junl.crm_common.pojo.work.SelBiddingProductEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
* @description: SelBiddingProductVo
* @author:      daiqimeng
**/
@ApiModel(description = "竞价商品详情表表-Vo类")
@Data
public class SelBiddingProductVo extends ParentDto {

    @ApiModelProperty(notes = "轮次序列id",allowEmptyValue = true, required = false)
    private String sequenceId;

    @ApiModelProperty("企业id")
    private String customerId;


    // 商品数组
    List<SelBiddingProductEntity> productList;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式")
    private Integer modeType;

    @ApiModelProperty(notes = "运输方式")
    private String trafficMode;

    @ApiModelProperty(notes = "交货方式")
    private Integer deliveryMode;

    @ApiModelProperty(notes = "交货日期开始")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryStartDate;

    @ApiModelProperty(notes = "交货日期结束")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryEndTime;

    @ApiModelProperty(notes = "质量标准")
    private String quality;

    @ApiModelProperty(notes = "交易描述")
    @TableField("`describe`")
    private String describe;

    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @ApiModelProperty(notes = "备注")
    private String remark;

}