package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

/**
* @description: SelProductVo
* @author:      daiqimeng
**/
@ApiModel(description = "商品表表-Vo类")
@Data
public class SelProductVo extends ParentDto {

    @ApiModelProperty("公司id")
    private String companyId;

    @ApiModelProperty("商品名称")
    private String productName;

}