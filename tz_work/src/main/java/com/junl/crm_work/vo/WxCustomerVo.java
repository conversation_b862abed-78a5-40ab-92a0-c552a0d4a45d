package com.junl.crm_work.work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: WxCustomerVo
* @author:      daiqimeng
**/
@ApiModel(description = "微信客户表表-Vo类")
@Data
public class WxCustomerVo extends ParentDto {
    @ApiModelProperty(notes = "省", allowEmptyValue = true, required = false)
    private  String province;
    @ApiModelProperty(notes = "市", allowEmptyValue = true, required = false)
    private  String city;
    @ApiModelProperty(notes = "真实姓名", allowEmptyValue = true, required = false)
    private  String realName;
    @ApiModelProperty(notes = "电话号码", allowEmptyValue = true, required = false)
    private  String phone;

}