package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: RecvsmsVo
* @author:      daiqimeng
**/
@ApiModel(description = "短信回复表表-Vo类")
@Data
public class RecvsmsVo extends ParentDto {
    @ApiModelProperty(notes = "回复短信的号码", allowEmptyValue = true, required = false)
    private  String sendnumber;
    @ApiModelProperty(notes = "回复人号码", allowEmptyValue = true, required = false)
    private  String callmdn;

}