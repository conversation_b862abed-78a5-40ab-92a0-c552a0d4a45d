package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SelBiddingCustomerOfferVo
* @author:      daiqimeng
**/
@ApiModel(description = "竞价企业报量表表-Vo类")
@Data
public class SelBiddingCustomerOfferVo extends ParentDto {

    @ApiModelProperty(notes = "序列轮次id", required = false)
    private String sequenceId;

    @ApiModelProperty(notes = "所属轮次", required = false)
    private Integer sequence;

    @ApiModelProperty(notes = "多轮次以逗号分隔", required = false)
    private String sequences;

    @ApiModelProperty(notes = "商品id", required = false)
    private String productId;

    @ApiModelProperty(notes = "竞价id", required = false)
    private String biddingId;

    @ApiModelProperty(notes = "关联表id", required = false)
    private String relevancyId;

    @ApiModelProperty(notes = "客户id", required = false)
    private String customerId;
}