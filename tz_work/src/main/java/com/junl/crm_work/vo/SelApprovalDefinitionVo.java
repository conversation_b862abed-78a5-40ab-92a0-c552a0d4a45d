package com.junl.crm_work.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @description: SelApprovalDefinitionVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "流程定义表表-Vo类")
@Data
public class SelApprovalDefinitionVo extends ParentDto {
    @ApiModelProperty(notes = "公司id", allowEmptyValue = true, required = false)
    private  String companyId;
    @ApiModelProperty(notes = "业务类型 0销售计划审核 1 提交竞价审核 2 交易完成审核", allowEmptyValue = true, required = false)
    private  Integer busCode;

}