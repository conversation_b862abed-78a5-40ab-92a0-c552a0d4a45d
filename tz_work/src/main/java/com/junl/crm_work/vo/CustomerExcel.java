package com.junl.crm_work.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.junl.crm_common.tools.excel.SheetName;
import lombok.Data;

/**
 * @Despetion  企业信息导出excel
 * */

@Data
@ContentRowHeight(18)
@ColumnWidth(30)
@SheetName("企业人员明细")
public class CustomerExcel {

    @ExcelProperty("企业名称")
    private String customerName;
    @ExcelProperty("企业编码")
    private String customerCode;
    @ExcelProperty("经营类型")
    private String manageProduct;
    @ExcelProperty("法人")
    private String deputy;
    @ExcelProperty("法人身份证")
    private String deputyCardNo;
    @ExcelProperty("法人手机号")
    private String deputyPhone;
    @ExcelProperty("注册资金(万元)")
    @NumberFormat("#.##%")
    private Double registerFee;
    @ExcelProperty("公司电话")
    private String telephone;
    @ExcelProperty("是否认证")
    private String is_auth;
    @ExcelProperty("所在省")
    private String province;
    @ExcelProperty("所在市")
    private String city;
    @ExcelProperty("所在区")
    private String region;
    @ExcelProperty("详细地址")
    private String detailAddress;
    @ExcelProperty("邮政编码")
    private String postalCode;
    @ExcelProperty("是否微信认证")
    private String isWxAuth;




}
