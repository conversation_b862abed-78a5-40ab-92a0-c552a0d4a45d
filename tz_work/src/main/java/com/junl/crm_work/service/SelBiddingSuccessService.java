package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelBiddingSuccessEntity;
import com.junl.crm_common.pojo.work.SelBiddingSuccessProductEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_work.vo.SelBiddingSuccessVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:SelBiddingSuccessservice接口
 * @author: daiqimeng
 **/
public interface SelBiddingSuccessService extends IService<SelBiddingSuccessEntity> {
    PageEntity queryPage(SelBiddingSuccessVo selBiddingSuccessVo);

    PageEntity queryAppPage(SelBiddingSuccessVo selBiddingSuccessVo);

    /**
     * @description: 查询列表
     * @param selBiddingSuccessVo
     */
    Result queryList(SelBiddingSuccessVo selBiddingSuccessVo, SelCustomerEntity customerEntity);
    List<SelBiddingSuccessProductEntity> querySuccessCount(List<String> biddingIds,String productId,String customerId);
}