package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_work.vo.SelPlanProductDetailVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SelPlanProductDetailservice接口
* @author: daiqimeng
**/
public interface SelPlanProductDetailService extends IService<SelPlanProductDetailEntity> {
    PageEntity queryPage(SelPlanProductDetailVo selPlanProductDetailVo);


}