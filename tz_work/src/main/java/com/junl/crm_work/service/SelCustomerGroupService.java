package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.SelCustomerGroupEntity;
import com.junl.crm_common.pojo.work.SelGroupRelevanceEntity;
import com.junl.crm_work.vo.SelCustomerGroupVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description:SelCustomerGroupservice接口
 * @author: daiqimeng
 **/
public interface SelCustomerGroupService extends IService<SelCustomerGroupEntity> {
    PageEntity queryPage(SelCustomerGroupVo selCustomerGroupVo);
    SelCustomerGroupEntity getInfo(String id);
    boolean saveGroup(SelCustomerGroupEntity selCustomerGroupEntity);

    List<SelGroupRelevanceEntity> queryGroupCustomer(@PathVariable("id")String id);
}