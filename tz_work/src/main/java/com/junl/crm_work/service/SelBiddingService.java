package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.SuccessExcel;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_work.vo.*;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
* @description:SelBiddingservice接口
* @author: daiqimeng
**/
public interface SelBiddingService extends IService<SelBiddingEntity> {
    PageEntity queryPage(SelBiddingVo selBiddingVo);
    PageEntity queryBiddingPage(SelBiddingVo selBiddingVo);
    SelBiddingEntity getSuccessInfo(SelBiddingVo selBiddingVo);
    SelBiddingEntity info(String id);
    SelBiddingEntity getAppinfo(String id);
    boolean again(SelInquiryVo selInquiryVo);

    List<SelBiddingSequenceEntity> queryFee(String id);

    boolean success(SelBiddingVo selBiddingVo);
    boolean againSubmitSuccess( SelBiddingVo selBiddingVo);

    void updateStatus(String id);

    boolean againSubmit(SelBiddingEntity selBiddingVo);

    FlowSelBiddingEntity getFlowBiddingInfo(String id);
    FlowSelBiddingEntity getFlowSuccessInfo(String id);
    Map<String,String> exportExcel(SelBiddingVo selBiddingVo);

    Map<String,String> exportSuccessExcel(String id);

    List<SelInquiryOfferEntity> queryInquiry(SelBiddingVo selBiddingVo);
    SelBiddingEntity querySuccessInfo(String id);
    List<BiddingExcel> queryBiddingInfo(SelBiddingVo selBiddingVo);

    PageEntity getPlanReport(ReportPage reportPage);

    Map<String,String> exportReport(ReportPage reportPage);
    List<Integer> getListTime(ReportPage reportPage);

    boolean biddingRevocation(String id);
    boolean successRevocation(String id);

    boolean successPricing(SelPlanVo selPlanVo);

    SelBiddingEntity getPricing(String id);

    boolean submitBidding(SelPlanVo selPlanVo);

    List<SelBiddingCustomerEntity> filter(String id);

    List<SelBiddingCustomerEntity> getCustomerAll(String id);

    List<QuarterReport> queryLivenessAndCloseRateReport( QuarterVo quarterVo);

    Map<String,Object> exportLivenessAndCloseRateReport(QuarterVo quarterVo);


    boolean deleteById(String id);


}