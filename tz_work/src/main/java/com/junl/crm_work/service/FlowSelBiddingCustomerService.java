package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.FlowSelBiddingCustomerEntity;
import com.junl.crm_work.vo.FlowSelBiddingCustomerVo;
import com.junl.crm_common.common.PageEntity;

/**
* @description:FlowSelBiddingCustomerservice接口
* @author: daiqimeng
**/
public interface FlowSelBiddingCustomerService extends IService<FlowSelBiddingCustomerEntity> {
    PageEntity queryPage(FlowSelBiddingCustomerVo flowSelBiddingCustomerVo);
}