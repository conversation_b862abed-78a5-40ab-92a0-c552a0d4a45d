package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.ExcelResult;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelProductEntity;
import com.junl.crm_work.vo.SelProductVo;
import com.junl.crm_common.common.PageEntity;

import java.io.ByteArrayOutputStream;

/**
* @description:SelProductservice接口
* @author: daiqimeng
**/
public interface SelProductService extends IService<SelProductEntity> {

    PageEntity queryPage(SelProductVo selProductVo);

    String download(ByteArrayOutputStream byteArrayOutputStream);
    ExcelResult importExcel(String url, SysUserEntity sysUserEntity);
}