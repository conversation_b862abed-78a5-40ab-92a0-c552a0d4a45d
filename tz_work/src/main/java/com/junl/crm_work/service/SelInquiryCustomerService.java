package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_work.vo.SelInquiryCustomerVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SelInquiryCustomerservice接口
* @author: daiqimeng
**/
public interface SelInquiryCustomerService extends IService<SelInquiryCustomerEntity> {
    PageEntity queryPage(SelInquiryCustomerVo selInquiryCustomerVo);
}