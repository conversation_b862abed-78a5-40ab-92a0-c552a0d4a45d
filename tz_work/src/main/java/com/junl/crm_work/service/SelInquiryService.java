package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_work.vo.InquiryExcel;
import com.junl.crm_work.vo.SelInquiryVo;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_work.vo.SelPlanVo;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
* @description:SelInquiryservice接口
* @author: daiqimeng
**/
public interface SelInquiryService extends IService<SelInquiryEntity> {
    PageEntity queryPage(SelInquiryVo selInquiryVo);

    List<InquiryExcel> getInquiryInfo(String id);

    PageEntity queryCustomerOffer(SelInquiryVo selInquiryVo);

    PageEntity queryAppPage(SelInquiryVo selInquiryVo);
    SelInquiryEntity infoInquiry(String id);
    boolean issue(SelInquiryEntity selInquiryEntity);

    boolean bidding(SelInquiryVo selInquiryVo);


    List<SelPlanProductDetailEntity> queryProduct(String id);

    Map<String,String> exportExcel(String id);

    List<SelInquiryCustomerEntity> getInquiryCustomer(String id);
    List<SelInquiryCustomerEntity> queryInquiryCustomer(String id);

    boolean savePlan(SelInquiryEntity selInquiryEntity);

    boolean updatePlan(SelInquiryEntity selInquiryEntity);
}