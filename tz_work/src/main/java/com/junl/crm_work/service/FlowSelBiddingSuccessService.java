package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.FlowSelBiddingSuccessEntity;
import com.junl.crm_work.vo.FlowSelBiddingSuccessVo;
import com.junl.crm_common.common.PageEntity;

/**
* @description:FlowSelBiddingSuccessservice接口
* @author: daiqimeng
**/
public interface FlowSelBiddingSuccessService extends IService<FlowSelBiddingSuccessEntity> {
    PageEntity queryPage(FlowSelBiddingSuccessVo flowSelBiddingSuccessVo);
}