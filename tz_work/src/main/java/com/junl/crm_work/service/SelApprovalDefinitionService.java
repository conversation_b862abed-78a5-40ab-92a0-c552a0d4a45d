package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelApprovalDefinitionEntity;
import com.junl.crm_common.pojo.work.SelApprovalUnderwayEntity;
import com.junl.crm_work.vo.SelApprovalFlowVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
* @description:SelApprovalDefinitionservice接口
* @author: daiqimeng
**/
public interface SelApprovalDefinitionService extends IService<SelApprovalDefinitionEntity> {
    List<SelApprovalDefinitionEntity> getList(SysUserEntity sysUserEntity);
    SelApprovalDefinitionEntity getInfo(String id);
        /** 提交审核 */
    boolean submit(@RequestBody SelApprovalFlowVo selApprovalFlowVo);

    List<SelApprovalUnderwayEntity> getAuditRecord(@RequestBody SelApprovalFlowVo selApprovalFlowVo);
}