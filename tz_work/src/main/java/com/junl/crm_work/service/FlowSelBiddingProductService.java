package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.FlowSelBiddingProductEntity;
import com.junl.crm_work.vo.FlowSelBiddingProductVo;
import com.junl.crm_common.common.PageEntity;

/**
* @description:FlowSelBiddingProductservice接口
* @author: daiqimeng
**/
public interface FlowSelBiddingProductService extends IService<FlowSelBiddingProductEntity> {
    PageEntity queryPage(FlowSelBiddingProductVo flowSelBiddingProductVo);
}