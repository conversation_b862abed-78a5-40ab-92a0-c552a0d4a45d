package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_work.vo.SelBiddingCustomerOfferVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SelBiddingCustomerOfferservice接口
* @author: daiqimeng
**/
public interface SelBiddingCustomerOfferService extends IService<SelBiddingCustomerOfferEntity> {
    PageEntity queryPage(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo);

    Result queryList(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo, SelCustomerEntity customerEntity);

    List<SelBiddingCustomerOfferEntity> getList(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo);

    SelBiddingCustomerOfferEntity selectOfferOne(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo);
}