package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.ExcelResult;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_work.vo.SelCustomerVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.Map;

/**
* @description:SelCustomerservice接口
* @author: daiqimeng
**/
public interface SelCustomerService extends IService<SelCustomerEntity> {
    PageEntity queryPage(SelCustomerVo selCustomerVo);
    String download(ByteArrayOutputStream outputStream);

    ExcelResult importExcel(String url, SysUserEntity sysUserEntity);
    /**
    * <AUTHOR>
    * @Description App修改
    **/
    int updateAppCustomerById(SelCustomerEntity customerEntity);

    boolean updateWxAuth(String id);

    Map<String,String> exportExcel(SelCustomerVo selCustomerVo);
}