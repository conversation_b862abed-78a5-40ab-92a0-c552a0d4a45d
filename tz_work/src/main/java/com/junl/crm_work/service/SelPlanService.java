package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.FlowSelPlanEntity;
import com.junl.crm_common.pojo.work.SelApprovalFlowEntity;
import com.junl.crm_common.pojo.work.SelPlanEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_work.vo.SelPlanVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
* @description:SelPlanservice接口
* @author: daiqimeng
**/
public interface SelPlanService extends IService<SelPlanEntity> {
    PageEntity queryPage(SelPlanVo selPlanVo);

    FlowSelPlanEntity getPlanInfo(String id);
    boolean  savePlan(SelPlanEntity selPlanEntity);
    boolean  updatePlan(SelPlanEntity selPlanEntity);
    SelPlanEntity getInfo(String id);

    boolean audit(String id,String userId);

    SelApprovalFlowEntity getSchedule(String id);

    double getCount(SelPlanProductDetailEntity selPlanProductDetailEntity, long day);

    SelApprovalFlowEntity getFlow(String id);

    boolean submitBidding(@RequestBody SelPlanVo selPlanVo);

    boolean revocation(@PathVariable("id")String id);

    boolean submitPricing(@RequestBody SelPlanVo selPlanVo);



    List<SelPlanProductDetailEntity> queryProduct(@PathVariable("id")String id);


}