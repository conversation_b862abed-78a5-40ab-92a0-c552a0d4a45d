package com.junl.crm_work.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.SelBiddingFeeRecordEntity;
import com.junl.crm_common.pojo.work.SelBiddingSequenceEntity;

import java.util.List;

/**
* @description:SelBiddingFeeRecordservice接口
* @author: daiqimeng
**/
public interface SelBiddingFeeRecordService extends IService<SelBiddingFeeRecordEntity> {
    //根据竞价id 查询每轮基价信息
    List<SelBiddingSequenceEntity> queryRecords(String id);
}