package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_work.vo.SelInquiryOfferVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SelInquiryOfferservice接口
* @author: daiqimeng
**/
public interface SelInquiryOfferService extends IService<SelInquiryOfferEntity> {
    PageEntity queryPage(SelInquiryOfferVo selInquiryOfferVo);


    Result queryList(SelInquiryOfferVo selInquiryOfferVo, SelCustomerEntity customerEntity);
}