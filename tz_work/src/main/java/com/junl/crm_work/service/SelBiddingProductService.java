package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.SelBiddingProductEntity;
import com.junl.crm_work.vo.SelBiddingProductVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SelBiddingProductservice接口
* @author: daiqimeng
**/
public interface SelBiddingProductService extends IService<SelBiddingProductEntity> {
    PageEntity queryPage(SelBiddingProductVo selBiddingProductVo);


    List<SelBiddingProductEntity> getAppList(SelBiddingProductVo selBiddingProductVo);
}