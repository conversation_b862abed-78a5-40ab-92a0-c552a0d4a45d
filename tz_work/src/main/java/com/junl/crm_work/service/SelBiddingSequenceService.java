package com.junl.crm_work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.work.SelBiddingSequenceEntity;
import com.junl.crm_work.vo.SelBiddingSequenceVo;
import com.junl.crm_common.common.PageEntity;

/**
* @description:SelBiddingSequenceservice接口
* @author: daiqimeng
**/
public interface SelBiddingSequenceService extends IService<SelBiddingSequenceEntity> {
    PageEntity queryPage(SelBiddingSequenceVo selBiddingSequenceVo);

    PageEntity queryAppPage(SelBiddingSequenceVo selBiddingSequenceVo);


    PageEntity queryAppList(SelBiddingSequenceVo selBiddingSequenceVo);
}