package com.junl.crm_work.threadTask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_work.dao.SelInquiryDao;
import com.junl.crm_work.dao.SelInquiryOfferDao;
import com.junl.crm_work.vo.InquiryExcel;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * @description: 询价商品查询以产品为维度
 * @author: daiqimeng
 * @date: 2021/8/2416:13
 */
@AllArgsConstructor
@Log4j2
public class InquiryTaskQuery implements Callable<List<InquiryExcel>> {

    private CountDownLatch countDownLatch;
    private SelPlanProductDetailEntity selPlanProductDetailEntity;
    private SelInquiryDao selInquiryDao;
    private SelInquiryOfferDao selInquiryOfferDao;
    private String inquiryId;


    @Override
    public List<InquiryExcel> call() {
        List<InquiryExcel> list=new ArrayList<>();

        try {
            //查询出所有参与的公司
            List<SelInquiryCustomerEntity> customer = selInquiryDao.getInquiryCustomer(inquiryId);

            //组装数据
            for (SelInquiryCustomerEntity selInquiryCustomerEntity : customer) {
                InquiryExcel inquiryExcel=new InquiryExcel();
                inquiryExcel.setCustomerName(selInquiryCustomerEntity.getCustomerName());
                inquiryExcel.setProductName(selPlanProductDetailEntity.getProductName());
                //报价信息
                SelInquiryOfferEntity selInquiryOfferEntity = selInquiryOfferDao.selectOne(
                        new LambdaQueryWrapper<SelInquiryOfferEntity>()
                        .and(e -> e.eq(SelInquiryOfferEntity::getRelevancyId, selInquiryCustomerEntity.getId())
                                .eq(SelInquiryOfferEntity::getProductId, selPlanProductDetailEntity.getProductId())
                                .eq(SelInquiryOfferEntity::getOfferer, selInquiryCustomerEntity.getCustomerId()))
                );

                if (Assert.notNull(selInquiryOfferEntity)) {
                    inquiryExcel.setCount(selInquiryOfferEntity.getCount());
                    inquiryExcel.setPrice(selInquiryOfferEntity.getPrice());
                    inquiryExcel.setSubmitDate(selInquiryOfferEntity.getCreateTime());
                }else{
                    inquiryExcel.setCount(0.0);
                    inquiryExcel.setPrice(0.0);
                    inquiryExcel.setFlag(Integer.valueOf(Opposite.SINGLE));
                }
                list.add(inquiryExcel);
            }

            if (Assert.notNullCollect(list)) {
                //小计计算
                InquiryExcel inquiryExcel=new InquiryExcel();
                inquiryExcel.setProductName("小计");
                inquiryExcel.setCount(list.stream().mapToDouble(InquiryExcel::getCount).sum());
                inquiryExcel.setSize(1);

                //赋值前端渲染需要的长度
                list.sort((x,y)->-x.getCount().compareTo(y.getCount()));
                list.get(0).setSize(list.size());
                list.add(inquiryExcel);
            }
            countDownLatch.countDown();
        }catch (Exception e){
            log.error("询价组装数组错误:{}",e.getMessage());
            countDownLatch.countDown();
        }
        return list;
    }
}
