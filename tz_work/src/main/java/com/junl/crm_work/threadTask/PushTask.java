package com.junl.crm_work.threadTask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.pojo.work.SelBiddingEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.DateUtils;
import com.junl.crm_common.tools.SpringUtil;
import com.junl.crm_work.service.SelBiddingService;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.status.PushStatus;
import com.junl.crm_work.wx.SendMsgEntity;
import com.junl.crm_work.wx.WxTemplateMsg;
import com.junl.crm_work.wx.WxTemplateMsgDev;
import com.junl.crm_work.wx.WxUtils;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import java.util.List;

/**
 * @description: 推送公众号消息通知
 * @author: daiqimeng
 * @date: 2021/8/417:55
 */

@AllArgsConstructor
@Log4j2
public class PushTask implements Runnable {
    public List<String> customerIds;
    public SelInquiryService selInquiryService;
    public SelBiddingService selBiddingService;
    public SelCustomerService selCustomerService;
    public WxUtils wxUtils;
    public PushStatus pushStatus;
    public String busId;

    @Override
    public void run() {
        try {
            List<SelCustomerEntity> customers = selCustomerService.list(
                    new LambdaQueryWrapper<SelCustomerEntity>()
                            .eq(SelCustomerEntity::getIsWxAuth, Opposite.SINGLE)
                    .in(SelCustomerEntity::getId, customerIds)
            );
            if (Assert.notNullCollect(customers)) {
                switch (pushStatus){
                    //竞价方面的
                    case BIDDING:
                    case SUCCESS:
                    case OVER_BIDDING:
                    case START_BIDDING:
                        //获取业务对象
                        SelBiddingEntity bidding = selBiddingService.getById(busId);
                        //发送消息
                        customers.forEach(x->{
                            SendMsgEntity biddingMsg = biddingSendMsgEntity(pushStatus,bidding);
                            biddingMsg.setUser(x.getOpenId());
                            String msg3 = SpringUtil.getProperties("wx.template_msg3");

                            if (SpringUtil.isProd()) {
                                WxTemplateMsg assembly = wxUtils.assembly(biddingMsg);
                                assembly.setTemplate_id(msg3);
                                wxUtils.sendPost(assembly);
                            }else {
                                WxTemplateMsgDev wxTemplateMsgDev = wxUtils.assemblyDev(biddingMsg);
                                wxTemplateMsgDev.setTemplate_id(msg3);
                                wxUtils.sendPostDev(wxTemplateMsgDev);
                            }

                        });

                        break;
                    //询价方面的
                    case INQUIRE:
                    case OVER_INQUIRE:
                    case START_INQUIRE:
                        //获取业务对象
                        SelInquiryEntity inquiry = selInquiryService.getById(busId);
                        //发送消息
                        customers.forEach(x->{
                            SendMsgEntity inquireMsg = inquireSendMsgEntity(pushStatus,inquiry);
                            String msg2 = SpringUtil.getProperties("wx.template_msg2");
                            inquireMsg.setUser(x.getOpenId());
                            if (SpringUtil.isProd()) {
                                WxTemplateMsg assembly = wxUtils.assembly(inquireMsg);
                                assembly.setTemplate_id(msg2);
                                wxUtils.sendPost(assembly);
                            }else {
                                WxTemplateMsgDev wxTemplateMsgDev = wxUtils.assemblyDev(inquireMsg);
                                wxTemplateMsgDev.setTemplate_id(msg2);
                                wxUtils.sendPostDev(wxTemplateMsgDev);
                            }
                        });
                        break;
                    default:
                        log.warn("未知的业务类型,无法推送消息");
                }
            }
        }catch (Exception e){
            log.error("推送公众号信息错误:"+e.getMessage());
        }

    }


    //获取消息体
    private SendMsgEntity inquireSendMsgEntity(PushStatus pushStatus,SelInquiryEntity inquiry){
        SendMsgEntity sendMsgEntity=new SendMsgEntity();
        switch (pushStatus){
            case INQUIRE:
                sendMsgEntity.setTitle("诚邀你参加中海泰州石化第"+inquiry.getPeriodNum()+"期"+inquiry.getInquiryName()+"销售计划的商品询价.");
                sendMsgEntity.setWord2("无");
                sendMsgEntity.setWord1("未开始");
                sendMsgEntity.setWord3(DateUtils.format(inquiry.getInquiryStartDate(),"yyyy-MM-dd HH:mm:ss")+"\n"+DateUtils.format(inquiry.getInquiryEndTime(),"yyyy-MM-dd HH:mm:ss"));
                break;
            case OVER_INQUIRE:
                sendMsgEntity.setTitle("中海泰州石化第"+inquiry.getPeriodNum()+"期"+inquiry.getInquiryName()+"销售计划的商品询价已提前结束.");
                sendMsgEntity.setWord2("无");
                sendMsgEntity.setWord1("已结束");
                sendMsgEntity.setWord3(DateUtils.format(inquiry.getInquiryEndTime(),"yyyy-MM-dd HH:mm:ss"));
                break;
            case START_INQUIRE:
                sendMsgEntity.setTitle("中海泰州石化第"+inquiry.getPeriodNum()+"期"+inquiry.getInquiryName()+"销售计划的商品询价已提前开始.");
                sendMsgEntity.setWord2("无");
                sendMsgEntity.setWord1("已开始");
                sendMsgEntity.setWord3(DateUtils.format(inquiry.getInquiryStartDate(),"yyyy-MM-dd HH:mm:ss"));
                break;
        }
        sendMsgEntity.setRemark("更多详情请进入小程序查看.");

        return sendMsgEntity;
    }

    //获取消息体
    private SendMsgEntity biddingSendMsgEntity(PushStatus pushStatus,SelBiddingEntity bidding){
        SendMsgEntity sendMsgEntity=new SendMsgEntity();
        switch (pushStatus){
            case BIDDING:
                sendMsgEntity.setTitle("邀请竞价");
                sendMsgEntity.setWord2("未开始");
                sendMsgEntity.setWord1("诚邀您参加中海泰州石化第"+bidding.getPeriodNum()+"期"+bidding.getPlanName()+"的第"+bidding.getRoundNum()+"轮销售计划商品竞价.");
                sendMsgEntity.setRemark("开始时间："+DateUtils.format(bidding.getPlanStartDate(),"yyyy-MM-dd HH:mm:ss")+"\n结束时间："+DateUtils.format(bidding.getPlanEndTime(),"yyyy-MM-dd HH:mm:ss")+"\n更多详情请进入小程序查看.");

                break;
            case SUCCESS:
                sendMsgEntity.setTitle("交易成功");
                sendMsgEntity.setWord2("竞拍成功");
                sendMsgEntity.setWord1("您参加中海泰州石化第"+bidding.getPeriodNum()+"期"+bidding.getPlanName()+"的第"+bidding.getRoundNum()+"轮销售计划商品竞价竞拍成功.");
                sendMsgEntity.setRemark("时间："+DateUtils.format(DateUtils.getNow(),"yyyy-MM-dd HH:mm:ss")+"\n更多详情请进入小程序查看.");
                break;
            case OVER_BIDDING:
                sendMsgEntity.setTitle("结束通知");
                sendMsgEntity.setWord2("已结束");
                sendMsgEntity.setWord1("您参加的中海泰州石化第"+bidding.getPeriodNum()+"期"+bidding.getPlanName()+"的第"+bidding.getRoundNum()+"轮销售计划商品竞价已提前结束");
                sendMsgEntity.setRemark("时间："+DateUtils.format(bidding.getPlanEndTime(),"yyyy-MM-dd HH:mm:ss")+"\n更多详情请进入小程序查看.");
                break;
            case START_BIDDING:
                sendMsgEntity.setTitle("开始通知");
                sendMsgEntity.setWord2("已开始");
                sendMsgEntity.setWord1("您参加的中海泰州石化第"+bidding.getPeriodNum()+"期"+bidding.getPlanName()+"的第"+bidding.getRoundNum()+"轮销售计划商品竞价已提前开始");
                sendMsgEntity.setRemark("时间："+DateUtils.format(bidding.getPlanStartDate(),"yyyy-MM-dd HH:mm:ss")+"\n更多详情请进入小程序查看.");
                break;

        }
        return sendMsgEntity;
    }

}
