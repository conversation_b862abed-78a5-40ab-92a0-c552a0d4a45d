package com.junl.crm_work.threadTask;

import com.alibaba.fastjson.JSONObject;
import com.junl.crm_common.pojo.work.FlowSelPlanEntity;
import com.junl.crm_common.pojo.work.FlowSelPlanProductDetailEntity;
import com.junl.crm_common.pojo.work.SelPlanEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.FlowSelPlanProductDetailService;
import com.junl.crm_work.service.FlowSelPlanService;
import com.junl.crm_work.service.SelPlanService;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;

import java.util.List;


/**
 * 记录销售计划审批流程的详情
 */
@AllArgsConstructor
@Log4j2
public class FlowPlanTask implements Runnable {

    private SelPlanService selPlanService;
    private FlowSelPlanService flowSelPlanService;
    private FlowSelPlanProductDetailService flowSelPlanProductDetailService;
    //审批id
    private String underwayId;
    //计划id
    private String planId;

    @Override
    public void run() {
        try {
            SelPlanEntity info = selPlanService.getInfo(planId);
            if (Assert.notNull(info)) {
                //处理销售计划数据
                FlowSelPlanEntity flowSelPlanEntity=new FlowSelPlanEntity();
                BeanUtils.copyProperties(info,flowSelPlanEntity);
                flowSelPlanEntity.setUnderwayId(underwayId);
                flowSelPlanEntity.setId(SnowFlake.getUUId());
                flowSelPlanEntity.setPlanId(info.getId());
                flowSelPlanService.save(flowSelPlanEntity);
                //处理销售产品计划详情
                List<SelPlanProductDetailEntity> productList = info.getProductList();

                if (Assert.notNullCollect(productList)) {
                    productList.forEach(x->{
                        FlowSelPlanProductDetailEntity f=new FlowSelPlanProductDetailEntity();
                        BeanUtils.copyProperties(x,f);
                        f.setId(SnowFlake.getUUId());
                        f.setPlanId(flowSelPlanEntity.getId());
                        flowSelPlanProductDetailService.save(f);
                    });
                }
            }

        }catch (Exception e){
            log.error("销售计划审批记录错误：{}",e.getMessage());
        }
    }
}
