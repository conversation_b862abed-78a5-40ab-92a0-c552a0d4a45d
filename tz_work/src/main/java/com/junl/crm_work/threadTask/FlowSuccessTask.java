package com.junl.crm_work.threadTask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 竞价成功审批记录详情
 */
@AllArgsConstructor
@Log4j2
public class FlowSuccessTask implements Runnable {
    private SelBiddingService selBiddingService;
    private SelBiddingSuccessService selBiddingSuccessService;
    private SelBiddingSuccessProductService selBiddingSuccessProductService;
    private String biddingId;
    private String sequenceId;
    private String underwayId;
    private FlowSelBiddingSuccessService flowSelBiddingSuccessService;
    private FlowSelBiddingSuccessProductService flowSelBiddingSuccessProductService;
    private FlowSelBiddingService flowSelBiddingService;
    private CountDownLatch countDownLatch;
    @Override
    public void run() {

        try {
            //处理流程主数据
            SelBiddingEntity byId = selBiddingService.getById(biddingId);
            FlowSelBiddingEntity biddingEntity=new FlowSelBiddingEntity();
            BeanUtils.copyProperties(byId,biddingEntity);
            biddingEntity.setId(SnowFlake.getUUId());
            biddingEntity.setUnderwayId(underwayId);
            biddingEntity.setBiddingId(biddingId);
            biddingEntity.setRoundId(sequenceId);
            flowSelBiddingService.save(biddingEntity);

            //查询出交易成功的信息 并处理
            List<SelBiddingSuccessEntity> list = selBiddingSuccessService.list(
                    new LambdaQueryWrapper<SelBiddingSuccessEntity>()
                    .and(e -> e.eq(SelBiddingSuccessEntity::getBiddingId, biddingId)
                            .eq(SelBiddingSuccessEntity::getSequenceId, sequenceId))
            );

            if (Assert.notNull(list)) {
                list.forEach(x->{
                    //处理主体数据
                    FlowSelBiddingSuccessEntity f=new FlowSelBiddingSuccessEntity();
                    BeanUtils.copyProperties(x,f);
                    f.setUnderwayId(underwayId);
                    f.setBiddingId(biddingEntity.getId());
                    f.setId(SnowFlake.getUUId());
                    flowSelBiddingSuccessService.save(f);

                    //处理商品
                    List<FlowSelBiddingSuccessProductEntity> temp=new ArrayList<>();
                    List<SelBiddingSuccessProductEntity> product = selBiddingSuccessProductService.list(
                            new LambdaQueryWrapper<SelBiddingSuccessProductEntity>()
                            .eq(SelBiddingSuccessProductEntity::getSuccessId, x.getId())
                    );

                    product.forEach(e->{
                        FlowSelBiddingSuccessProductEntity successProduct=new FlowSelBiddingSuccessProductEntity();
                        BeanUtils.copyProperties(e,successProduct);
                        successProduct.setId(SnowFlake.getUUId());
                        successProduct.setSuccessId(f.getId());
                        temp.add(successProduct);
                    });
                    flowSelBiddingSuccessProductService.saveBatch(temp);

                });
            }
            countDownLatch.countDown();
        }catch (Exception e){
            log.error("交易成功审批记录错误: {}",e.getMessage());
            countDownLatch.countDown();
        }

    }
}
