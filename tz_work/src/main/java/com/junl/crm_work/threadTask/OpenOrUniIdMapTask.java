package com.junl.crm_work.threadTask;

import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_work.wx.WxUtils;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR> Dai
 * @date 2024/4/23 17:38
 */

@Component
@AllArgsConstructor
@Log4j2
public class OpenOrUniIdMapTask {

    private final WxUtils wxUtils;

    private final RedisUtils redisUtils;
    @Scheduled(cron = "0 */5 * * * ?")
    public void run(){
        try {
            List<String> userOpenIds = wxUtils.getUserOpenIds();

            if (redisUtils.hasKey(RedisKey.OPEN_ID.getName())) {
                Set<String> setValue = redisUtils.hGetAll(RedisKey.OPEN_ID.getName());
                List<String> collect = userOpenIds.stream()
                        .filter(x -> !setValue.contains(x))
                        .collect(Collectors.toList());
                userOpenIds = collect;
            }

            if (!CollectionUtils.isEmpty(userOpenIds)) {
                int skip = 0;
                while (skip<userOpenIds.size()){
                    List<String> openIds = userOpenIds.stream()
                            .skip(skip)
                            .limit(100)
                            .collect(Collectors.toList());
                    //微信限制 一次最多查询100个
                    skip+=100;
                    List<WxUtils.WxUser> unionIdAll = wxUtils.getUnionIdAll(openIds);
                    for (WxUtils.WxUser wxUser : unionIdAll) {
                        redisUtils.hSet(
                                RedisKey.OPEN_ID.getName(),
                                wxUser.getUnionid(),
                                wxUser.getOpenid()
                        );
                    }
                }
            }

        }catch (Exception e){
            log.error("映射Openid 和 uniId 错误");
        }


    }

}
