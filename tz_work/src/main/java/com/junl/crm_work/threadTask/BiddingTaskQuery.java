package com.junl.crm_work.threadTask;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_work.dao.SelBiddingDao;
import com.junl.crm_work.service.SelBiddingCustomerOfferService;
import com.junl.crm_work.service.SelBiddingCustomerService;
import com.junl.crm_work.service.SelBiddingService;
import com.junl.crm_work.vo.BiddingExcel;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * @description: 竞价商品上传 按产品维度
 * @author: daiqimeng
 * @date: 2021/8/2416:42
 */
@AllArgsConstructor
@Log4j2
public class BiddingTaskQuery implements Callable<List<BiddingExcel>> {
    private CountDownLatch countDownLatch;
    private SelBiddingSequenceEntity sequenceEntity;
    private SelBiddingProductEntity selBiddingProductEntity;
    private SelBiddingDao selBiddingDao;
    private SelBiddingCustomerOfferService selBiddingCustomerOfferService;

    @Override
    public List<BiddingExcel> call(){
        List<BiddingExcel> list=new ArrayList<>();

        try {
            //查询出邀请竞价的所有公司
            List<SelBiddingCustomerEntity> customer = selBiddingDao.getBiddingCustomer(sequenceEntity.getId());

            for (SelBiddingCustomerEntity selBiddingCustomerEntity : customer) {
                BiddingExcel biddingExcel=new BiddingExcel();
                biddingExcel.setProductName(selBiddingProductEntity.getProductName());
                biddingExcel.setCustomerName(selBiddingCustomerEntity.getCustomerName());

                SelBiddingCustomerOfferEntity customerOffer = selBiddingCustomerOfferService.getOne(
                        new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                        .and(e -> e.eq(SelBiddingCustomerOfferEntity::getRelevanceId, selBiddingCustomerEntity.getId())
                                .eq(SelBiddingCustomerOfferEntity::getOfferer, selBiddingCustomerEntity.getCustomerId())
                                .eq(SelBiddingCustomerOfferEntity::getProductId, selBiddingProductEntity.getProductId())));

                if (Assert.notNull(customerOffer)) {
                    biddingExcel.setPrice(customerOffer.getPrice().doubleValue());
                    biddingExcel.setCount(customerOffer.getCount());
                    biddingExcel.setSubmitDate(customerOffer.getCreateTime());
                }else{
                    biddingExcel.setFlag(Integer.valueOf(Opposite.SINGLE));
                    biddingExcel.setPrice(0.0);
                    biddingExcel.setCount(0.0);
                }
                list.add(biddingExcel);
            }

            if (Assert.notNullCollect(list)) {
                list.sort((x,y)->-x.getCount().compareTo(y.getCount()));
                list.get(0).setSize(list.size());

                BiddingExcel biddingExcel=new BiddingExcel();
                biddingExcel.setProductName("小计");
                biddingExcel.setCount(list.stream().mapToDouble(BiddingExcel::getCount).sum());
                biddingExcel.setSize(1);
                list.add(biddingExcel);
            }
            countDownLatch.countDown();
        }catch (Exception e){
            log.error("竞价组装数据错误：{}",e.getMessage());
            countDownLatch.countDown();
        }
        return list;
    }
}
