package com.junl.crm_work.threadTask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 竞价审批详情记录
 */
@AllArgsConstructor
@Log4j2
public class FlowBiddingTask implements Runnable {
    private SelBiddingService selBiddingService;
    private SelBiddingSequenceService selBiddingSequenceService;
    private SelBiddingCustomerService customerService;
    private SelBiddingProductService selBiddingProductService;

    private String underwayId;
    private String biddingId;
    private FlowSelBiddingService flowSelBiddingService;
    private FlowSelBiddingCustomerService flowSelBiddingCustomerService;
    private FlowSelBiddingProductService flowSelBiddingProductService;
    private CountDownLatch countDownLatch;



    @Override
    public void run() {
        try {
            //查询竞价
            SelBiddingEntity bidding = selBiddingService.getById(biddingId);
            //查询轮次
            SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                    new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                    .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, bidding.getId())
                            .eq(SelBiddingSequenceEntity::getSequence, bidding.getRoundNum()))
            );
            //根据轮次查询参与的商家
            List<SelBiddingCustomerEntity> customers = customerService.list(
                    new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                            .eq(SelBiddingCustomerEntity::getSequence, sequence.getId())
            );
            //根据轮次查询关联的商品
            List<SelBiddingProductEntity> products = selBiddingProductService.list(
                    new LambdaQueryWrapper<SelBiddingProductEntity>()
                            .eq(SelBiddingProductEntity::getSequenceId,
                                    sequence.getId())
            );

            //处理商家
            List<FlowSelBiddingCustomerEntity> flowCustomer=new ArrayList<>();
            customers.forEach(x->{
                FlowSelBiddingCustomerEntity f=new FlowSelBiddingCustomerEntity();
                BeanUtils.copyProperties(x,f);
                f.setId(SnowFlake.getUUId());
                flowCustomer.add(f);
            });
            //处理商品
            List<FlowSelBiddingProductEntity> flowProduct=new ArrayList<>();
            products.forEach(x->{
                FlowSelBiddingProductEntity f=new FlowSelBiddingProductEntity();
                BeanUtils.copyProperties(x,f);
                f.setId(SnowFlake.getUUId());
                flowProduct.add(f);
            });

            //组装竞价审批主数据详情
            FlowSelBiddingEntity flowSelBiddingEntity=new FlowSelBiddingEntity();
            BeanUtils.copyProperties(bidding,flowSelBiddingEntity);
            flowSelBiddingEntity.setId(SnowFlake.getUUId());
            flowSelBiddingEntity.setUnderwayId(underwayId);
            flowSelBiddingEntity.setRoundId(sequence.getId());
            flowSelBiddingEntity.setBiddingId(biddingId);

            flowSelBiddingService.save(flowSelBiddingEntity);
            flowSelBiddingCustomerService.saveBatch(flowCustomer);
            flowSelBiddingProductService.saveBatch(flowProduct);
            countDownLatch.countDown();
        }catch (Exception e){
            countDownLatch.countDown();
            log.error("竞价审批记录流程错误：{}",e.getMessage());
        }
    }
}
