package com.junl.crm_work.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_work.listener.SmsListener;
import com.junl.crm_work.service.*;
import com.junl.crm_work.status.BiddingStatus;
import com.junl.crm_work.status.BusCode;
import com.junl.crm_work.status.PlanModel;
import com.junl.crm_work.vo.*;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.msg.common.MsgType;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sell/selBidding")
@Api(tags = "销售竞价接口")
public class SelBiddingController extends ParentController {

    @Autowired
    private SelBiddingService selBiddingService;

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;

    @Autowired
    private SmsListener smsListener;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "销售竞价表--分页列表查询",notes="销售竞价表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelBiddingVo selBiddingVo){
        selBiddingVo.setUserId(getUserId());
        selBiddingVo.setCompanyId(getCompanyId());
        PageEntity page = selBiddingService.queryPage(selBiddingVo);
        return Result.success(page);
    }


    @ApiOperation("查询每轮竞价情况")
    @PostMapping("/queryBiddingPage")
    public Result queryBiddingPage(@RequestBody SelBiddingVo selBiddingVo){
        PageEntity pageEntity = selBiddingService.queryBiddingPage(selBiddingVo);
        return Result.success(pageEntity);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "销售竞价查询详情",notes="销售竞价查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selBiddingService.info(id));
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售竞价--删除", notes = "销售竞价--删除")
    @GetMapping("/delete/{id}")
    public Result delete(@PathVariable("id") String ids){
        selBiddingService.deleteById(ids);
        return Result.success();
    }


    @ApiOperation("再次竞价")
    @PostMapping("/again")
    public Result again(@RequestBody SelInquiryVo selInquiryVo){
        Validate.startValidate(new NotNullVerification(selInquiryVo,null));
        selInquiryVo.setUserId(getUserId());
        selBiddingService.again(selInquiryVo);
        return Result.success();
    }

    @ApiOperation("再次发起交易成功审批(只有定价模式被驳回或撤销的时候)")
    @PostMapping("/successPricing")
    public Result successPricing(@RequestBody SelPlanVo selPlanVo){
        selPlanVo.setUserId(getUserId());
        selBiddingService.successPricing(selPlanVo);
        return Result.success();
    }

    @ApiOperation("查询竞价报价信息")
    @GetMapping("/getPricing/{id}")
    public Result getPricing(@PathVariable("id")String id){
        return Result.success(selBiddingService.getPricing(id));
    }


    @ApiModelProperty("竞价审核失败再次提交审核")
    @PostMapping("/againSubmitBidding")
    public Result againSubmitBidding(@RequestBody SelBiddingEntity selBiddingVo){
        selBiddingVo.setUpdateBy(getUserId());
        selBiddingService.againSubmit(selBiddingVo);
        return Result.success();
    }


    @ApiOperation("查询基价调整记录")
    @GetMapping("/queryFee/{id}")
    public Result queryFee(@PathVariable("id")String id){
        return Result.success(selBiddingService.queryFee(id));
    }



    @ApiOperation("交易完成审核失败再次提交")
    @PostMapping("/againSubmitSuccess")
    public Result againSubmitSuccess(@RequestBody SelBiddingVo selBiddingVo){
        selBiddingVo.setUserId(getUserId());
        selBiddingService.againSubmitSuccess(selBiddingVo);
        return Result.success();
    }

    @ApiOperation("/交易完成审核")
    @PostMapping("/success")
    public Result success(@RequestBody SelBiddingVo selBiddingVo){
        selBiddingVo.setUserId(getUserId());
        selBiddingService.success(selBiddingVo);
        return Result.success();
    }

    @ApiOperation("/查询成功交易信息")
    @PostMapping("/getSuccessInfo")
    public Result getSuccessInfo(@RequestBody SelBiddingVo selBiddingVo){
        SelBiddingEntity successInfo = selBiddingService.getSuccessInfo(selBiddingVo);
        return Result.success(successInfo);
    }

    @ApiOperation("竞价提前开始")
    @GetMapping("/biddingStart/{id}")
    public Result biddingStart(@PathVariable("id")String id){

        SelBiddingEntity byId = selBiddingService.getById(id);
        Assert.notNull(byId,"查询不到该主键信息");

        if (!byId.getIsDelete().equals(0)) {
            throw new RuntimeException("竞价已废弃,请刷新后操作");
        }

        if (BiddingStatus.BIDDING.getCode().equals(byId.getBiddingStatus())) {
            throw new RuntimeException("该竞价已开始,请勿重复提交.");
        }

        Date date = new Date();
        SelBiddingEntity selBiddingEntity=new SelBiddingEntity();
        selBiddingEntity.setId(id);
        selBiddingEntity.setBiddingStatus(BiddingStatus.BIDDING.getCode());
        selBiddingEntity.setPlanStartDate(date);
        selBiddingService.updateById(selBiddingEntity);

        //更改对应轮次的开始时间
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                        .eq(SelBiddingSequenceEntity::getSequence, byId.getRoundNum())));

        sequenceEntity.setPlanStartDate(date);
        sequenceEntity.setBiddingStatus(BiddingStatus.BIDDING.getCode());
        selBiddingSequenceService.updateById(sequenceEntity);

        redisUtils.delete(RedisKey.BIDDING.getName()+id);
        long l = DateUtils.compareSecond(DateUtils.getNow(), byId.getPlanEndTime());
        redisUtils.set(RedisKey.BIDDING.getName()+id,Opposite.SINGLE,l);


        List<SelBiddingCustomerEntity> list = selBiddingCustomerService.list(
                new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                        .eq(SelBiddingCustomerEntity::getSequence, sequenceEntity.getId())
        );
//        list.forEach(x->{
//            SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//            sysUserMsgEntity.setMsgType(MsgType.USER.getCode());
//            sysUserMsgEntity.setMsgTitle("竞价提前开始通知");
//            sysUserMsgEntity.setMsgContent("邀请您参加的"+byId.getPlanName()+"的第"+sequenceEntity.getRoundNum()+"轮商品竞价已提前开始.");
//            sysUserMsgEntity.setBusCode(BusCode.START_BIDDING.getCode());
//            sysUserMsgEntity.setMsgAccessory(id);
//            sysUserMsgEntity.setCompanyId(byId.getCompanyId());
//            sysUserMsgEntity.setReader(x.getCustomerId());
//        });
        //发送短信通知
        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
        sysUserMsgEntity.setMsgType(MsgType.SMS.getCode());
        sysUserMsgEntity.setBusCode(BusCode.START_BIDDING.getCode());
        sysUserMsgEntity.setMsgAccessory(CommonUtil.collect(list,SelBiddingCustomerEntity::getCustomerId,","));
        sysUserMsgEntity.setMsgContent(byId.getId());
        sysUserMsgEntity.setCompanyId(byId.getCompanyId());
        smsListener.dispose(sysUserMsgEntity);
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.SMS.getKey());


        return Result.success();
    }


    @ApiOperation("竞价提前结束")
    @GetMapping("/biddingEnd/{id}")
    public Result biddingEnd(@PathVariable("id")String id){
        SelBiddingEntity byId = selBiddingService.getById(id);
        Assert.notNull(byId,"查询不到该主键信息");

        if (!byId.getIsDelete().equals(0)) {
            throw new RuntimeException("竞价已废弃,请刷新后操作");
        }

        if (BiddingStatus.BIDDING_OVER.getCode().equals(byId.getBiddingStatus())) {
            throw new RuntimeException("该竞价已结束,请勿重复提交");
        }

        Date date = new Date();
        SelBiddingEntity selBiddingEntity=new SelBiddingEntity();
        selBiddingEntity.setId(id);
        selBiddingEntity.setBiddingStatus(BiddingStatus.BIDDING_OVER.getCode());
        selBiddingEntity.setPlanEndTime(date);
        selBiddingService.updateById(selBiddingEntity);


        //更改对应轮次的开始时间
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                                .eq(SelBiddingSequenceEntity::getSequence, byId.getRoundNum())));

        sequenceEntity.setPlanEndTime(date);
        sequenceEntity.setBiddingStatus(BiddingStatus.BIDDING_OVER.getCode());
        selBiddingSequenceService.updateById(sequenceEntity);

        redisUtils.delete(RedisKey.BIDDING.getName()+id);



        List<SelBiddingCustomerEntity> list = selBiddingCustomerService.list(
                new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                        .eq(SelBiddingCustomerEntity::getSequence, sequenceEntity.getId())
        );
//        list.forEach(x->{
//            SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//            sysUserMsgEntity.setMsgType(MsgType.USER.getCode());
//            sysUserMsgEntity.setMsgTitle("竞价提前结束通知");
//            sysUserMsgEntity.setMsgContent("邀请您参加的"+byId.getPlanName()+"的第"+sequenceEntity.getRoundNum()+"轮商品竞价已提前结束.");
//            sysUserMsgEntity.setBusCode(BusCode.OVER_BIDDING.getCode());
//            sysUserMsgEntity.setMsgAccessory(id);
//            sysUserMsgEntity.setCompanyId(byId.getCompanyId());
//            sysUserMsgEntity.setReader(x.getCustomerId());
//        });

        //发送短信通知
        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
        sysUserMsgEntity.setMsgType(MsgType.SMS.getCode());
        sysUserMsgEntity.setBusCode(BusCode.OVER_BIDDING.getCode());
        sysUserMsgEntity.setMsgAccessory(CommonUtil.collect(list,SelBiddingCustomerEntity::getCustomerId,","));
        sysUserMsgEntity.setMsgContent(byId.getId());
        sysUserMsgEntity.setCompanyId(byId.getCompanyId());
        smsListener.dispose(sysUserMsgEntity);
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.SMS.getKey());

        redisUtils.delete(RedisKey.BIDDING_NOTICE.getName()+id);

        return Result.success();
    }

    @ApiOperation("根据流程id查询竞价审批记录")
    @GetMapping("/getFlowBiddingInfo/{id}")
    private Result getFlowBiddingInfo(@PathVariable("id")String id){
        FlowSelBiddingEntity flowSuccessInfo = selBiddingService.getFlowBiddingInfo(id);
        return Result.success(flowSuccessInfo);
    }



    @ApiOperation("根据流程id 查询竞价成功的审批记录")
    @GetMapping("/getFlowSuccessInfo/{id}")
    private Result getFlowSuccessInfo(@PathVariable("id")String id){
        return Result.success(selBiddingService.getFlowSuccessInfo(id));
    }


    @ApiOperation("导出竞价excel信息")
    @PostMapping("/exportExcel")
    public Result exportExcel(@RequestBody SelBiddingVo selBiddingVo){
        Map<String, String> map = selBiddingService.exportExcel(selBiddingVo);
        return Result.success(map);
    }

    @ApiOperation("导出交易成功excel")
    @GetMapping("/exportSuccessExcel/{id}")
    public Result exportSuccessExcel(@PathVariable("id")String id){
        Map<String, String> map = selBiddingService.exportSuccessExcel(id);
        return Result.success(map);
    }

    @ApiOperation("根据竞价Id和公司Id 查询该公司的对应销售计划的报价情况")
    @PostMapping("/queryInquiry")
    public Result queryInquiry(@RequestBody SelBiddingVo selBiddingVo){
        return Result.success(selBiddingService.queryInquiry(selBiddingVo));
    }

    @ApiOperation("查询交易成功的信息(数据组装)")
    @GetMapping("/querySuccessInfo/{id}")
    public Result querySuccessInfo(@PathVariable("id")String id){
        SelBiddingEntity selBiddingEntity = selBiddingService.querySuccessInfo(id);
        return Result.success(selBiddingEntity);
    }

    @ApiOperation("查询当前竞价的公司报价信息 (数据组装)")
    @PostMapping("/queryBiddingInfo")
    public Result queryBiddingInfo(@RequestBody SelBiddingVo selBiddingVo){
        return Result.success(selBiddingService.queryBiddingInfo(selBiddingVo));
    }

    @ApiOperation("获取报过价的客户信息")
    @GetMapping("/filter/{id}")
    public Result filter(@PathVariable("id")String id){
        return Result.success(selBiddingService.filter(id));
    }


    @ApiOperation("获取指定期数报表")
    @PostMapping("/getPlanReport")
    public Result getPlanReport(@RequestBody ReportPage reportPage){
        Validate.startValidate(new NotNullVerification(reportPage,null));
        return Result.success(selBiddingService.getPlanReport(reportPage));
    }

    @ApiOperation("导出指定期数报表")
    @PostMapping("/exportReport")
    public Result exportReport(@RequestBody ReportPage reportPage){
        Validate.startValidate(new NotNullVerification(reportPage,null));
        return Result.success(selBiddingService.exportReport(reportPage));
    }

    @ApiOperation("根据年份 查询该年的期数")
    @PostMapping("/getListTime")
    public Result getListTime(@RequestBody ReportPage reportPage){
        reportPage.setCompanyId(getCompanyId());
        return Result.success(selBiddingService.getListTime(reportPage));
    }

    @ApiOperation("撤回竞价审批申请")
    @GetMapping("/biddingRevocation/{id}")
    public Result biddingRevocation(@PathVariable("id")String id){
        selBiddingService.biddingRevocation(id);
        return Result.success();
    }

    @ApiOperation("撤回交易成功审批申请")
    @GetMapping("/successRevocation/{id}")
    public Result successRevocation(@PathVariable("id")String id){
        selBiddingService.successRevocation(id);
        return Result.success();
    }

    @ApiOperation("开始竞价(用于撤销后再次竞价)")
    @PostMapping("/submitBidding")
    public Result submitBidding(@RequestBody SelPlanVo selPlanVo){
        selPlanVo.setUserId(getUserId());
        selBiddingService.submitBidding(selPlanVo);
        return Result.success();
    }

    @ApiOperation("再次竞价查询上一轮已报过价的客户")
    @GetMapping("/getCustomerAll/{id}")
    public Result getCustomerAll(@PathVariable("id")String id){
        return Result.success(selBiddingService.getCustomerAll(id));
    }

    @ApiOperation("查询以产品维度的企业活跃度，成交率等报表")
    @PostMapping("/queryLivenessAndCloseRateReport")
    public Result queryLivenessAndCloseRateReport(@RequestBody QuarterVo quarterVo){
        Validate.startValidate(new NotNullVerification(quarterVo,null));
        if(quarterVo.getModeType()!=PlanModel.BIDDING_AND_AMOUNT.getCode()){
            return Result.error("该报表目前只支持竞价竟量模式");
        }
        quarterVo.setCompanyId(getCompanyId());
        return Result.success(selBiddingService.queryLivenessAndCloseRateReport(quarterVo));
    }

    @ApiOperation("导出以产品维度的企业活跃度，成交率等报表")
    @PostMapping("/exportLivenessAndCloseRateReport")
    public Result exportLivenessAndCloseRateReport(@RequestBody QuarterVo quarterVo){
        Validate.startValidate(new NotNullVerification(quarterVo,null));
        if(quarterVo.getModeType()!=PlanModel.BIDDING_AND_AMOUNT.getCode()){
            return Result.error("该报表目前只支持竞价竟量模式");
        }
        quarterVo.setCompanyId(getCompanyId());
        return Result.success(selBiddingService.exportLivenessAndCloseRateReport(quarterVo));
    }



}