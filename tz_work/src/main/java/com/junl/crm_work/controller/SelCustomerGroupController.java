package com.junl.crm_work.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.work.SelCustomerGroupEntity;
import com.junl.crm_common.pojo.work.SelGroupRelevanceEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.SelCustomerGroupService;
import com.junl.crm_work.service.SelGroupRelevanceService;
import com.junl.crm_work.vo.SelCustomerGroupVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sell/selCustomerGroup")
@Api(tags = "企业分组接口")
public class SelCustomerGroupController extends ParentController {

    @Autowired
    private SelCustomerGroupService selCustomerGroupService;

    @Autowired
    private SelGroupRelevanceService selGroupRelevanceService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "企业分组表--分页列表查询",notes="企业分组表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelCustomerGroupVo selCustomerGroupVo){
        selCustomerGroupVo.setCompanyId(getCompanyId());
        selCustomerGroupVo.setUserId(getUserId());
        PageEntity page = selCustomerGroupService.queryPage(selCustomerGroupVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "企业分组表--列表查询",notes="企业分组表--列表查询")
    @GetMapping("/getList")
    public Result getList(){
        List<SelCustomerGroupEntity> list = selCustomerGroupService.list(new QueryWrapper<SelCustomerGroupEntity>().select("id","group_name")
                .and(e->e.eq("create_by",getUserId())
                        .eq("company_id",getCompanyId())));
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "企业分组查询详情",notes="企业分组查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selCustomerGroupService.getInfo(id));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "企业分组列表--新增", notes = "企业分组列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SelCustomerGroupEntity selCustomerGroupEntity){
        selCustomerGroupEntity.setId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(selCustomerGroupEntity,null));
        RuleTools.setField(selCustomerGroupEntity,getSysUserEntity(),true);
        selCustomerGroupService.save(selCustomerGroupEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "企业分组--修改", notes = "企业分组--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SelCustomerGroupEntity selCustomerGroupEntity){
        Validate.startValidate(new NotNullVerification(selCustomerGroupEntity,null));
        RuleTools.setField(selCustomerGroupEntity,getSysUserEntity(),true);
        selCustomerGroupService.updateById(selCustomerGroupEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "企业分组--删除", notes = "企业分组--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] ids){
        selCustomerGroupService.removeByIds(Arrays.asList(ids));
        for (String id : ids) {
            selGroupRelevanceService.remove(new LambdaQueryWrapper<SelGroupRelevanceEntity>().eq(SelGroupRelevanceEntity::getGroupId,id));
        }
        return Result.success();
    }

    @ApiOperation("保存组内关系")
    @PostMapping("/saveGroup")
    public Result saveGroup(@RequestBody SelCustomerGroupEntity selCustomerGroupEntity){
        selCustomerGroupService.saveGroup(selCustomerGroupEntity);
        return Result.success();
    }


    @ApiOperation("根据分组Id获取所有分组企业")
    @GetMapping("/queryGroupCustomer/{id}")
    public Result queryGroupCustomer(@PathVariable("id")String id){
        List<SelGroupRelevanceEntity> selGroupRelevanceEntities = selCustomerGroupService.queryGroupCustomer(id);
        return Result.success(selGroupRelevanceEntities);
    }




}