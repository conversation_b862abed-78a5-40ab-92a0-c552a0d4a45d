package com.junl.crm_work.controller;

import com.github.pagehelper.PageHelper;
import com.junl.crm_common.common.ParentDto;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SmsRecordEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.SmsErrorDao;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR> <PERSON>
 * @date 2024/3/25 11:26
 */
@RequestMapping("/sms")
@RestController
@RequiredArgsConstructor
public class SmsErrorController {

    private final SmsErrorDao smsErrorDao;


    @PostMapping("/getList")
    public Result getList(@RequestBody ParentDto parentDto){
        PageHelper.startPage(parentDto.getPage(),parentDto.getLimit());
        List<SmsRecordEntity> list = smsErrorDao.getList();
        return Result.success(PageUtils.getData(list));
    }


}
