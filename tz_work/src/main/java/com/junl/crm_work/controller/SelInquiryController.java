package com.junl.crm_work.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_work.listener.SmsListener;
import com.junl.crm_work.service.SelInquiryCustomerService;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.status.BusCode;
import com.junl.crm_work.status.InquiryStatus;
import com.junl.crm_work.vo.InquiryExcel;
import com.junl.crm_work.vo.SelInquiryVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.msg.common.MsgType;
import com.junl.msg.socket.WebSocketChannel;
import com.junl.system.UserMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.simpleframework.xml.Path;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sell/selInquiry")
@Api(tags = "销售询价主表接口")
public class SelInquiryController extends ParentController {

    @Autowired
    private SelInquiryService selInquiryService;


    @Autowired
    private SelInquiryCustomerService selInquiryCustomerService;

    @Autowired
    private RedisUtils redisUtils;


    @Autowired
    private SmsListener smsListener;

    @Autowired
    private UserMsgService userMsgService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "销售询价主表表--分页列表查询",notes="销售询价主表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelInquiryVo selInquiryVo){
        selInquiryVo.setUserId(getUserId());
        selInquiryVo.setCompanyId(getCompanyId());
        PageEntity page = selInquiryService.queryPage(selInquiryVo);
        return Result.success(page);
    }


    @ApiOperation("查询商家报价详情")
    @GetMapping("/getInquiryInfo/{id}")
    public Result getInquiryInfo(@PathVariable("id")String id){
        List<InquiryExcel> inquiryInfo = selInquiryService.getInquiryInfo(id);
        return Result.success(inquiryInfo);
    }


    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售询价主表查询详情",notes="销售询价主表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selInquiryService.getById(id));
    }


    @ApiOperation("分页查询询价报价信息")
    @PostMapping("/queryCustomerOffer")
    public Result queryCustomerOffer(@RequestBody SelInquiryVo selInquiryVo){
        return Result.success(selInquiryService.queryCustomerOffer(selInquiryVo));
    }


    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售询价主表--删除", notes = "销售询价主表--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] ids){
        for (String id : ids) {
            SelInquiryEntity selInquiryEntity=new SelInquiryEntity();
            selInquiryEntity.setId(id);
            selInquiryEntity.setIsDelete(1);
            selInquiryService.updateById(selInquiryEntity);
        }
        return Result.success();
    }

    @ApiOperation("保存并发布询价")
    @PostMapping("/issue")
    public Result issue(@RequestBody SelInquiryEntity selInquiryEntity){
        selInquiryEntity.setCompanyId(getCompanyId());
        Validate.startValidate(new NotNullVerification(selInquiryEntity,null));
        selInquiryService.issue(selInquiryEntity);
        return Result.success();
    }


    @ApiOperation("保存询价")
    @PostMapping("/savePlan")
    public Result savePlan(@RequestBody SelInquiryEntity selInquiryEntity){
        selInquiryEntity.setCompanyId(getCompanyId());
        Validate.startValidate(new NotNullVerification(selInquiryEntity,null));
        selInquiryService.savePlan(selInquiryEntity);
        return Result.success();
    }

//    @ApiOperation("修改询价")
//    @PostMapping("/updatePlan")
//    public Result updatePlan(@RequestBody SelInquiryEntity selInquiryEntity){
//        selInquiryService.updatePlan(selInquiryEntity);
//        return Result.success();
//    }



    @ApiOperation("提交竞价")
    @PostMapping("/bidding")
    public Result bidding(@RequestBody  SelInquiryVo selInquiryVo){
        Validate.startValidate(new NotNullVerification(selInquiryVo,null));
        selInquiryVo.setUserId(getUserId());
        selInquiryService.bidding(selInquiryVo);
        return Result.success();
    }

    @ApiOperation("根据询价id查询关联的商品")
    @GetMapping("/queryProduct/{id}")
    public Result queryProduct(@PathVariable("id")String id){
        return Result.success(selInquiryService.queryProduct(id));
    }

    @ApiOperation("提前开始询价")
    @GetMapping("/advance/{id}")
    public Result advance(@PathVariable("id")String id){

        SelInquiryEntity byId = selInquiryService.getById(id);
        Assert.notNull(byId,"查询不到该主键信息");
        if (InquiryStatus.INQUIRY.getCode().equals(byId.getInquiryStatus())) {
            throw new RuntimeException("已在询价中,请勿重复提交.");
        }

        SelInquiryEntity selInquiryEntity=new SelInquiryEntity();
        selInquiryEntity.setId(id);
        selInquiryEntity.setInquiryStatus(InquiryStatus.INQUIRY.getCode());
        selInquiryEntity.setInquiryStartDate(new Date());
        selInquiryService.updateById(selInquiryEntity);

        redisUtils.delete(RedisKey.INQUIRY.getName()+id);
        redisUtils.set(
                RedisKey.INQUIRY.getName()+id,
                Opposite.SINGLE,
                DateUtils.compareSecond(DateUtils.getNow(),byId.getInquiryEndTime())
        );

        //发送消息通知各商家
        List<SelInquiryCustomerEntity> customerList = selInquiryCustomerService.list(
                new LambdaQueryWrapper<SelInquiryCustomerEntity>()
                        .eq(SelInquiryCustomerEntity::getInquiryId, id)
        );


        //短信通知
        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
        sysUserMsgEntity.setMsgType(MsgType.SMS.getCode());
        sysUserMsgEntity.setBusCode(BusCode.START_INQUIRE.getCode());
        sysUserMsgEntity.setMsgAccessory(CommonUtil.collect(customerList,SelInquiryCustomerEntity::getCustomerId,","));
        sysUserMsgEntity.setMsgContent(selInquiryEntity.getId());
        sysUserMsgEntity.setCompanyId(selInquiryEntity.getCompanyId());
        smsListener.dispose(sysUserMsgEntity);

        return Result.success();
    }


    @ApiOperation("提前结束询价")
    @GetMapping("/advanceOver/{id}")
    public Result advanceOver(@PathVariable("id")String id){
        SelInquiryEntity byId = selInquiryService.getById(id);
        Assert.notNull(byId,"查询不到该主键信息");

        if (InquiryStatus.INQUIRY_OVER.getCode().equals(byId.getInquiryStatus())) {
            throw new RuntimeException("询价已结束,请勿重复提交.");
        }

        SelInquiryEntity selInquiryEntity=new SelInquiryEntity();
        selInquiryEntity.setId(id);
        selInquiryEntity.setInquiryEndTime(new Date());
        selInquiryEntity.setInquiryStatus(InquiryStatus.INQUIRY_OVER.getCode());
        selInquiryService.updateById(selInquiryEntity);

        redisUtils.delete(RedisKey.INQUIRY.getName()+id);

        List<SelInquiryCustomerEntity> customerList = selInquiryCustomerService.list(
                new LambdaQueryWrapper<SelInquiryCustomerEntity>()
                        .eq(SelInquiryCustomerEntity::getInquiryId, id)
        );

//        ThreadPoolUtil.runTask(
//                () -> {
//                    customerList.forEach(x->{
//                        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//                        sysUserMsgEntity.setMsgType(MsgType.USER.getCode());
//                        sysUserMsgEntity.setMsgTitle("询价提前结束通知");
//                        sysUserMsgEntity.setMsgContent("邀请您参加的"+byId.getInquiryName()+"的商品询价已提前结束.");
//                        sysUserMsgEntity.setBusCode(BusCode.OVER_INQUIRE.getCode());
//                        sysUserMsgEntity.setMsgAccessory(id);
//                        sysUserMsgEntity.setCompanyId(selInquiryEntity.getCompanyId());
//                        sysUserMsgEntity.setReader(x.getCustomerId());
//                        userMsgService.saveHistory(sysUserMsgEntity);
//                    });
//                }
//        );

        //短信通知
        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
        sysUserMsgEntity.setMsgType(MsgType.SMS.getCode());
        sysUserMsgEntity.setBusCode(BusCode.OVER_INQUIRE.getCode());
        sysUserMsgEntity.setMsgAccessory(CommonUtil.collect(customerList,SelInquiryCustomerEntity::getCustomerId,","));
        sysUserMsgEntity.setMsgContent(selInquiryEntity.getId());
        sysUserMsgEntity.setCompanyId(selInquiryEntity.getCompanyId());

        smsListener.dispose(sysUserMsgEntity);
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.SMS.getKey());


        redisUtils.delete(RedisKey.INQUIRY_NOTICE.getName()+selInquiryEntity.getId());


        return Result.success();
    }

    @ApiOperation("导出询价excel")
    @GetMapping("/exportExcel/{id}")
    public Result exportExcel(@PathVariable("id")String id){
        Map<String, String> map = selInquiryService.exportExcel(id);
        return Result.success(map);
    }

    @ApiOperation("查询询价的所有客户（已报价的）")
    @GetMapping("/getInquiryCustomer/{id}")
    public Result getInquiryCustomer(@PathVariable("id")String id){
        return Result.success(selInquiryService.getInquiryCustomer(id));
    }

    @ApiOperation("查询询价的所有客户")
    @GetMapping("/queryInquiryCustomer/{id}")
    public Result queryInquiryCustomer(@PathVariable("id")String id){
        return Result.success(selInquiryService.queryInquiryCustomer(id));
    }


}