package com.junl.crm_work.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.api.R;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.work.SelProductEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.SelProductService;
import com.junl.crm_work.status.BusPrefix;
import com.junl.crm_work.util.CodeGenerate;
import com.junl.crm_work.util.SalesModel;
import com.junl.crm_work.vo.SelProductVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.*;
import org.jboss.netty.util.internal.NonReentrantLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.locks.AbstractQueuedSynchronizer;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sell/selProduct")
@Api(tags = "商品表接口")
public class SelProductController  extends ParentController {

    @Autowired
    private SelProductService selProductService;

    @Autowired
    private CodeGenerate codeGenerate;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "商品表表--分页列表查询",notes="商品表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelProductVo selProductVo){
        selProductVo.setCompanyId(getCompanyId());
        PageEntity page = selProductService.queryPage(selProductVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "商品表表--列表查询（按公司查询）",notes="商品表表--列表查询")
    @GetMapping("/getList")
    public Result getList(){
        List<SelProductEntity> list = selProductService.list(new QueryWrapper<SelProductEntity>().select("id","product_name")
                .lambda()
                .and(e->{
                    e.eq(SelProductEntity::getIsDelete,Opposite.ZERO)
                            .eq(SelProductEntity::getCompanyId,getCompanyId());
                }));
        return Result.success(list);
    }

    @ApiOperation(value = "商品表表--列表查询(查询全部)",notes="商品表表--列表查询")
    @GetMapping("/getLists")
    public Result getLists(){
        List<SelProductEntity> list = selProductService.list(new QueryWrapper<SelProductEntity>().select("id","product_name")
                .eq("is_delete", Opposite.ZERO));
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "商品表查询详情",notes="商品表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selProductService.getById(id));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "商品表列表--新增", notes = "商品表列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SelProductEntity selProductEntity){
        selProductEntity.setId(SnowFlake.getUUId());
        selProductEntity.setProductCode(codeGenerate.getCode(BusPrefix.PRODUCT));
        Validate.startValidate(new NotNullVerification(selProductEntity,null));

        if (selProductService.count(new LambdaQueryWrapper<SelProductEntity>()
                .and(e->e.eq(SelProductEntity::getProductName,selProductEntity.getProductName()))
                .eq(SelProductEntity::getCompanyId,getCompanyId())) >0) {
            return Result.error("商品名称重复,请重新修改");
        }
        RuleTools.setField(selProductEntity,getSysUserEntity(),true);
        selProductService.save(selProductEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "商品表--修改", notes = "商品表--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SelProductEntity selProductEntity){
        Validate.startValidate(new NotNullVerification(selProductEntity,null));
        RuleTools.setField(selProductEntity,getSysUserEntity(),false);
        if (selProductService.count(new LambdaQueryWrapper<SelProductEntity>()
                .and(e->e.eq(SelProductEntity::getProductName,selProductEntity.getProductName()))
                .eq(SelProductEntity::getCompanyId,getCompanyId())
                .ne(SelProductEntity::getId,selProductEntity.getId())) >0) {
            return Result.error("商品名称重复,请重新修改");
        }

        selProductService.updateById(selProductEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */


    @ApiOperation(value = "商品表--删除", notes = "商品表--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] ids){
        for (String id : ids) {
            SelProductEntity selProductEntity=new SelProductEntity();
            selProductEntity.setId(id);
            selProductEntity.setIsDelete(1);
            selProductService.updateById(selProductEntity);
        }
        return Result.success();
    }


    @ApiOperation("商品excel 模板下载")
    @GetMapping("/download")
    public Result download(){
        return Result.success(selProductService.download(new ByteArrayOutputStream()));
    }

    @ApiOperation("excel商品导入")
    @PostMapping("/import")
    public Result importExcel(@RequestParam("url")String url){
        return Result.success(selProductService.importExcel(url,getSysUserEntity()));
    }

}