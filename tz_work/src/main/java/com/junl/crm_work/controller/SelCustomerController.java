package com.junl.crm_work.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelGroupRelevanceEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.crm_work.service.SelGroupRelevanceService;
import com.junl.crm_work.status.BusPrefix;
import com.junl.crm_work.util.CodeGenerate;
import com.junl.crm_work.vo.SelCustomerVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sell/selCustomer")
@Api(tags = "企业表接口")
public class SelCustomerController  extends ParentController {

    @Autowired
    private SelCustomerService selCustomerService;

    @Autowired
    private CodeGenerate codeGenerate;

    @Autowired
    private SelGroupRelevanceService selGroupRelevanceService;

    @Autowired
    private UserService userService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "企业表表--分页列表查询",notes="企业表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelCustomerVo selCustomerVo){
        selCustomerVo.setCompanyId(getCompanyId());
        PageEntity page = selCustomerService.queryPage(selCustomerVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "企业表表--列表查询",notes="企业表表--列表查询")
    @GetMapping("/getList")
    public Result getList(){
        List<SelCustomerEntity> list = selCustomerService.list(
                new QueryWrapper<SelCustomerEntity>().select("customer_name","id")
                        .eq("is_enable",Opposite.ZERO)
                        .eq("company_id",getCompanyId())
                        .eq("is_delete",Opposite.ZERO)
        );
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "企业表查询详情",notes="企业表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selCustomerService.getById(id));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "企业表列表--新增", notes = "企业表列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SelCustomerEntity selCustomerEntity){
        selCustomerEntity.setId(SnowFlake.getUUId());

        if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
                .and(e->e.eq(SelCustomerEntity::getCustomerName,selCustomerEntity.getCustomerName())
                        .eq(SelCustomerEntity::getIsDelete,Opposite.ZERO)
                        .eq(SelCustomerEntity::getCompanyId,getCompanyId())))>0) {
            return Result.error("企业名称重复,请重新修改");
        }

//        if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
//                .and(e->e.eq(SelCustomerEntity::getDeputy,selCustomerEntity.getDeputy())
//                        .eq(SelCustomerEntity::getIsDelete, Opposite.ZERO)))>0) {
//
//            return Result.error("企业管理人账号已存在,请重新修改");
//        }


        //判断系统用户是否有手机号重复的 因为小程序合并了通过手机号确认身份 所以不能有两端手机号重复的
        SysUserEntity userInfoByTel = userService.getUserInfoByTel(selCustomerEntity.getDeputyPhone());
        if (!ObjectUtils.isEmpty(userInfoByTel)) {
            return Result.error("企业用户手机号码已注册,请更换手机号");
        }

        if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
                .and(e->e.eq(SelCustomerEntity::getDeputyPhone,selCustomerEntity.getDeputyPhone())
                        .eq(SelCustomerEntity::getIsDelete,Opposite.ZERO)))>0) {
            return Result.error("企业用户手机号码已注册,请更换手机号");
        }

        selCustomerEntity.setCustomerCode(codeGenerate.getCode(BusPrefix.CUSTOMER));
        Validate.startValidate(new NotNullVerification(selCustomerEntity,null));
        RuleTools.setField(selCustomerEntity,getSysUserEntity(),true);
        selCustomerService.save(selCustomerEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "企业表--修改", notes = "企业表--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SelCustomerEntity selCustomerEntity){
        Validate.startValidate(new NotNullVerification(selCustomerEntity,null));

        if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
                .and(e->e.eq(SelCustomerEntity::getCustomerName,selCustomerEntity.getCustomerName())
                        .eq(SelCustomerEntity::getCompanyId,getCompanyId())
                        .eq(SelCustomerEntity::getIsDelete,Opposite.ZERO)
                        .ne(SelCustomerEntity::getId,selCustomerEntity.getId())))>0) {
            return Result.error("企业名称重复,请重新修改");
        }

        //判断系统用户是否有手机号重复的 因为小程序合并了通过手机号确认身份 所以不能有两端手机号重复的
        SysUserEntity userInfoByTel = userService.getUserInfoByTel(selCustomerEntity.getDeputyPhone());
        if (!ObjectUtils.isEmpty(userInfoByTel)) {
            return Result.error("企业用户手机号码已注册,请更换手机号");
        }

        if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
                .and(e->e.eq(SelCustomerEntity::getDeputyPhone,selCustomerEntity.getDeputyPhone())
                        .eq(SelCustomerEntity::getIsDelete,Opposite.ZERO)
                        .ne(SelCustomerEntity::getId,selCustomerEntity.getId())))>0) {
            return Result.error("企业用户手机号码已注册,请更换手机号");
        }




        RuleTools.setField(selCustomerEntity,getSysUserEntity(),false);
        selCustomerService.updateById(selCustomerEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */


    @ApiOperation("禁用企业")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] ids){
        for (String id : ids) {
            SelCustomerEntity selCustomerEntity=new SelCustomerEntity();
            selCustomerEntity.setIsEnable(Integer.valueOf(Opposite.SINGLE));
            selCustomerEntity.setId(id);
            selCustomerService.updateById(selCustomerEntity);
        }
        return Result.success();
    }

    @ApiOperation("企业认证")
    @GetMapping("/auth/{id}")
    public Result auth(@PathVariable("id")String id){
        SelCustomerEntity selCustomerEntity=new SelCustomerEntity();
        selCustomerEntity.setId(id);
        selCustomerEntity.setIsAuth(Integer.valueOf(Opposite.SINGLE));
        selCustomerService.updateById(selCustomerEntity);
        return Result.success();
    }


    @ApiOperation("启用企业")
    @PostMapping("/enable")
    public Result enable(@RequestBody String[] ids){
        for (String id : ids) {
            SelCustomerEntity selCustomerEntity=new SelCustomerEntity();
            selCustomerEntity.setIsEnable(Integer.valueOf(Opposite.ZERO));
            selCustomerEntity.setId(id);
            selCustomerService.updateById(selCustomerEntity);
        }
        return Result.success();
    }

    @ApiOperation("删除企业")
    @PostMapping("/deleteCustomer")
    public Result deleteCustomer(@RequestBody String[] ids){
        for (String id : ids) {
            SelCustomerEntity selCustomerEntity=new SelCustomerEntity();
            selCustomerEntity.setIsDelete(Integer.valueOf(Opposite.SINGLE));
            selCustomerEntity.setId(id);
            selCustomerService.updateById(selCustomerEntity);

            //删除已被分组的记录
            selGroupRelevanceService.remove(new LambdaQueryWrapper<SelGroupRelevanceEntity>()
                    .eq(SelGroupRelevanceEntity::getCustomerId,id));
        }
        return Result.success();
    }

    @ApiOperation("企业excel 模板下载")
    @GetMapping("/download")
    public Result download(){
        return Result.success(selCustomerService.download(new ByteArrayOutputStream()));
    }

    @ApiOperation("excel企业导入")
    @PostMapping("/import")
    public Result importExcel(@RequestParam("url")String url){
        return Result.success(selCustomerService.importExcel(url,getSysUserEntity()));
    }

    @ApiOperation("excel企业导出")
    @PostMapping("/export")
    public Result exportExcel(@RequestBody SelCustomerVo selCustomerVo){
        selCustomerVo.setCompanyId(getCompanyId());
        return Result.success(selCustomerService.exportExcel(selCustomerVo));
    }


}