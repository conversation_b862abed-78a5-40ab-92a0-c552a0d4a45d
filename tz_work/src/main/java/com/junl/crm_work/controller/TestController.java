package com.junl.crm_work.controller;

import com.alibaba.fastjson.JSONObject;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelBiddingCustomerEntity;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_common.tools.SqlBatchUtil;
import com.junl.crm_work.dao.SelBiddingCustomerDao;
import com.junl.crm_work.wx.WxUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Description:
 *
 * <AUTHOR> <PERSON>
 * @date 2024/3/19 15:36
 */

@RequestMapping("/test")
@RestController
@RequiredArgsConstructor
public class TestController {

    private final WxUtils wxUtils;

    private final RedisUtils redisUtils;


    @RequestMapping("/test1")
    public Result test(){
        if (!redisUtils.hasKey("zhangsan")) {
            redisUtils.set("zhangsan","0",25-LocalDateTime.now().getHour(), TimeUnit.HOURS);
        }

        for(int i=0;i<10;i++){
            long zhangsan = redisUtils.increment("zhangsan");
            System.out.println(zhangsan);
        }
        return Result.success();
    }

}
