package com.junl.crm_work.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.AuditDto;
import com.junl.crm_common.pojo.work.SelApprovalDefinitionEntity;
import com.junl.crm_common.pojo.work.SelApprovalFlowEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.exception.ExamineException;
import com.junl.crm_work.service.SelApprovalDefinitionService;
import com.junl.crm_common.common.Result;
import com.junl.crm_work.vo.SelApprovalFlowVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sell/selApprovalDefinition")
@Api(tags = "流程定义表接口")
public class SelApprovalDefinitionController  extends ParentController {

    @Autowired
    private SelApprovalDefinitionService selApprovalDefinitionService;

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "流程定义表表--列表查询",notes="流程定义表表--列表查询")
    @PostMapping("/getList")
    public Result getList(){
        return Result.success(selApprovalDefinitionService.getList(getSysUserEntity()));
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "流程定义表查询详情",notes="流程定义表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String  id){
        return Result.success(selApprovalDefinitionService.getInfo(id));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "流程定义表列表--新增", notes = "流程定义表列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SelApprovalDefinitionEntity selApprovalDefinitionEntity){
        selApprovalDefinitionEntity.setId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(selApprovalDefinitionEntity,null));
        RuleTools.setField(selApprovalDefinitionEntity,getSysUserEntity(),true);

        AuditDto treeData = selApprovalDefinitionEntity.getTreeData();
        selApprovalDefinitionEntity.setSchedule(JSONObject.toJSONString(treeData));

        if (selApprovalDefinitionService.count(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,getCompanyId())
                .eq(SelApprovalDefinitionEntity::getBusCode,selApprovalDefinitionEntity.getBusCode())))>0) {
            return Result.error("该业务类型的流程已经定义,请更换其他流程定义或直接修改.");
        }

        selApprovalDefinitionService.save(selApprovalDefinitionEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "流程定义表--修改", notes = "流程定义表--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SelApprovalDefinitionEntity selApprovalDefinitionEntity){
        Validate.startValidate(new NotNullVerification(selApprovalDefinitionEntity,null));
        RuleTools.setField(selApprovalDefinitionEntity,getSysUserEntity(),false);
        AuditDto treeData = selApprovalDefinitionEntity.getTreeData();
        selApprovalDefinitionEntity.setSchedule(JSONObject.toJSONString(treeData));

        if (selApprovalDefinitionService.count(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode,selApprovalDefinitionEntity.getBusCode())
                        .ne(SelApprovalDefinitionEntity::getId,selApprovalDefinitionEntity.getId())))>0) {
           return  Result.error("该业务类型的流程已经定义,请更换其他流程定义或直接修改.");
        }

        selApprovalDefinitionService.updateById(selApprovalDefinitionEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "流程定义表--删除", notes = "流程定义表--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody Long[] ids){
        selApprovalDefinitionService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }


    @ApiOperation("审核提交")
    @PostMapping("/submit")
    public Result submit(@RequestBody SelApprovalFlowVo selApprovalFlowVo){
        Validate.startValidate(new NotNullVerification(selApprovalFlowVo,null));
        try {
            selApprovalDefinitionService.submit(selApprovalFlowVo);
        }catch (ExamineException e){
            return Result.reCall(e.getMessage());
        }

        return Result.success();
    }

    @ApiOperation("根据业务主键和业务类型查询历史审核记录")
    @PostMapping("/getAuditRecord")
    public Result getAuditRecord(@RequestBody SelApprovalFlowVo selApprovalFlowVo){
        return Result.success(selApprovalDefinitionService.getAuditRecord(selApprovalFlowVo));
    }
}