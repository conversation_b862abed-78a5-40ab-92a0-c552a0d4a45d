package com.junl.crm_work.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.FlowSelPlanEntity;
import com.junl.crm_common.pojo.work.SelPlanEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.DateUtils;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.SelPlanService;
import com.junl.crm_work.vo.SelPlanVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.Reference;
import org.checkerframework.checker.units.qual.A;
import org.simpleframework.xml.Path;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sell/selPlan")
@Api(tags = "销售计划接口")
public class SelPlanController extends ParentController {

    @Autowired
    private SelPlanService selPlanService;

    @Reference
    private UserService userService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "销售计划表--分页列表查询",notes="销售计划表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelPlanVo selPlanVo){
        selPlanVo.setUserId(getUserId());
        selPlanVo.setCompanyId(getCompanyId());
        PageEntity page = selPlanService.queryPage(selPlanVo);
        return Result.success(page);
    }



    @ApiOperation("获取所有销售计划")
    @GetMapping("/getList")
    public Result getList(){

        List<String> list=new ArrayList<>();

        SysUserEntity userInfo = userService.getUserInfo(getUserId());
        Assert.notNull(userInfo,"系统无法识别的账户信息");
        if(Assert.notNull(userInfo.getDataId())&&!Opposite.ZERO.equals(userInfo.getDataId())){
            List<String> users = userService.getUsers(userInfo.getDataId());
            list.addAll(users);
        }else{
            Integer identityCode = userInfo.getIdentityCode();

            if (identityCode.equals(IdentityStatus.DEPT_ADMIN.getCode())) {
                List<String> deptUsers = userService.getDeptUsers(userInfo.getDeptId());
                list.addAll(deptUsers);
            }else if(identityCode.equals(IdentityStatus.USER.getCode())){
                ArrayList<String> objects = new ArrayList<>();
                objects.add(userInfo.getUserId());
                list.addAll(objects);
            }
        }

        return Result.success(
                selPlanService.list(
                        new LambdaQueryWrapper<SelPlanEntity>()
                                .and(e->e.eq(SelPlanEntity::getCompanyId,getCompanyId())
                                        .eq(SelPlanEntity::getIsDelete,Opposite.ZERO)
                                        .in(list.size()>0,SelPlanEntity::getCreateBy,list)
                                ).orderByDesc(SelPlanEntity::getCreateTime)
//                                .last("limit 20")
                )
        );
    }


    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "销售计划查询详情",notes="销售计划查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selPlanService.getInfo(id));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售计划列表--新增", notes = "销售计划列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SelPlanEntity selPlanEntity){
        selPlanEntity.setId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(selPlanEntity,null));
        RuleTools.setField(selPlanEntity,getSysUserEntity(),true);
        selPlanService.savePlan(selPlanEntity);
        return Result.success();
    }


    @ApiOperation(value = "查询预计可销售量")
    @PostMapping("/querySalesCount")
    public Result querySalesCount(@RequestBody SelPlanVo selPlanVo){
        long l = DateUtils.compareDay(selPlanVo.getPlanStartDate(), selPlanVo.getPlanEndTime());
        List<SelPlanProductDetailEntity> product = selPlanVo.getProduct();
        product.forEach(x->{
            double count = selPlanService.getCount(x, l);
            x.setSaleQuantity(count);
        });
        return Result.success(product);
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售计划--修改", notes = "销售计划--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SelPlanEntity selPlanEntity){
        Validate.startValidate(new NotNullVerification(selPlanEntity,null));
        RuleTools.setField(selPlanEntity,getSysUserEntity(),false);
        selPlanService.updatePlan(selPlanEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售计划--删除", notes = "销售计划--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] ids){
        for (String id : ids) {
            SelPlanEntity selPlanEntity=new SelPlanEntity();
            selPlanEntity.setId(id);
            selPlanEntity.setIsDelete(Integer.valueOf(Opposite.SINGLE));
            selPlanService.updateById(selPlanEntity);
        }
        return Result.success();
    }

    @ApiOperation("提交审核或再次提交审核")
    @GetMapping("/audit/{id}")
    public Result audit(@PathVariable("id")String id){
        selPlanService.audit(id,getUserId());
        return Result.success();
    }

    @ApiOperation("查看审批进度")
    @GetMapping("/getSchedule/{id}")
    public Result getSchedule(@PathVariable("id")String id){
        return Result.success(selPlanService.getSchedule(id));
    }

    @ApiOperation("根据审批流程节点id查询销售计划审批详情")
    @GetMapping("/getPlanInfo/{id}")
    public Result getPlanInfo(@PathVariable("id")String id){
        FlowSelPlanEntity planInfo = selPlanService.getPlanInfo(id);
        return Result.success(planInfo);
    }

    @ApiOperation("提交竞价(只有零售和纯竞价模式可以走)")
    @PostMapping("/submitBidding")
    public Result submitBidding(@RequestBody SelPlanVo selPlanVo){
        selPlanVo.setUserId(getUserId());
        selPlanService.submitBidding(selPlanVo);
        return Result.success();
    }


    @ApiOperation("提交竞价(只有定价模式可以走)")
    @PostMapping("/submitPricing")
    public Result submitPricing(@RequestBody SelPlanVo selPlanVo){
        selPlanVo.setUserId(getUserId());
        selPlanService.submitPricing(selPlanVo);
        return Result.success();
    }



    @ApiOperation("销售计划审核撤销")
    @GetMapping("/revocation/{id}")
    public Result revocation(@PathVariable("id")String id){
        selPlanService.revocation(id);
        return Result.success();
    }

    @ApiOperation("根据询价id查询关联的商品")
    @GetMapping("/queryProduct/{id}")
    public Result queryProduct(@PathVariable("id")String id){
        return Result.success(selPlanService.queryProduct(id));
    }

}