package com.junl.crm_work.util;

import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_work.status.BusPrefix;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description: 业务编码生成
 * @author: daiqimeng
 * @date: 2021/7/1310:45
 */
@Component
public class CodeGenerate {
    //时间格式化
    private static SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
    //数字格式化
    private static NumberFormat numberFormat=new DecimalFormat("00000");

    @Autowired
    private RedisUtils redisUtils;

    /**
     * @describe:  业务编码生成
     * <AUTHOR>
     * @date 2021/7/13 14:11
     * @param busPrefix
     * @return: {@link String}
     */
    public synchronized String getCode(BusPrefix busPrefix){
        Assert.notNull(busPrefix,"入参不能为null  CodeGenerate#getCode");
        String code;
        String number = redisUtils.get(busPrefix.getPrefix());
        if (!Assert.notNull(number)) {
            code=Opposite.SINGLE;
            redisUtils.set(busPrefix.getPrefix(),code);
        }else{
            code=number;
            redisUtils.increment(busPrefix.getPrefix());
        }
        return busPrefix.getPrefix()+simpleDateFormat.format(new Date())+numberFormat.format(Integer.valueOf(code));
    }

}
