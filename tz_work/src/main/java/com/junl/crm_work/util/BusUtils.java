package com.junl.crm_work.util;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.AuditDto;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.pojo.work.SelApprovalDefinitionEntity;
import com.junl.crm_common.pojo.work.SelApprovalFlowEntity;
import com.junl.crm_common.pojo.work.SelApprovalUnderwayEntity;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.*;
import com.junl.crm_work.bo.InitFlowBO;
import com.junl.crm_work.dao.SelApprovalFlowDao;
import com.junl.crm_work.service.SelApprovalDefinitionService;
import com.junl.crm_work.service.SelApprovalUnderwayService;
import com.junl.crm_work.wx.SendMsgEntity;
import com.junl.crm_work.wx.WxTemplateMsg;
import com.junl.crm_work.wx.WxTemplateMsgDev;
import com.junl.crm_work.wx.WxUtils;
import com.junl.msg.SocketUtils;
import com.junl.msg.socket.ChannelSocketEntity;
import com.junl.msg.socket.SocketStatus;
import com.junl.msg.socket.WebSocketChannel;
import com.junl.system.UserService;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description: 业务工具
 * @author: daiqimeng
 * @date: 2021/7/2923:18
 */
@Log4j2
public class BusUtils {


    public static void sendMsg(String userId){
        UserService userService = SpringUtil.getBean(UserService.class);

        SysUserSoleEntity sole = userService.getSole(userId);

        if (ObjectUtils.isEmpty(sole)) {
            log.warn("查询用户唯一标识不存在: {}",userId);
            return;
        }


        List<ChannelSocketEntity> channelSocket = SocketUtils.query(
                WebSocketChannel.handlerList,
                sole.getSoleId()
        );

        if (CollectionUtils.isEmpty(channelSocket)) {
            log.info("用户不在线 信息未实时送达: {}",userId);
            return;
        }

        Result success = Result.success();
        success.setCode(SocketStatus.MSG.getCode());
        for (ChannelSocketEntity socketEntity : channelSocket) {
            socketEntity.getContext().writeAndFlush(
                    new TextWebSocketFrame(JSONObject.toJSONString(success))
            );
        }
    }


    public static void wxAuditPush(String approverId,String submitId,String content){

        UserService userService = SpringUtil.getBean(UserService.class);

        SysUserSoleEntity sole=userService.getSole(approverId);
        if(Assert.notNull(sole.getOpenId())){
            SendMsgEntity sendMsgEntity = new SendMsgEntity();
            sendMsgEntity.setWord1(content);
            sendMsgEntity.setUser(sole.getOpenId());
            sendMsgEntity.setTitle("您有一条新的记录待审批");
            sendMsgEntity.setWord2(DateUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
            SysUserEntity userInfo = userService.getUserInfo(submitId);

            sendMsgEntity.setWord3(userInfo.getRealName());
            sendMsgEntity.setRemark("提交公司： "+SpringUtil.getBean(RedisUtils.class).get(userInfo.getCompanyId()));

            WxUtils wxUtils = SpringUtil.getBean(WxUtils.class);

            String msg1 = SpringUtil.getProperties("wx.template_msg1");

            //发送公众号消息给审批人
            if (SpringUtil.isProd()) {
                WxTemplateMsg assembly = wxUtils.assembly(sendMsgEntity);
                assembly.setTemplate_id(msg1);
                wxUtils.sendPost(assembly);
            }else {
                WxTemplateMsgDev templateMsgDev = wxUtils.assemblyDev(sendMsgEntity);
                templateMsgDev.setTemplate_id(msg1);
                wxUtils.sendPostDev(templateMsgDev);
            }

        }
    }


    //流程定义初始化  返回第一个节点的审批人ID和id
    public static InitFlowBO definitionFlow(String busCode, String companyId, String busId,String userId){

        //查询出流程  不存在没有的情况 调用之前需要校验一下
        SelApprovalDefinitionEntity approval = SpringUtil.getBean(SelApprovalDefinitionService.class)
                .getOne(
                        new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                                .and(e -> e.eq(SelApprovalDefinitionEntity::getBusCode, busCode)
                                        .eq(SelApprovalDefinitionEntity::getCompanyId, companyId)));

        //进行中流程定义
        SelApprovalUnderwayEntity underwayEntity = SpringUtil.getBean(SelApprovalUnderwayService.class)
                .queryFlowAll(busId);

        SelApprovalUnderwayEntity selApprovalUnderwayEntity=new SelApprovalUnderwayEntity();
        selApprovalUnderwayEntity.setId(SnowFlake.getUUId());
        selApprovalUnderwayEntity.setBusId(busId);
        selApprovalUnderwayEntity.setBusType(approval.getBusCode());
        selApprovalUnderwayEntity.setCompanyId(approval.getCompanyId());
        selApprovalUnderwayEntity.setSchedule(approval.getSchedule());
        selApprovalUnderwayEntity.setSort(Assert.notNull(underwayEntity)?underwayEntity.getSort()+1:1);
        selApprovalUnderwayEntity.setCreateBy(userId);
        SpringUtil.getBean(SelApprovalUnderwayService.class)
                .save(selApprovalUnderwayEntity);


        String schedule = approval.getSchedule();
        //根据json转化为想要的流程对象  去掉无用的开始头节点 从下一个节点开始
        AuditDto auditDto = JSONObject.parseObject(schedule, AuditDto.class);
        //首个流程节点定义人
        List<AuditDto> children = auditDto.getChildren();
        String leadsId =children.get(0).getLeadsId();

        List<SelApprovalFlowEntity> flowEntities=new ArrayList<>();

        //递归定义流程
        recursionGetNode(
                children,
                IdentityStatus.GOD.getName(),
                selApprovalUnderwayEntity.getId(),
                flowEntities
        );
        //保存流程
        SqlBatchUtil.execute(
                flowEntities,
                SelApprovalFlowDao.class,
                (t,m)->m.insert(t)
        );

        return new InitFlowBO()
                .setLeadsId(leadsId)
                .setFirstFlowId(
                        flowEntities.get(0).getId()
                )
                .setUnderWayId(
                        selApprovalUnderwayEntity.getId()
                );
    }


    private static void recursionGetNode(List<AuditDto> list, String parent, String underway,List<SelApprovalFlowEntity> result){
        if (Assert.notNullCollect(list)) {
            AuditDto auditDto = list.get(0);
            SelApprovalFlowEntity selApprovalFlowEntity=new SelApprovalFlowEntity();
            selApprovalFlowEntity.setId(SnowFlake.getUUId());
            selApprovalFlowEntity.setApprover(auditDto.getLeadsId());
            selApprovalFlowEntity.setParentId(parent);
            selApprovalFlowEntity.setPanelName(auditDto.getPanelName());
            selApprovalFlowEntity.setApproverName(auditDto.getLeadsName());
            selApprovalFlowEntity.setUnderwayId(underway);
            result.add(selApprovalFlowEntity);
            //继续
            recursionGetNode(auditDto.getChildren(),selApprovalFlowEntity.getId(),underway,result);
        }
    }



    /**
     * @describe:  计算各节点审批时长
     * <AUTHOR>
     * @date 2021/7/29 23:30
     * @param selApprovalFlowEntity
     * @return: {@link SelApprovalFlowEntity}
     */
    public static SelApprovalFlowEntity approvalTime(SelApprovalFlowEntity selApprovalFlowEntity){
        Date createTime = selApprovalFlowEntity.getCreateTime();
        Date updateTime = selApprovalFlowEntity.getUpdateTime();
        if(Assert.notNull(updateTime)){
            selApprovalFlowEntity.setTime(getDatePoor(updateTime,createTime));
            count(selApprovalFlowEntity.getChild(),updateTime);
        }
        return selApprovalFlowEntity;
    }

    /**
     * 计算审批子节点审批时长
     * @param selApprovalFlowEntity
     * @param date
     */
    private static void count(SelApprovalFlowEntity selApprovalFlowEntity,Date date){
        if(Assert.notNull(selApprovalFlowEntity)){
            Date updateTime = selApprovalFlowEntity.getUpdateTime();
            if (Assert.notNull(updateTime)) {
                selApprovalFlowEntity.setTime(getDatePoor(updateTime,date));
                count(selApprovalFlowEntity.getChild(),updateTime);
            }
        }
    }

    /**
     * 计算天时分秒
     * @param endDate
     * @param nowDate
     * @return
     */
    private static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒
        long s = diff % nd % nh % nm / ns;
        // 输出结果
        if(day<=0){
            return hour+"时"+min+"分"+s+"秒";
        }else{
            return day + "天" + hour + "时" + min + "分" + s + "秒";
        }
    }
}
