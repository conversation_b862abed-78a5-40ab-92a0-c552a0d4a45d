package com.junl.crm_work.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.RedisQueueService;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_work.service.SelInquiryCustomerService;
import com.junl.crm_work.service.SelInquiryOfferService;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.status.BusCode;
import com.junl.crm_work.status.InquiryStatus;
import com.junl.msg.common.MsgType;
import com.junl.msg.socket.WebSocketChannel;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/** 询价要结束通知 */
@Component
@Log4j2
public class InquiryNotice implements RedisQueueService {

    @Autowired
    private SelInquiryService selInquiryService;

    @Autowired
    private SelInquiryCustomerService selInquiryCustomerService;

    @Autowired
    private SelInquiryOfferService selInquiryOfferService;


    @Autowired
    private SmsListener smsListener;

    @Override
    public void dispose(String key, String busId) {
        if(RedisKey.INQUIRY_NOTICE.getName().equals(key+":")){
            SelInquiryEntity byId = selInquiryService.getById(busId);
            if (Assert.notNull(byId)) {
                if (byId.getInquiryStatus().equals(InquiryStatus.INQUIRY.getCode())) {
                    List<SelInquiryCustomerEntity> customer = selInquiryCustomerService.list(
                            new LambdaQueryWrapper<SelInquiryCustomerEntity>()
                                    .eq(SelInquiryCustomerEntity::getInquiryId, busId)
                    );

                    //查询出未报价的客户
                    List<SelInquiryCustomerEntity> customerList = customer.stream()
                            .filter(x -> {
                                if (
                                        selInquiryOfferService.count(
                                                new LambdaQueryWrapper<SelInquiryOfferEntity>()
                                                        .eq(SelInquiryOfferEntity::getRelevancyId, x.getId())
                                        ) <= 0
                                ) {
                                    return true;
                                }
                                return false;
                            })
                            .collect(Collectors.toList());

                    if (Assert.notNullCollect(customerList)) {
                        /** 通知 */
                        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
                        sysUserMsgEntity.setMsgType(MsgType.SMS.getCode());
                        sysUserMsgEntity.setBusCode(BusCode.INFORM_COMPANY.getCode());
                        sysUserMsgEntity.setMsgAccessory(
                                CommonUtil.collect(customerList,SelInquiryCustomerEntity::getCustomerId,",")
                        );
                        sysUserMsgEntity.setMsgContent(byId.getId());
                        sysUserMsgEntity.setCompanyId(byId.getCompanyId());

                        smsListener.dispose(sysUserMsgEntity);
                    }
                }
            }
        }
    }
}
