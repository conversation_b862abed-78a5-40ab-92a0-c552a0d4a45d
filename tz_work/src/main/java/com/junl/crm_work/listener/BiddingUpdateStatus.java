package com.junl.crm_work.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.RedisQueueService;
import com.junl.crm_common.pojo.work.SelBiddingEntity;
import com.junl.crm_common.pojo.work.SelBiddingSequenceEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.DateUtils;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_work.service.SelBiddingSequenceService;
import com.junl.crm_work.service.SelBiddingService;
import com.junl.crm_work.status.BiddingStatus;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 竞价询价状态更新
 * @author: daiqimeng
 * @date: 2021/7/2914:50
 */

@Component
@Log4j2
public class BiddingUpdateStatus implements RedisQueueService {

    @Autowired
    private SelBiddingService selBiddingService;

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void dispose(String key, String busId) {
        if(RedisKey.BIDDING.getName().equals(key+":")){
            SelBiddingEntity temp=new SelBiddingEntity();
            SelBiddingEntity byId = selBiddingService.getById(busId);
            if (Assert.notNull(byId)) {

                SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                        new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(x -> x.eq(SelBiddingSequenceEntity::getBiddingId, byId.getId())
                                .eq(SelBiddingSequenceEntity::getSequence, byId.getRoundNum()))
                );

                Integer biddingStatus = byId.getBiddingStatus();
                if(biddingStatus.equals(BiddingStatus.AUDIT_SUCCESS.getCode())){

                    temp.setBiddingStatus(BiddingStatus.BIDDING.getCode());
                    sequence.setBiddingStatus(BiddingStatus.BIDDING.getCode());
                    long l = DateUtils.compareSecond(DateUtils.getNow(), byId.getPlanEndTime());
                    redisUtils.set(RedisKey.BIDDING.getName()+busId, Opposite.SINGLE,l);

                }else if(biddingStatus.equals(BiddingStatus.BIDDING.getCode())){
                    temp.setBiddingStatus(BiddingStatus.BIDDING_OVER.getCode());
                    sequence.setBiddingStatus(BiddingStatus.BIDDING_OVER.getCode());
                }
                temp.setId(byId.getId());
                if(Assert.notNull(temp)&&Assert.notNull(temp.getBiddingStatus())){
                    selBiddingService.updateById(temp);
                    selBiddingSequenceService.updateById(sequence);
                }
            }else{
                log.warn("查询不到竞价主键记录：{}",busId);
            }

        }
    }
}
