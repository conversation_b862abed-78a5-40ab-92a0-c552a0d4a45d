package com.junl.crm_work.listener;

import com.junl.crm_common.common.RedisQueueService;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.DateUtils;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.status.InquiryStatus;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 询价状态更新
 * @author: daiqimeng
 * @date: 2021/7/2914:51
 */
@Component
@Log4j2
public class InquiryUpdateStatus implements RedisQueueService {

    @Autowired
    private SelInquiryService selInquiryService;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void dispose(String key, String busId) {
        if(RedisKey.INQUIRY.getName().equals(key+":")){
            SelInquiryEntity temp=new SelInquiryEntity();

            SelInquiryEntity byId = selInquiryService.getById(busId);
            if (Assert.notNull(byId)) {
                Integer inquiryStatus = byId.getInquiryStatus();
                if(inquiryStatus.equals(InquiryStatus.ISSUE.getCode())){
                    temp.setInquiryStatus(InquiryStatus.INQUIRY.getCode());
                    redisUtils.set(
                            RedisKey.INQUIRY.getName()+busId,
                            Opposite.SINGLE,
                            DateUtils.compareSecond(DateUtils.getNow(),byId.getInquiryEndTime())
                    );
                }else if(inquiryStatus.equals(InquiryStatus.INQUIRY.getCode())){
                   temp.setInquiryStatus(InquiryStatus.INQUIRY_OVER.getCode());
                }
                temp.setId(busId);
                selInquiryService.updateById(temp);
            }else{
                log.warn("查询不到询价主键记录: {}",busId);
            }

        }
    }
}
