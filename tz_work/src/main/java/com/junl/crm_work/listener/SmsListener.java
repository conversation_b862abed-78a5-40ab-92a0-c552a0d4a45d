package com.junl.crm_work.listener;

import com.alibaba.fastjson.JSONObject;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.pojo.work.SelBiddingEntity;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_common.tools.ThreadPoolUtil;
import com.junl.crm_work.service.SelBiddingService;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.status.BusCode;
import com.junl.crm_work.status.PushStatus;
import com.junl.crm_work.threadTask.PushTask;
import com.junl.crm_work.util.BusUtils;
import com.junl.crm_work.wx.WxUtils;
import com.junl.msg.SocketUtils;
import com.junl.msg.common.MsgType;
import com.junl.msg.socket.ChannelSocketEntity;
import com.junl.msg.socket.SocketStatus;
import com.junl.system.UserMsgService;
import com.junl.system.UserService;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 处理短信业务
 */
@Component
@Log4j2
public class SmsListener{

    @Reference(lazy = true)
    private UserMsgService userMsgService;
    @Reference(lazy = true)
    private UserService userService;

    @Autowired
    private SelInquiryService selInquiryService;

    @Autowired
    @Lazy
    private SelBiddingService selBiddingService;

    @Autowired
    private SelCustomerService selCustomerService;

    @Autowired
    private WxUtils wxUtils;

    public void dispose(SysUserMsgEntity sysUserMsgEntity) {

        Integer busCode = sysUserMsgEntity.getBusCode();
        String[] customerIds = sysUserMsgEntity.getMsgAccessory().split(",");
        List<String> readers=new ArrayList<>();
        //公司参与询价
        if(busCode.equals(BusCode.INFORM_COMPANY.getCode())){
            SelInquiryEntity byId = selInquiryService.getById(sysUserMsgEntity.getMsgContent());
            for (String customerId : customerIds) {
                SysUserMsgEntity msg=new SysUserMsgEntity();
                msg.setMsgId(SnowFlake.getUUId());
                msg.setBusCode(busCode);
                msg.setReader(customerId);
                msg.setMsgType(MsgType.LINK.getCode());
                msg.setMsgAccessory(sysUserMsgEntity.getMsgContent());
                msg.setMsgTitle("商品询价");
                msg.setMsgContent("诚邀你参加中海泰州石化"+byId.getInquiryName()+"销售计划的商品询价");
                msg.setCompanyId(sysUserMsgEntity.getCompanyId());
//                userMsgService.saveMsg(msg);
                userMsgService.saveHistory(msg);
                readers.add(customerId);
            }
            //公众号推送
            ThreadPoolUtil.runTask(
                    new PushTask(
                            new ArrayList<>(Arrays.asList(customerIds)
                            ),selInquiryService,
                            selBiddingService,
                            selCustomerService,
                            wxUtils,
                            PushStatus.INQUIRE,
                            byId.getId())
            );

            //公司竞价成功
        }else if(busCode.equals(BusCode.TRANSACTION_SUCCESS_COMPANY.getCode())){
            SelBiddingEntity biddingEntity = selBiddingService.getById(sysUserMsgEntity.getMsgContent());
            for (String customerId : customerIds) {
                SysUserMsgEntity msg=new SysUserMsgEntity();
                msg.setMsgId(SnowFlake.getUUId());
                msg.setBusCode(busCode);
                msg.setReader(customerId);
                msg.setMsgType(MsgType.USER.getCode());
                String msgContent = sysUserMsgEntity.getMsgContent();
                msg.setMsgAccessory(sysUserMsgEntity.getMsgContent());
                msg.setMsgTitle("竞价成功");
                msg.setMsgContent("恭喜您参加的中海泰州石化"+biddingEntity.getPlanName()+"第"+msgContent.split(",")[1]+"轮的商品竞价竞拍成功.");
                msg.setCompanyId(sysUserMsgEntity.getCompanyId());
//                userMsgService.saveMsg(msg);
                userMsgService.saveHistory(msg);
                readers.add(customerId);
            }

            ThreadPoolUtil.runTask(
                    new PushTask(
                            new ArrayList<>(Arrays.asList(customerIds)
                            ),
                            selInquiryService,
                            selBiddingService,
                            selCustomerService,
                            wxUtils,
                            PushStatus.SUCCESS,
                            biddingEntity.getId())
            );
            //通知公司参与竞价
        }else if(busCode.equals(BusCode.BIDDING_COMPANY.getCode())){
            SelBiddingEntity biddingEntity = selBiddingService.getById(sysUserMsgEntity.getMsgContent());
            for (String customerId : customerIds) {
                SysUserMsgEntity msg=new SysUserMsgEntity();
                msg.setMsgId(SnowFlake.getUUId());
                msg.setBusCode(busCode);
                msg.setReader(customerId);
                msg.setMsgType(MsgType.LINK.getCode());
                String msgContent = sysUserMsgEntity.getMsgContent();
                msg.setMsgAccessory(sysUserMsgEntity.getMsgContent());
                msg.setMsgTitle("商品竞价");
                msg.setMsgContent("诚邀您参加"+biddingEntity.getPlanName()+"第"+msgContent.split(",")[1]+"轮的商品竞价.");
                msg.setCompanyId(sysUserMsgEntity.getCompanyId());
//                userMsgService.saveMsg(msg);
                userMsgService.saveHistory(msg);
                readers.add(customerId);
            }
            ThreadPoolUtil.runTask(
                    new PushTask(
                            new ArrayList<>(
                                    Arrays.asList(customerIds)
                            ),selInquiryService,
                            selBiddingService,
                            selCustomerService,
                            wxUtils,
                            PushStatus.BIDDING,
                            biddingEntity.getId())
            );
        }else if(busCode.equals(BusCode.START_INQUIRE.getCode())){
            ThreadPoolUtil.runTask(
                    new PushTask(
                            new ArrayList<>(Arrays.asList(customerIds)
                            ),
                            selInquiryService,
                            selBiddingService,
                            selCustomerService,
                            wxUtils,
                            PushStatus.START_INQUIRE,
                            sysUserMsgEntity.getMsgContent())
            );
        }else if(busCode.equals(BusCode.OVER_INQUIRE.getCode())){
            ThreadPoolUtil.runTask(
                    new PushTask(
                            new ArrayList<>(Arrays.asList(customerIds)
                            ),
                            selInquiryService,
                            selBiddingService,
                            selCustomerService,
                            wxUtils,
                            PushStatus.OVER_INQUIRE,
                            sysUserMsgEntity.getMsgContent())
            );
        }else if(busCode.equals(BusCode.START_BIDDING.getCode())){
            ThreadPoolUtil.runTask(
                    new PushTask(
                            new ArrayList<>(Arrays.asList(customerIds)
                            ),
                            selInquiryService,
                            selBiddingService,
                            selCustomerService,
                            wxUtils,
                            PushStatus.START_BIDDING,
                            sysUserMsgEntity.getMsgContent())
            );
        }else if(busCode.equals(BusCode.OVER_BIDDING.getCode())){
            ThreadPoolUtil.runTask(
                    new PushTask(
                            new ArrayList<>(Arrays.asList(customerIds)
                            ),
                            selInquiryService,
                            selBiddingService,
                            selCustomerService,
                            wxUtils,
                            PushStatus.OVER_BIDDING,
                            sysUserMsgEntity.getMsgContent())
            );
        }


        if (!CollectionUtils.isEmpty(readers)) {

            //实时通知有新消息
            ThreadPoolUtil.runTask(
                    () -> {
                        for (String reader : readers) {
                            BusUtils.sendMsg(reader);
                        }
                    }
            );
        }

    }
}
