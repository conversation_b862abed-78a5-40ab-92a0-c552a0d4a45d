package com.junl.crm_work.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.RedisQueueService;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.work.SelBiddingCustomerEntity;
import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_common.pojo.work.SelBiddingEntity;
import com.junl.crm_common.pojo.work.SelBiddingSequenceEntity;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_work.service.*;
import com.junl.crm_work.status.BiddingStatus;
import com.junl.crm_work.status.BusCode;
import com.junl.msg.common.MsgType;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/** 竞价要结束通知 */
@Component
@Log4j2
public class BiddingNotice implements RedisQueueService {

    @Autowired
    private SelBiddingService selBiddingService;

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;

    @Autowired
    private SelBiddingCustomerOfferService selBiddingCustomerOfferService;


    @Autowired
    private SmsListener smsListener;

    @Override
    public void dispose(String key, String busId) {

        if(RedisKey.BIDDING_NOTICE.getName().equals(key+":")){
            SelBiddingEntity byId = selBiddingService.getById(busId);
            if (Assert.notNull(byId)) {
                if (byId.getBiddingStatus().equals(BiddingStatus.BIDDING.getCode())) {

                    //查询出轮次信息
                    SelBiddingSequenceEntity sequenceTemp = selBiddingSequenceService.getOne(
                            new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                            .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, busId)
                                    .eq(SelBiddingSequenceEntity::getSequence, byId.getRoundNum()))
                    );

                    List<SelBiddingCustomerEntity> customerList = selBiddingCustomerService.list(
                            new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                            .eq(SelBiddingCustomerEntity::getSequence, sequenceTemp.getId())
                    );

                    //查询出未报价的客户
                    List<SelBiddingCustomerEntity> list = customerList.stream()
                            .filter(x -> {
                                if (selBiddingCustomerOfferService.count(
                                        new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                                        .eq(SelBiddingCustomerOfferEntity::getRelevanceId, x.getId())
                                ) <= 0) {
                                    return true;
                                } else {
                                    return false;
                                }
                            })
                            .collect(Collectors.toList());

                    /** 竞价通知 */
                    //再次通知商家
                    SysUserMsgEntity msg=new SysUserMsgEntity();
                    msg.setMsgType(MsgType.SMS.getCode());
                    msg.setBusCode(BusCode.BIDDING_COMPANY.getCode());
                    msg.setMsgAccessory(CommonUtil.collect(list,SelBiddingCustomerEntity::getCustomerId,","));
                    msg.setMsgContent(byId.getId()+","+byId.getRoundNum());
                    msg.setCompanyId(byId.getCompanyId());
                    smsListener.dispose(msg);
                }
            }
        }
    }
}
