package com.junl.crm_work.app.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.constant.BiddingModeTypeEnum;
import com.junl.crm_common.constant.FlagStatusEnum;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.ResponseCode;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.app.controller.param.bidding.SeBiddingOfferSaveParam;
import com.junl.crm_work.app.controller.param.bidding.SeBiddingOfferUpdateParam;
import com.junl.crm_work.app.controller.param.inquiry.SelInquiryOfferSaveParam;
import com.junl.crm_work.app.controller.param.inquiry.SelInquiryOfferUpdateParam;
import com.junl.crm_work.service.*;
import com.junl.crm_work.status.BiddingStatus;
import com.junl.crm_work.vo.SelBiddingCustomerOfferVo;
import com.junl.crm_work.vo.SelBiddingSequenceVo;
import com.junl.crm_work.vo.SelBiddingVo;
import com.junl.crm_work.vo.SelInquiryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sell/selBidding")
@Api(tags = "App竞价管理")
public class AppSelBiddingController extends AppParentController {

    @Autowired
    private SelBiddingService selBiddingService;


    // 竞价轮次
    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    // 竞价轮次中间表
    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;


    // 竞价商品表
    @Autowired
    private SelBiddingProductService selBiddingProductService;


    // 竞价明细商品表
    @Autowired
    private SelBiddingCustomerOfferService selBiddingCustomerOfferService;




    /**
     * @description: 分页列表查询
     */
   /* @ApiOperation(value = "销售竞价表--分页列表查询",notes="销售竞价表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelBiddingVo selBiddingVo){
        selBiddingVo.setCompanyId(getCompanyId());
        PageEntity page = selBiddingService.queryPage(selBiddingVo);
        return Result.success(page);
    }*/




    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    /*@ApiOperation(value = "销售竞价查询详情",notes="销售竞价查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selBiddingService.info(id));
    }
*/

    /**
     * @description: 保存 2021-09-01 备份
     * @author: daiqimeng
     */
    /*@ApiOperation(value = "竞价明细--批量保存", notes = "竞价明细--批量保存")
    @PostMapping("/saveOfferBatch")
    public Result saveOfferBatch(@RequestBody SeBiddingOfferSaveParam param){
        //系统参数
        String customerId = getSelCustomerEntity().getId();
        Date nowDate = new Date();
        String sequenceId = param.getSequenceId();
        if(!Assert.notNullCollect(param.getOfferEntities())) {
            return Result.error(ResponseCode.ERROR.getCode(), "数组参数不能为空");
        }
        // 查询轮次
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getById(sequenceId);
        if(sequenceEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        SelBiddingEntity biddingEntity = selBiddingService.getById(sequenceEntity.getBiddingId());
        if(biddingEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        if(!sequenceEntity.getBiddingStatus().equals(BiddingStatus.BIDDING.getCode())) {
            return Result.error(ResponseCode.ERROR.getCode(), "未在竞价时间段内");
        }
        for (int j = 0; j < param.getOfferEntities().size(); j++) {
            SelBiddingCustomerOfferEntity offerEntity = param.getOfferEntities().get(j);
            Double count = offerEntity.getCount();
            if(count<=0) {
                return Result.error(ResponseCode.ERROR.getCode(), "采购量必须大于零");
            }
        }
        String biddingId = biddingEntity.getId();
        // 判断当前竞量是否大于上一轮竞量，竞量只能小于等于上一轮竞量
        Integer nowSequence=sequenceEntity.getSequence();
        Integer temSequence=sequenceEntity.getSequence() - 1;
        if(Assert.notNullCollect(param.getOfferEntities()) && temSequence>0) {
            for (int i = 0; i < param.getOfferEntities().size(); i++) {
                // 判断基价大于上一轮基价
                SelBiddingCustomerOfferEntity offerEntity = param.getOfferEntities().get(i);
                SelBiddingSequenceEntity sequenceLast= selBiddingSequenceService.getOne(new LambdaQueryWrapper<SelBiddingSequenceEntity>().eq(SelBiddingSequenceEntity::getBiddingId, biddingId).eq(SelBiddingSequenceEntity::getSequence, temSequence));
                if(Assert.notNull(sequenceLast)) {
                    String lastSequenceId = sequenceLast.getId();
                    String productId = offerEntity.getProductId();
                    BigDecimal nowPrice = offerEntity.getPrice();
                    // 查询上一轮基价
                    SelBiddingProductEntity  productEntity = selBiddingProductService.getOne(new LambdaQueryWrapper<SelBiddingProductEntity>().eq(SelBiddingProductEntity::getSequenceId, lastSequenceId).eq(SelBiddingProductEntity::getProductId, productId));
                    // 上一轮未参与不能竞量
                    SelBiddingCustomerOfferVo offerVo = new SelBiddingCustomerOfferVo();
                    offerVo.setSequenceId(lastSequenceId);
                    offerVo.setProductId(productId);
                    offerVo.setCustomerId(customerId);
                    SelBiddingCustomerOfferEntity offermaxEntity = selBiddingCustomerOfferService.selectOfferOne(offerVo);
                    if (!Assert.notNull(offermaxEntity)) {
                        if (offerEntity.getCount() > 0) {
                            return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "上一轮未参与不能竞量");
                        }
                    } else {
                        if (nowPrice.compareTo(productEntity.getBaseFee()) > 0) {
                             if (Assert.notNull(offermaxEntity)) {
                                if (offerEntity.getCount() > offermaxEntity.getCount()) {
                                    return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "基价上涨,只能小于等于上一轮竞量");
                                }
                            }
                        }
                    }

                }


            }
        }
        // 查询竞价中间表
        SelBiddingCustomerEntity biddingCustomerEntity = selBiddingCustomerService.getOne(new LambdaQueryWrapper<SelBiddingCustomerEntity>().eq(SelBiddingCustomerEntity::getSequence, sequenceId).eq(SelBiddingCustomerEntity::getCustomerId, customerId));
        if(biddingCustomerEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        String relevanceId = biddingCustomerEntity.getId();
        List<SelBiddingCustomerOfferEntity> biddingOfferEntities = Lists.newArrayList();
        selBiddingCustomerOfferService.lambdaUpdate().eq(SelBiddingCustomerOfferEntity::getRelevanceId, relevanceId).eq(SelBiddingCustomerOfferEntity::getOfferer,customerId).remove();
        biddingOfferEntities.addAll(param.getOfferEntities().stream().map(item->
                {
                    SelBiddingCustomerOfferEntity newOffer = new SelBiddingCustomerOfferEntity();
                    BeanUtils.copyProperties(item, newOffer);
                    newOffer.setId(SnowFlake.getUUId());
                    newOffer.setRelevanceId(relevanceId);
                    newOffer.setOfferer(customerId);
                    newOffer.setCreateTime(nowDate);
                    return newOffer;
                }
        ).collect(Collectors.toList()));
        selBiddingCustomerOfferService.saveBatch(biddingOfferEntities);
        // 修改是否竞价状态
        biddingCustomerEntity.setDetailAddress(param.getDetailAddress());
        biddingCustomerEntity.setRemark(param.getRemark());
        biddingCustomerEntity.setIsBidding(FlagStatusEnum.LEADS.getCode());
        selBiddingCustomerService.updateById(biddingCustomerEntity);
        return Result.success();
    }*/



    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "竞价明细--批量保存", notes = "竞价明细--批量保存")
    @PostMapping("/saveOfferBatch")
    public Result saveOfferBatch(@RequestBody SeBiddingOfferSaveParam param){
        //系统参数
        String customerId =getCustomerId();
        Date nowDate = new Date();
        String sequenceId = param.getSequenceId();
        if(!Assert.notNullCollect(param.getOfferEntities())) {
            return Result.error(ResponseCode.ERROR.getCode(), "数组参数不能为空");
        }
        // 查询轮次
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getById(sequenceId);
        if(sequenceEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        SelBiddingEntity biddingEntity = selBiddingService.getById(sequenceEntity.getBiddingId());
        if(biddingEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        if(!sequenceEntity.getBiddingStatus().equals(BiddingStatus.BIDDING.getCode())) {
            return Result.error(ResponseCode.ERROR.getCode(), "未在竞价时间段内");
        }
        Integer modeType = biddingEntity.getModeType();
        // 1.竞价竞量，2.零销模式
        if(modeType.equals(BiddingModeTypeEnum.VOLUME_COUNT.getCode()) || modeType.equals(BiddingModeTypeEnum.RETAIL_COUNT.getCode())) {
//            for (int j = 0; j < param.getOfferEntities().size(); j++) {
//                SelBiddingCustomerOfferEntity offerEntity = param.getOfferEntities().get(j);
//                Double count = offerEntity.getCount();
//                if (count <= 0) {
//                    return Result.error(ResponseCode.ERROR.getCode(), "采购量必须大于零");
//                }
//            }
            String biddingId = biddingEntity.getId();
            // 判断当前竞量是否大于上一轮竞量，竞量只能小于等于上一轮竞量
            Integer temSequence = sequenceEntity.getSequence() - 1;
            if (Assert.notNullCollect(param.getOfferEntities()) && temSequence > 0) {
                for (int i = 0; i < param.getOfferEntities().size(); i++) {
                    // 判断基价大于上一轮基价
                    SelBiddingCustomerOfferEntity offerEntity = param.getOfferEntities().get(i);
                    SelBiddingSequenceEntity sequenceLast = selBiddingSequenceService.getOne(new LambdaQueryWrapper<SelBiddingSequenceEntity>().and(it->it.eq(SelBiddingSequenceEntity::getBiddingId, biddingId).eq(SelBiddingSequenceEntity::getSequence, temSequence)));
                    if (Assert.notNull(sequenceLast)) {
                        String lastSequenceId = sequenceLast.getId();
                        String productId = offerEntity.getProductId();
                        BigDecimal nowPrice = offerEntity.getPrice();
                        // 查询上一轮基价
                        SelBiddingProductEntity productEntity = selBiddingProductService.getOne(new LambdaQueryWrapper<SelBiddingProductEntity>().and(it->it.eq(SelBiddingProductEntity::getSequenceId, lastSequenceId).eq(SelBiddingProductEntity::getProductId, productId)));
                        // 上一轮未参与不能竞量
                        SelBiddingCustomerOfferVo offerVo = new SelBiddingCustomerOfferVo();
                        offerVo.setSequenceId(lastSequenceId);
                        offerVo.setProductId(productId);
                        offerVo.setCustomerId(customerId);
                        SelBiddingCustomerOfferEntity offermaxEntity = selBiddingCustomerOfferService.selectOfferOne(offerVo);
                        // 上一轮没参与的可以报价 2021-09-15 提出
                        /*if (!Assert.notNull(offermaxEntity)) {
                            if (offerEntity.getCount() > 0) {
                                return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "上一轮未参与不能竞量");
                            }
                        } else {
                            if (nowPrice.compareTo(productEntity.getBaseFee()) > 0) {
                                if (offerEntity.getCount() > offermaxEntity.getCount()) {
                                    return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "基价上涨,只能小于等于上一轮竞量");
                                }
                            }
                        }*/
                        if(Assert.notNull(offermaxEntity)) {
                            // 当前轮基价大于上一轮的基价
                            if (nowPrice.compareTo(productEntity.getBaseFee()) > 0) {
                                if (offerEntity.getCount() > offermaxEntity.getCount()) {
                                    return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "基价上涨,只能小于等于上一轮竞量");
                                }
                            }
                        }

                    }
                }
            }
        } else if (modeType.equals(BiddingModeTypeEnum.PURE_PRICE.getCode())) {
            // 3.纯竞价模式
            String biddingId = biddingEntity.getId();
            // 判断当前竞量是否大于上一轮竞量，竞量只能小于等于上一轮竞量
            Integer temSequence = sequenceEntity.getSequence() - 1;
            for (int j = 0; j < param.getOfferEntities().size(); j++) {
                SelBiddingCustomerOfferEntity offerEntity = param.getOfferEntities().get(j);
                String productId = offerEntity.getProductId();
                // 传入的报价
                BigDecimal parPrice = offerEntity.getPrice();
//                if(parPrice.compareTo(BigDecimal.ZERO)<1) {
//                    return Result.error(ResponseCode.ERROR.getCode(), "采购单价必须大于零");
//                }
                // 查询当前轮基价（底价）
                SelBiddingProductEntity productEntity = selBiddingProductService.getOne(new LambdaQueryWrapper<SelBiddingProductEntity>().eq(SelBiddingProductEntity::getSequenceId, sequenceId).eq(SelBiddingProductEntity::getProductId, productId));
                if (!Assert.notNull(productEntity)) {
                    return Result.error(ResponseCode.ERROR.getCode(), "查询当前轮基价失败");
                }
                // 当前基价
//                BigDecimal nowPrice = productEntity.getBaseFee();
//                if (parPrice.compareTo(nowPrice)<0) {
//                    return Result.error(ResponseCode.ERROR.getCode(), "采购单价必须大于等于基价");
//                }
            }
            if (Assert.notNullCollect(param.getOfferEntities()) && temSequence > 0) {
                for (int i = 0; i < param.getOfferEntities().size(); i++) {
                    // 判断基价大于上一轮基价
                    SelBiddingCustomerOfferEntity offerEntity = param.getOfferEntities().get(i);
                    SelBiddingSequenceEntity sequenceLast = selBiddingSequenceService.getOne(new LambdaQueryWrapper<SelBiddingSequenceEntity>().eq(SelBiddingSequenceEntity::getBiddingId, biddingId).eq(SelBiddingSequenceEntity::getSequence, temSequence));
                    if (Assert.notNull(sequenceLast)) {
                        String lastSequenceId = sequenceLast.getId();
                        String productId = offerEntity.getProductId();
                        BigDecimal nowPrice = offerEntity.getPrice();
                        // 上一轮未参与不能竞量
                        SelBiddingCustomerOfferVo offerVo = new SelBiddingCustomerOfferVo();
                        offerVo.setSequenceId(lastSequenceId);
                        offerVo.setProductId(productId);
                        offerVo.setCustomerId(customerId);
                        SelBiddingCustomerOfferEntity offermaxEntity = selBiddingCustomerOfferService.selectOfferOne(offerVo);
                        // 上一轮没参与的可以报价 2021-09-15 提出
                        /*if (!Assert.notNull(offermaxEntity)) {
                            if (offerEntity.getCount() > 0) {
                                return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "上一轮未参与不能竞量");
                            }
                        } else {
                            // 报价小于上一轮报价
                            if (nowPrice.compareTo(offermaxEntity.getPrice()) < 1) {
                                return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "基价上涨,采购单价必须大于上一轮的报价");
                            }
                        }*/
                        if (Assert.notNull(offermaxEntity)) {
                            // 报价小于上一轮报价
                            if (nowPrice.compareTo(offermaxEntity.getPrice()) < 1) {
                                return Result.error(ResponseCode.ERROR.getCode(), offerEntity.getProductName() + "基价上涨,采购单价必须大于上一轮的报价");
                            }
                        }
                    }
                }
            }
        }
        // 查询竞价中间表
        SelBiddingCustomerEntity biddingCustomerEntity = selBiddingCustomerService.getOne(new LambdaQueryWrapper<SelBiddingCustomerEntity>().and(it->it.eq(SelBiddingCustomerEntity::getSequence, sequenceId).eq(SelBiddingCustomerEntity::getCustomerId, customerId)));
        if(biddingCustomerEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        String relevanceId = biddingCustomerEntity.getId();
        List<SelBiddingCustomerOfferEntity> biddingOfferEntities = Lists.newArrayList();
        //selBiddingCustomerOfferService.lambdaUpdate().eq(SelBiddingCustomerOfferEntity::getRelevanceId, relevanceId).eq(SelBiddingCustomerOfferEntity::getOfferer,customerId).remove();
        biddingOfferEntities.addAll(param.getOfferEntities().stream().map(item->
                {
                    String productId= item.getProductId();
                    selBiddingCustomerOfferService.lambdaUpdate().eq(SelBiddingCustomerOfferEntity::getRelevanceId, relevanceId).eq(SelBiddingCustomerOfferEntity::getProductId,productId).eq(SelBiddingCustomerOfferEntity::getOfferer,customerId).remove();
                    SelBiddingCustomerOfferEntity newOffer = new SelBiddingCustomerOfferEntity();
                    BeanUtils.copyProperties(item, newOffer);
                    newOffer.setId(SnowFlake.getUUId());
                    newOffer.setRelevanceId(relevanceId);
                    newOffer.setOfferer(customerId);
                    newOffer.setCreateTime(nowDate);
                    if (null==newOffer.getCount()) {
                        newOffer.setCount(0.0);
                    }
                    return newOffer;
                }
        ).collect(Collectors.toList()));
        selBiddingCustomerOfferService.saveBatch(biddingOfferEntities);
        // 修改是否竞价状态
        biddingCustomerEntity.setDetailAddress(param.getDetailAddress());
        biddingCustomerEntity.setRemark(param.getRemark());
        biddingCustomerEntity.setIsBidding(FlagStatusEnum.LEADS.getCode());
        selBiddingCustomerService.updateById(biddingCustomerEntity);
        return Result.success();
    }


    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "竞价明细--单个产品修改", notes = "竞价明细--单个产品修改")
    @PostMapping("/updateOffer")
    public Result updateOffer(@RequestBody SeBiddingOfferUpdateParam param) {
        String sequenceId =param.getSequenceId();
        SelBiddingCustomerOfferEntity selBiddingOfferEntity = param.getOfferEntitie();
        String offerId =selBiddingOfferEntity.getId();
        Date nowDate = new Date();
        // 查询询价
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getById(sequenceId);
        if(sequenceEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "请输入有效的ID");
        }
        Date planStartDate= sequenceEntity.getPlanStartDate();
        Date planEndTime= sequenceEntity.getPlanEndTime();
        // 判断时间
        if(nowDate.compareTo(planStartDate)==-1) {
            return Result.error(ResponseCode.ERROR.getCode(), "待竞价");
        }
        if(nowDate.compareTo(planEndTime)==1) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价已结束");
        }
        //系统参数
        String customerId = getCustomerId();
        SelBiddingCustomerOfferEntity offerEntity = selBiddingCustomerOfferService.getById(offerId);
        if(offerEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "请输入有效的ID");
        }
        offerEntity.setOfferer(customerId);
        offerEntity.setCreateTime(nowDate);
        offerEntity.setCount(selBiddingOfferEntity.getCount());
        if (null== offerEntity) {
            offerEntity.setCount(0.0);
        }

        selBiddingCustomerOfferService.updateById(offerEntity);
        return Result.success();
    }




}