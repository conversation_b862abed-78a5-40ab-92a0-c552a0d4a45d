package com.junl.crm_work.app.controller;



import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.constant.DySmsEnum;
import com.junl.crm_common.constant.FlagStatusEnum;
import com.junl.crm_common.constant.SmsTemplateEnum;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SmsRecordEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.*;
import com.junl.crm_common.tools.sms.SmsRestResult;
import com.junl.crm_common.tools.sms.SmsSendParam;
import com.junl.crm_common.tools.sms.TzSmsHelper;
import com.junl.crm_work.app.controller.param.LoginParam;
import com.junl.crm_work.dao.SmsErrorDao;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("app/login")
@Api(value = "App登录管理",tags = {"App登录管理"})
@Slf4j
public class AppLoginController {

	// 缓存
	@Autowired
	private RedisUtils redisUtil;

	//企业用户
	@Autowired
	private SelCustomerService selCustomerService;

	//发短信
	@Autowired
	private TzSmsHelper tzSmsHelper;

	@Autowired
	private UserService userService;

	@Autowired
	private SmsErrorDao smsErrorDao;



	private static final String BASE_CHECK_CODES = "qwertyuiplkjhgfdsazxcvbnmQWERTYUPLKJHGFDSAZXCVBNM1234567890";




	/**
	 * 获取短信登录接口
	 *
	 * @param param
	 * @return
	 */
	@ApiOperation(value = "App登录管理--获取短信登录接口",notes="手机号登录--获取短信登录接口")
	@PostMapping(value = "/sms")
	public Result sms(@RequestBody LoginParam param) {
		String phone = param.getPhone();
		Assert.notNull(phone,"手机号不能为空");
		Integer smsmode=param.getSmsmode();
		log.info(phone);
		//随机数
		String captcha =RandomsUtil.getRandomNumber(6);
		try {

			if (SmsTemplateEnum.SMS_TPL_LOGIN.getCode().equals(smsmode)) {
				//登录模式，校验用户有效性
				SelCustomerEntity customer = selCustomerService.getOne(
						new LambdaQueryWrapper<SelCustomerEntity>()
						.and(e->{
							e.eq(SelCustomerEntity::getDeputyPhone, phone)
									.eq(SelCustomerEntity::getIsDelete, Opposite.ZERO);
						})
				);

				if (ObjectUtils.isEmpty(customer)) {

					SysUserEntity userEntity = userService.getUserInfoByTel(phone);
					if (ObjectUtils.isEmpty(userEntity)) {
						return Result.error("用户不存在");
					}

					if (userEntity.getStatus().equals(Opposite.SINGLE)) {
						return Result.error("用户已冻结");
					}
				}else {
					if (FlagStatusEnum.LEADS.getCode().equals(customer.getIsDelete())) {
						return Result.error("该用户已注销");
					}
					if (FlagStatusEnum.LEADS.getCode().equals(customer.getIsEnable())) {
						return Result.error("该用户已禁用");
					}
				}

				Object object = redisUtil.get(phone);
				if (object != null) {
					return Result.error("验证码仍在有效期内!");
				}
				/**
				 * smsmode 短信模板方式  1 .登录模板、2.注册模板、3.忘记密码模板
				 */
				SmsSendParam smsSendParam = new SmsSendParam();
				smsSendParam.setMessageContent(captcha);
				smsSendParam.setUserNumber(phone); // 手机号
				SmsRestResult result = tzSmsHelper.sendSms(smsSendParam, DySmsEnum.LOGIN_TEMPLATE_CODE);
				// 失败返回
				if (result.getResult() >0) {
					redisUtil.set(phone, captcha, 300);
					smsErrorDao.insert(
							new SmsRecordEntity()
									.setPhone(phone)
									.setStage(1)
									.setDes(
											String.format(
													"%s: %s",
													captcha,
													result.getDescription()
											)
									)
					);

					return Result.error("短信验证码发送失败，请稍后重试");
				}

				smsErrorDao.insert(
						new SmsRecordEntity()
								.setPhone(phone)
								.setStage(0)
								.setDes(
										String.format(
												"%s: %s",
												captcha,
												result.getDescription()
										)
								)
				);
				redisUtil.set(phone, captcha, 300);
			}else
				// 更换手机号
				if (SmsTemplateEnum.SMS_TPL_CHANGE.getCode().equals(smsmode)) {
					//更换手机号
					SelCustomerEntity customer = selCustomerService.getOne(
							new LambdaQueryWrapper<SelCustomerEntity>()
							.and(e->{
								e.eq(SelCustomerEntity::getDeputyPhone, phone)
										.eq(SelCustomerEntity::getIsDelete, Opposite.ZERO);
							})
					);
					if (Assert.notNull(customer)) {
						return Result.error("该用户已存在");
					}
					Object object = redisUtil.get(phone+"update");
					if (object != null) {
						return Result.error("验证码仍在有效期内!");
					}
					SmsSendParam smsSendParam = new SmsSendParam();
					smsSendParam.setMessageContent(captcha);
					smsSendParam.setUserNumber(phone); // 手机号
					SmsRestResult result = tzSmsHelper.sendSms(smsSendParam, DySmsEnum.LOGIN_TEMPLATE_CODE);
					// 失败返回
					if (result.getResult() >0) {
						redisUtil.set(phone+"update", captcha, 300);
						smsErrorDao.insert(
								new SmsRecordEntity()
										.setPhone(phone)
										.setStage(1)
										.setDes(
												String.format(
														"%s: %s",
														captcha,
														result.getDescription()
												)
										)
						);

						return Result.error("短信验证码发送失败，请稍后重试");
					}
					redisUtil.set(phone+"update", captcha, 300);
					smsErrorDao.insert(
							new SmsRecordEntity()
									.setPhone(phone)
									.setStage(1)
									.setDes(
											String.format(
													"%s: %s",
													captcha,
													result.getDescription()
											)
									)
					);
				}
			//验证码5分钟内有效
			//redisUtil.set(phone, captcha, 300);
			return Result.success();
		} catch (Exception e) {
			return Result.error("短信接口未配置，请联系管理员！"+e.getMessage());
		}
	}


	/**
	 * 手机号登录接口
	 *
	 * @param param
	 * @return
	 */
	@ApiOperation(value = "App登录管理--手机号登录接口",notes="手机号登录--手机号登录接口")
	@PostMapping("/phoneLogin")
	public Result phoneLogin(@RequestBody LoginParam param) {
		String phone = param.getPhone();
		Assert.notNull(phone,"手机号不能为空");
		String smscode = param.getCaptcha();
		Assert.notNull(smscode,"验证码不能为空");
		// 判断测试账号,官方审核所需
		if(phone.equals("18285383669") && smscode.equals("111111")) {
			SelCustomerEntity customer = selCustomerService.getOne(
					new LambdaQueryWrapper<SelCustomerEntity>()
							.eq(SelCustomerEntity::getDeputyPhone, phone)
			);
			//校验通过 生成token

			String token = JWTUtil.createAlwaysToken(
					new SysUserEntity()
							.setUserId(customer.getId())
							.setCompanyId(customer.getId())
							.setDeptId(null)
							.setSoleId(null)
							.setUserName(customer.getCustomerName())
			);
			//用户信息
			Map<String,Object> map=new HashMap<>();
			map.put("status",0);
			map.put("token",token);
			//用户信息
			return Result.success(map);
		}


		Integer status=0;

		String token=null;

		//校验用户有效性
		SelCustomerEntity customer = selCustomerService.getOne(
				new LambdaQueryWrapper<SelCustomerEntity>()
						.eq(SelCustomerEntity::getDeputyPhone, phone)
						.eq(SelCustomerEntity::getIsDelete,
								FlagStatusEnum.PRODUCT.getCode())
		);

		if (ObjectUtils.isEmpty(customer)) {
			SysUserEntity userEntity = userService.getUserInfoByTel(phone);
			if (ObjectUtils.isEmpty(userEntity)) {
				return Result.error("用户不存在");
			}

			if (userEntity.getStatus().equals(Opposite.SINGLE)) {
				return Result.error("用户已冻结");
			}

			token=JWTUtil.createAlwaysToken(userEntity);
			status=1;
		}else {
			if(FlagStatusEnum.LEADS.getCode().equals(customer.getIsEnable())) {
				return Result.error("该用户已注销");
			}
			if(FlagStatusEnum.LEADS.getCode().equals(customer.getIsEnable())) {
				return Result.error("该用户已冻结");
			}
			token = JWTUtil.createAlwaysToken(
					new SysUserEntity()
							.setUserId(customer.getId())
							.setCompanyId(customer.getId())
							.setDeptId(null)
							.setSoleId(null)
							.setUserName(customer.getCustomerName())
			);
			status=0;
		}

		Object code = redisUtil.get(phone);
		Assert.notNull(code,"验证码已过期，请重新获取");
		if (!smscode.equals(code)) {
			return Result.error("验证码错误，请重新输入");
		}
		//校验通过 生成token
		Map<String,Object> map=new HashMap<>();
		map.put("status",status);
		map.put("token",token);
		//用户信息
		return Result.success(map);
	}
}