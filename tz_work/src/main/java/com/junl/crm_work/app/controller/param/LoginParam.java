package com.junl.crm_work.app.controller.param;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 登录参数
* @author:      chenlong
**/
@ApiModel(description = "登录参数")
@Data
public class LoginParam {
    @ApiModelProperty(notes = "手机号", allowEmptyValue = true, required = false)
    private  String phone;
    @ApiModelProperty(notes = "短信模板方式  1 .登录模板、2.注册模板、3.忘记密码模板、4、手机号变更", allowEmptyValue = true, required = false)
    private  Integer smsmode;
    @ApiModelProperty(notes = "验证码", allowEmptyValue = true, required = false)
    private String captcha;




}