package com.junl.crm_work.app.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.constant.FlagStatusEnum;
import com.junl.crm_common.constant.InquiryStatusEnum;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.ResponseCode;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.app.controller.param.inquiry.SelInquiryOfferSaveParam;
import com.junl.crm_work.app.controller.param.inquiry.SelInquiryOfferUpdateParam;
import com.junl.crm_work.service.SelInquiryCustomerService;
import com.junl.crm_work.service.SelInquiryOfferService;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.status.InquiryStatus;
import com.junl.crm_work.vo.SelInquiryVo;
import io.swagger.annotations.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: chenlong
 **/
@RestController
@RequestMapping("app/sell/inquiry")
@Api(tags = "App询价管理")
public class AppSelInquiryController  extends AppParentController {


    // 询价主表
    @Autowired
    private SelInquiryService selInquiryService;

    // 询价中间主表
    @Autowired
    private SelInquiryCustomerService selInquiryCustomerService;

    // 询价报价明细表
    @Autowired
    private SelInquiryOfferService selInquiryOfferService;


    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "销售询价主表表--分页列表查询",notes="销售询价主表表--分页列表查询")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "su",response = PageEntity.class),
            @ApiResponse(code = 200,message = "su",response = SelInquiryEntity.class)})
    public Result pageList(@RequestBody SelInquiryVo selInquiryVo){
        selInquiryVo.setCustomerId(getCustomerId());
        PageEntity page = selInquiryService.queryAppPage(selInquiryVo);
        return Result.success(page);
    }


    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售询价主表查询详情",notes="销售询价主表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selInquiryService.infoInquiry(id));
    }




    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "询价明细--批量保存", notes = "询价明细--批量保存")
    @PostMapping("/saveOfferBatch")
    public Result saveOfferBatch(@RequestBody SelInquiryOfferSaveParam param){
        //系统参数
        String customerId = getCustomerId();
        Date nowDate = new Date();
        String inquiryId = param.getInquiryId();
        if(!Assert.notNullCollect(param.getOfferEntities())) {
            return Result.error(ResponseCode.ERROR.getCode(), "数组参数不能为空");
        }
        // 查询询价
        SelInquiryEntity inquiryEntity =selInquiryService.getById(inquiryId);
        if(inquiryEntity == null) {
            return Result.error(ResponseCode.ERROR.getCode(), "询价关联不存在");
        }
        if(!inquiryEntity.getInquiryStatus().equals(InquiryStatusEnum.UNDERWAY.getCode())) {
            return Result.error(ResponseCode.ERROR.getCode(), "未在询价时间段");
        }
        // 查询询价中间表
        SelInquiryCustomerEntity inquiryCustomerEntity = selInquiryCustomerService.getOne(new LambdaQueryWrapper<SelInquiryCustomerEntity>().and(it->it.eq(SelInquiryCustomerEntity::getInquiryId, inquiryId).eq(SelInquiryCustomerEntity::getCustomerId, customerId)));
        if(inquiryCustomerEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "询价关联不存在");
        }
        for (int i = 0; i < param.getOfferEntities().size(); i++) {
            SelInquiryOfferEntity offerEntity = param.getOfferEntities().get(i);
            Double count = offerEntity.getCount();
            Double price = offerEntity.getPrice();
            if(count<=0 || price <=0) {
                return Result.error(ResponseCode.ERROR.getCode(), "采购量及单价必须大于零");
            }
        }
        String relevancyId =inquiryCustomerEntity.getId();
        List<SelInquiryOfferEntity> inquiryOfferEntities = Lists.newArrayList();

        selInquiryOfferService.remove(new LambdaQueryWrapper<SelInquiryOfferEntity>().and(e->e.eq(SelInquiryOfferEntity::getRelevancyId, relevancyId)
        .eq(SelInquiryOfferEntity::getOfferer,customerId)));

        inquiryOfferEntities.addAll(param.getOfferEntities().stream().map(item->
                {
                    SelInquiryOfferEntity newOffer = new SelInquiryOfferEntity();
                    BeanUtils.copyProperties(item, newOffer);
                    newOffer.setId(SnowFlake.getUUId());
                    newOffer.setRelevancyId(relevancyId);
                    newOffer.setOfferer(customerId);
                    newOffer.setCreateTime(nowDate);
                    return newOffer;
                }
        ).collect(Collectors.toList()));
        selInquiryOfferService.saveBatch(inquiryOfferEntities);
        // 修改是否询价状态
        inquiryCustomerEntity.setIsInquiry(FlagStatusEnum.LEADS.getCode());
        selInquiryCustomerService.updateById(inquiryCustomerEntity);
        return Result.success();
    }


    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售询价主表--单个产品修改", notes = "销售询价主表--单个产品修改")
    @PostMapping("/updateOffer")
    public Result updateOffer(@RequestBody SelInquiryOfferUpdateParam param) {
        String inquiryId =param.getInquiryId();
        SelInquiryOfferEntity selInquiryOfferEntity = param.getOfferEntitie();
        String offerId =param.getOfferEntitie().getId();
        Date nowDate = new Date();
        // 查询询价
        SelInquiryEntity inquiryEntity = selInquiryService.getById(inquiryId);
        if(inquiryEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "请输入有效的ID");
        }
        Date inquiryStartDate= inquiryEntity.getInquiryStartDate();
        Date inquiryEndTime= inquiryEntity.getInquiryEndTime();
        // 判断时间
        if(nowDate.compareTo(inquiryStartDate)==-1) {
            return Result.error(ResponseCode.ERROR.getCode(), "待询价");
        }
        if(nowDate.compareTo(inquiryEndTime)==1) {
            return Result.error(ResponseCode.ERROR.getCode(), "询价已结束");
        }
        //系统参数
        String customerId = getCustomerId();
        Validate.startValidate(new NotNullVerification(selInquiryOfferEntity,null));
        SelInquiryOfferEntity offerEntity = selInquiryOfferService.getById(offerId);
        if(offerEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "请输入有效的ID");
        }
        offerEntity.setOfferer(customerId);
        offerEntity.setCreateTime(nowDate);
        selInquiryOfferService.updateById(offerEntity);
        return Result.success();
    }


}