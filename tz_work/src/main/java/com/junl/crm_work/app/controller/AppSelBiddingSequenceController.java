package com.junl.crm_work.app.controller;

import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.constant.BiddingSequenceAppStatusEnum;
import com.junl.crm_common.pojo.work.SelBiddingSequenceEntity;
import com.junl.crm_work.service.SelBiddingSequenceService;
import com.junl.crm_work.vo.SelBiddingSequenceVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sell/selBiddingSequence")
@Api(tags = "App竞价轮次管理")
public class AppSelBiddingSequenceController extends AppParentController {

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "竞价轮次关联表表--分页列表查询", notes = "竞价轮次关联表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelBiddingSequenceVo selBiddingSequenceVo) {
        //系统参数
        String customerId = getCustomerId();
        selBiddingSequenceVo.setCustomerId(customerId);
        PageEntity page = selBiddingSequenceService.queryAppPage(selBiddingSequenceVo);
        return Result.success(page);
    }


    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "竞价轮次关联表表--报量记录分页列表查询", notes = "竞价轮次关联表表--报量记录分页列表查询")
    @PostMapping("/pageAppList")
    public Result pageAppList(@RequestBody SelBiddingSequenceVo selBiddingSequenceVo) {
        //系统参数
        String customerId = getCustomerId();
        selBiddingSequenceVo.setCustomerId(customerId);
        PageEntity page = selBiddingSequenceService.queryAppList(selBiddingSequenceVo);
        return Result.success(page);
    }



    /**
     * @description: 查询所有列表
     */
    /*@ApiOperation(value = "竞价轮次关联表表--列表查询", notes = "竞价轮次关联表表--列表查询")
    @PostMapping("/getList")
    public Result getList() {
        List<SelBiddingSequenceEntity> list = selBiddingSequenceService.list();
        return Result.success(list);
    }*/

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "竞价轮次关联表查询详情", notes = "竞价轮次关联表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id", required = true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") Long id) {
        return Result.success(selBiddingSequenceService.getById(id));
    }

}