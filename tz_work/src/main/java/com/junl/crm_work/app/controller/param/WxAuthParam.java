package com.junl.crm_work.app.controller.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 微信授权参数
* @author:      chenlong
**/
@ApiModel(description = "微信授权参数")
@Data
public class WxAuthParam {
    @ApiModelProperty(notes = "客户端 1 .企业小程序、2.员工小程序", allowEmptyValue = true, required = false)
    private  Integer type = 1;
    @ApiModelProperty(notes = "明文:包括敏感数据在内的完整用户信息的加密数据", allowEmptyValue = true, required = false)
    private  String encryptedData;
    @ApiModelProperty(notes = "加密算法的初始向量", allowEmptyValue = true, required = false)
    private  String iv;
    @ApiModelProperty(notes = "用户秘钥", allowEmptyValue = true, required = false)
    private String sessionKey;




}