package com.junl.crm_work.app.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.status.ResponseCode;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.*;
import com.junl.crm_work.vo.SelBiddingProductVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sell/selBiddingProduct")
@Api(tags = "App竞价轮次商品管理")
public class AppSelBiddingProductController extends AppParentController {

    @Autowired
    private SelBiddingProductService selBiddingProductService;

    @Autowired
    private SelBiddingService selBiddingService;

    // 竞价轮次
    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    // 竞价轮次中间表
    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;

    // 竞价报量
    @Autowired
    private SelBiddingCustomerOfferService selBiddingCustomerOfferService;

    // 竞价报量
    @Autowired
    private SelLogService selLogService;

    /**
     * @description: 分页列表查询
     */
    /*@ApiOperation(value = "竞价商品详情表表--分页列表查询", notes = "竞价商品详情表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelBiddingProductVo selBiddingProductVo) {
        PageEntity page = selBiddingProductService.queryPage(selBiddingProductVo);
        return Result.success(page);
    }*/

    /**
     * @description: 查询所有列表-再次提交
     */
    @ApiOperation(value = "竞价商品详情表表--列表查询", notes = "竞价商品详情表表--列表查询")
    @PostMapping("/getList")
    public Result getList(@RequestBody SelBiddingProductVo selBiddingProductVo) {

        SelBiddingProductVo result = new SelBiddingProductVo();
        //系统参数
        String customerId = getCustomerId();
        String sequenceId = selBiddingProductVo.getSequenceId();
        // 查询轮次
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getById(sequenceId);
        if(sequenceEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        //SelBiddingEntity biddingEntity = selBiddingService.getById(sequenceEntity.getBiddingId());
        SelBiddingEntity biddingEntity = selBiddingService.getAppinfo(sequenceEntity.getBiddingId());
        if(biddingEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        result.setCompanyName(biddingEntity.getCompanyName());
        result.setModeType(biddingEntity.getModeType());
        result.setTrafficMode(biddingEntity.getTrafficMode());
        result.setDeliveryStartDate(biddingEntity.getDeliveryStartDate());
        result.setDeliveryEndTime(biddingEntity.getDeliveryEndTime());
        result.setQuality(biddingEntity.getQuality());
        result.setDescribe(biddingEntity.getDescribe());
        List<SelBiddingProductEntity> list = selBiddingProductService.list(new QueryWrapper<SelBiddingProductEntity>().eq("sequence_id",sequenceId));
        //SelBiddingProductVo productVo =new SelBiddingProductVo();
        //productVo.setSequenceId(sequenceId);
        //List<SelBiddingProductEntity> list = selBiddingProductService.getAppList(productVo);
        // 查询竞价中间表
        SelBiddingCustomerEntity biddingCustomerEntity = selBiddingCustomerService.getOne(new LambdaQueryWrapper<SelBiddingCustomerEntity>().eq(SelBiddingCustomerEntity::getSequence, sequenceId).eq(SelBiddingCustomerEntity::getCustomerId, customerId));
        if(biddingCustomerEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "竞价关联不存在");
        }
        result.setDetailAddress(biddingCustomerEntity.getDetailAddress());
        result.setRemark(biddingCustomerEntity.getRemark());
        List<SelBiddingProductEntity> listProduct =  Lists.newArrayList();
        listProduct.addAll(list.stream().map(item->
                {
                    SelBiddingProductEntity newOffer = new SelBiddingProductEntity();
                    BeanUtils.copyProperties(item, newOffer);
                    Double count = 0d;
                    Double price = 0d;
                    String offerId="";
                    SelBiddingCustomerOfferEntity offerEntity = selBiddingCustomerOfferService.getOne(new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>().eq(SelBiddingCustomerOfferEntity::getRelevanceId, biddingCustomerEntity.getId()).eq(SelBiddingCustomerOfferEntity::getProductId, item.getProductId()).eq(SelBiddingCustomerOfferEntity::getOfferer, customerId));
                    if(Assert.notNull(offerEntity)) {
                        count =offerEntity.getCount();
                        price =offerEntity.getPrice().doubleValue();
                        offerId = offerEntity.getId();
                    }
                    newOffer.setCount(count);
                    newOffer.setPrice(price);
                    newOffer.setOfferId(offerId);
                    return newOffer;
                }
        ).collect(Collectors.toList()));
        //return Result.success(list);
        result.setProductList(listProduct);
        return Result.success(result);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "竞价商品详情表查询详情", notes = "竞价商品详情表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id", required = true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") Long id) {
        return Result.success(selBiddingProductService.getById(id));
    }




    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    /*@ApiOperation(value = "销售报价主表--删除", notes = "销售报价主表--删除")
    @PostMapping("/offer/delete")
    public Result offerDelete(@RequestBody String id){*/
    @ApiOperation(value = "销售报价主表--删除", notes = "销售报价主表--删除")
    @ApiImplicitParams(@ApiImplicitParam(name = "offerId", value = "offerId", required = true))
    @GetMapping("/offer/delete")
    public Result offerDelete(@RequestParam("offerId") Long offerId) {
        Assert.isNull(offerId,"传入的ID不能为空");
        SelBiddingCustomerOfferEntity offerEntity = selBiddingCustomerOfferService.getById(offerId);
        if(!Assert.notNull(offerEntity)) {
            return Result.error(ResponseCode.ERROR.getCode(), "请传入有效的ID");
        }
        selBiddingCustomerOfferService.removeById(offerId);
        // 记录日志
        SelLogEntity selLogEntity =new SelLogEntity();
        selLogEntity.setLogId(SnowFlake.getUUId());
        selLogEntity.setLogDescribe("竞价管理");
        selLogEntity.setLogType("撤销");
        selLogEntity.setLogParam(offerId+"");
        selLogEntity.setLogContent(JSONObject.toJSONString(offerEntity));
        String customerId = getCustomerId();
        selLogEntity.setCreateBy(customerId);
        selLogEntity.setCreateTime(new Date());
        selLogService.save(selLogEntity);
        return Result.success();
    }


}