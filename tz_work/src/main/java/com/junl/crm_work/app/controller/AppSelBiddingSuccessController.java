package com.junl.crm_work.app.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.pojo.work.SelBiddingSuccessEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_work.service.SelBiddingSuccessService;
import com.junl.crm_work.vo.SelBiddingSuccessVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sell/selBiddingSuccess")
@Api(tags = "App竞价交易成功管理")
public class AppSelBiddingSuccessController extends AppParentController {

    @Autowired
    private SelBiddingSuccessService selBiddingSuccessService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "竞价企业交易成功表表--分页列表查询", notes = "竞价企业交易成功表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelBiddingSuccessVo selBiddingSuccessVo) {
        selBiddingSuccessVo.setOfferId(getCustomerId());
        PageEntity page = selBiddingSuccessService.queryAppPage(selBiddingSuccessVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "竞价企业交易成功表表--列表查询",notes="竞价企业交易成功表表--列表查询")
    @PostMapping("/getList")
    public Result getList(@RequestBody SelBiddingSuccessVo selBiddingSuccessVo){
        return selBiddingSuccessService.queryList(
                selBiddingSuccessVo,
                new SelCustomerEntity()
                        .setId(getCustomerId())
        );
    }


    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    /*@ApiOperation(value = "竞价企业交易成功表查询详情", notes = "竞价企业交易成功表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id", required = true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") Long id) {
        return Result.success(selBiddingSuccessService.getById(id));
    }*/


}