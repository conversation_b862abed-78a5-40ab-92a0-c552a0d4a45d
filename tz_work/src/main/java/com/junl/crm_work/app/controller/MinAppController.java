package com.junl.crm_work.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.WxCustomerEntity;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.app.controller.param.WxAuthParam;
import com.junl.crm_work.service.WxCustomerService;
import com.junl.crm_work.util.AESUtils;
import com.junl.crm_work.util.GetPostUntil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Date;


/**
 * @author: chenlong
 **/
@Log4j2
@Api(value = "App小程序登录授权",tags = {"App小程序登录授权"})
@RestController
@RequestMapping("app/wx/config")
public class MinAppController {

    @Autowired
    private WxCustomerService wxCustomerService;


    // 微信小程序APPId;
    @Value("${wxminapp.appId}")
    private String appId;

    // 微信小程序appSecret;
    @Value("${wxminapp.appSecret}")
    private String appSecret;

    // 微信小程序wxLoginUrl;
    @Value("${wxminapp.wxLoginUrl}")
    private String wxLoginUrl;



    /**
     * 微信小程序登录获取
     * 获取session_key
     * @param
     * @return
     */
    @ResponseBody
    @ApiOperation(value = "App小程序登录授权--code获取session_key",notes="小程序登录授权--code获取session_key")
    @PostMapping("/initWxLogin")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "js_code", value = "登录时获取的code",paramType = "form", dataType = "string", required = true)
    })
    public Result initWxLogin(@RequestParam(value = "js_code", required = true) String js_code) {

        //测试数据code
//    js_code = "081ZQ3f91fr9VM1HYdb91y93f91ZQ3fU";
        //微信获取session_key接口地址
        //String wxLoginUrl = "https://api.weixin.qq.com/sns/jscode2session";
        //接口参数
        String param = "appid="+appId+"&secret="+appSecret+"&js_code=" + js_code + "&grant_type=authorization_code";
        //调用获取session_key接口 请求方式get
        String jsonString = GetPostUntil.sendGet(wxLoginUrl, param);
        System.out.println(jsonString);
        //因为json字符串是大括号包围，所以用JSONObject解析
        JSONObject json = JSONObject.parseObject(jsonString);
        //json解析session_key值
        String session_key = json.getString("session_key");
        System.out.println("session_key：" + session_key);
        // 存到数据库
        WxCustomerEntity wxCustomerEntity =new WxCustomerEntity();
        wxCustomerEntity.setId(SnowFlake.getUUId());
        wxCustomerEntity.setUnionId(json.getString("unionid"));
        wxCustomerEntity.setOpenId(json.getString("openid"));
        wxCustomerEntity.setCreateTime(new Date());
        wxCustomerService.save(wxCustomerEntity);
        //返回给前端
        return Result.success(session_key);
    }


    /**
     * 解密小程序用户敏感数据
     *
     * @param param
     * @return
     */
    @ResponseBody
    @ApiOperation(value = "App小程序登录授权--获取手机号",notes="小程序登录授权--获取手机号")
    @PostMapping(value = "/decodePhone")
    public Result decodePhone(@RequestBody WxAuthParam param) {
        try {
            // 客户端 1 .企业小程序、2.员工小程序
            Integer type = param.getType();
            // 包括敏感数据在内的完整用户信息的加密数据
            String encryptedData = param.getEncryptedData();
            // 加密算法的初始向量
            String iv = param.getIv();
            // 用户秘钥
            String sessionKey = param.getSessionKey();
            //AESUtils微信获取手机号解密工具类
            AESUtils aes = new AESUtils();
            //调用AESUtils工具类decrypt方法解密获取json串
            byte[] resultByte = aes.decrypt(Base64.decodeBase64(encryptedData), Base64.decodeBase64(sessionKey), Base64.decodeBase64(iv));
            //判断返回参数是否为空
            if (null != resultByte && resultByte.length > 0) {
                String jsons = new String(resultByte, "UTF-8");
                System.out.println(jsons);
                JSONObject json = JSONObject.parseObject(jsons);
                //json解析phoneNumber值
                String phoneNumber = json.getString("phoneNumber");
                System.out.println("phoneNumber：" + phoneNumber);
                return Result.success(phoneNumber);
            }
        }catch (Exception e) {
            log.info("获取手机失败"+e.getMessage());
        }
        return Result.error(500,"session_key:失败");
    }
}