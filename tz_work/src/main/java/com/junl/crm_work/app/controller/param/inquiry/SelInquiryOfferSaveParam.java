package com.junl.crm_work.app.controller.param.inquiry;

import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* @description: 询价明细批量添加
* @author:      chenlong
**/
@ApiModel(description = "询价明细批量添加")
@Data
public class SelInquiryOfferSaveParam {
    @ApiModelProperty(notes = "询价ID", allowEmptyValue = true, required = false)
    private  String inquiryId;

    @ApiModelProperty(notes = "询价明细数组对象", allowEmptyValue = true, required = false)
    private List<SelInquiryOfferEntity> offerEntities;




}