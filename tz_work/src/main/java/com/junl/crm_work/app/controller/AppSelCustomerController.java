package com.junl.crm_work.app.controller;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.constant.FlagStatusEnum;
import com.junl.crm_common.pojo.work.SelBiddingCustomerEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.*;
import com.junl.crm_work.app.controller.param.LoginParam;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.crm_work.status.BusPrefix;
import com.junl.crm_work.util.CodeGenerate;
import com.junl.crm_work.vo.SelCustomerVo;
import io.swagger.annotations.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;

/**
 * @author: chenlong
 **/
@RestController
@RequestMapping("app/sell/customer")
@Api(tags = "App企业管理")
public class AppSelCustomerController extends AppParentController {

    @Autowired
    private SelCustomerService selCustomerService;

    // 缓存
    @Autowired
    private RedisUtils redisUtil;

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "企业表查询详情",notes="企业表查询详情")
    @GetMapping("/info")
    public Result info(){
        String customerId = getCustomerId();
        return Result.success(selCustomerService.getById(customerId));
    }


    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "企业表--修改", notes = "企业表--修改")
    @PostMapping("/update")
    public Result update(@RequestBody SelCustomerEntity selCustomerEntity){
        String customerId = getCustomerId();
        SelCustomerEntity customer = selCustomerService.getById(customerId);
        if(customer==null)  {
            return Result.error("该用户不存在");
        }
        BeanUtils.copyProperties(selCustomerEntity, customer);
        customer.setId(customerId);
        customer.setUpdateTime(new Date());
        selCustomerService.updateAppCustomerById(customer);
        return Result.success();
    }

    /**
     * 手机号登录接口
     * @param param
     * @return
     */
    @ApiOperation(value = "企业表--手机号变更",notes="企业表--手机号变更")
    @PostMapping("/phoneChange")
    public Result phoneChange(@RequestBody LoginParam param) {
        //系统参数
        String customerId =getCustomerId();
        String phone = param.getPhone();
        Assert.notNull(phone,"手机号不能为空");
        Date nowDate = new Date();
        String smscode = param.getCaptcha();
        Assert.notNull(smscode,"验证码不能为空");
        //校验用户有效性
        SelCustomerEntity customer = selCustomerService.getById(customerId);
        if(!Assert.notNull(customer)) {
            return Result.error("该企业不存在");
        }
        if(customer.getDeputyPhone().equals(phone)) {
            return Result.error("新手机号与旧手机号相同");
        }
        // 判断手机号是否存在
        List<SelCustomerEntity> customerEntities = selCustomerService.list(new LambdaQueryWrapper<SelCustomerEntity>().ne(SelCustomerEntity::getId, customer.getId()).eq(SelCustomerEntity::getDeputyPhone, phone));
        if(Assert.notNullCollect(customerEntities)) {
            return Result.error("该手机号已存在");
        }
        //Object code = redisUtil.get(phone);
        Object code = redisUtil.get(phone+"update");
        Assert.notNull(code,"验证码已过期，请重新获取");
        if (!smscode.equals(code)) {
            return Result.error("验证码错误，请重新输入");
        }
        customer.setDeputyPhone(phone);
        customer.setUpdateTime(nowDate);
        selCustomerService.updateById(customer);
        return Result.success();
    }



}