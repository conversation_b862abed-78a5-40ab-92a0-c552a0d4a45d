package com.junl.crm_work.app.controller;

import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.pojo.work.WxCustomerEntity;
import com.junl.crm_work.service.WxCustomerService;
import com.junl.crm_work.work.vo.WxCustomerVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @author: chenlong
 **/
@RestController
@RequestMapping("app/wx/customer")
@Api(tags = "App微信客户表接口")
public class AppWxCustomerController{

    @Autowired
    private WxCustomerService wxCustomerService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "微信客户表表--分页列表查询", notes = "微信客户表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody WxCustomerVo wxCustomerVo) {
        PageEntity page = wxCustomerService.queryPage(wxCustomerVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "微信客户表表--列表查询", notes = "微信客户表表--列表查询")
    @PostMapping("/getList")
    public Result getList() {
        List<WxCustomerEntity> list = wxCustomerService.list();
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "微信客户表查询详情", notes = "微信客户表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id", required = true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id) {
        return Result.success(wxCustomerService.getById(id));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "微信客户表列表--新增", notes = "微信客户表列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody WxCustomerEntity wxCustomerEntity) {
        wxCustomerService.save(wxCustomerEntity);
        return Result.success();
    }

    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "微信客户表--修改", notes = "微信客户表--修改")
    @PostMapping("/update")
    public Result update(@RequestBody WxCustomerEntity wxCustomerEntity) {
        wxCustomerService.updateById(wxCustomerEntity);
        return Result.success();
    }

    /**
     * @description: 根据主键删除WxCustomerController
     * @author: daiqimeng
     */


    @ApiOperation(value = "微信客户表--删除", notes = "微信客户表--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] ids) {
        wxCustomerService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }
}