package com.junl.crm_work.app.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_work.service.SelInquiryOfferService;
import com.junl.crm_work.vo.SelInquiryOfferVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sell/selInquiryOffer")
@Api(tags = "App企业询价报量管理")
public class AppSelInquiryOfferController extends AppParentController {

    @Autowired
    private SelInquiryOfferService selInquiryOfferService;

    /**
     * @description: 分页列表查询
     */
    /*@ApiOperation(value = "企业对于询价的报价报量记录表表--分页列表查询", notes = "企业对于询价的报价报量记录表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelInquiryOfferVo selInquiryOfferVo) {
        PageEntity page = selInquiryOfferService.queryPage(selInquiryOfferVo);
        return Result.success(page);
    }*/

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "App企业询价报量管理--列表查询", notes = "App企业询价报量管理--列表查询")
    @PostMapping("/getList")
    public Result getList(@RequestBody SelInquiryOfferVo selInquiryOfferVo) {
        return selInquiryOfferService.queryList(
                selInquiryOfferVo,
                new SelCustomerEntity()
                        .setId(getCustomerId())
        );
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "App企业询价报量管理-查询明细", notes = "App企业询价报量管理-查询明细")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id", required = true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") Long id) {
        return Result.success(selInquiryOfferService.getById(id));
    }


}