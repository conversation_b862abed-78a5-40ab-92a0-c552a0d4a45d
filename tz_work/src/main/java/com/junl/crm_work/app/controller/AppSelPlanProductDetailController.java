package com.junl.crm_work.app.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_common.status.ResponseCode;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.service.SelInquiryCustomerService;
import com.junl.crm_work.service.SelInquiryOfferService;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.service.SelPlanProductDetailService;
import com.junl.crm_work.vo.SelPlanProductDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sell/selPlanProductDetail")
@Api(tags = "App询价商品管理")
public class AppSelPlanProductDetailController extends AppParentController {

    @Autowired
    private SelPlanProductDetailService selPlanProductDetailService;


    // 询价报量
    @Autowired
    private SelInquiryService selInquiryService;

    // 询价中间主表
    @Autowired
    private SelInquiryCustomerService selInquiryCustomerService;

    // 询价报量
    @Autowired
    private SelInquiryOfferService selInquiryOfferService;
    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "App询价商品管理--列表查询",notes="App询价商品管理--列表查询")
    @PostMapping("/getList")
    public Result getList(@RequestBody SelPlanProductDetailVo selPlanProductDetailVo){
        //系统参数
        String customerId = getCustomerId();
        String planId =selPlanProductDetailVo.getPlanId();
        List<SelPlanProductDetailEntity> list = selPlanProductDetailService.list(new QueryWrapper<SelPlanProductDetailEntity>().eq("plan_id", planId));
        // 查询询价主表
        SelInquiryEntity selInquiryEntity = selInquiryService.getOne(new QueryWrapper<SelInquiryEntity>().eq("plan_id", planId));
        if(selInquiryEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "询价关联不存在");
        }
        String inquiryId = selInquiryEntity.getId();
        // 查询询价中间表
        SelInquiryCustomerEntity inquiryCustomerEntity = selInquiryCustomerService.getOne(new LambdaQueryWrapper<SelInquiryCustomerEntity>().and(it->it.eq(SelInquiryCustomerEntity::getInquiryId, inquiryId).eq(SelInquiryCustomerEntity::getCustomerId, customerId)));
        if(inquiryCustomerEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "询价关联不存在");
        }
        List<SelPlanProductDetailEntity> listProduct =  Lists.newArrayList();
        listProduct.addAll(list.stream().map(item->
                {
                    SelPlanProductDetailEntity newOffer = new SelPlanProductDetailEntity();
                    BeanUtils.copyProperties(item, newOffer);
                    Double count = 0d;
                    Double price = 0d;
                    SelInquiryOfferEntity offerEntity = selInquiryOfferService.getOne(new LambdaQueryWrapper<SelInquiryOfferEntity>().and(it->it.eq(SelInquiryOfferEntity::getRelevancyId, inquiryCustomerEntity.getId()).eq(SelInquiryOfferEntity::getProductId, item.getProductId()).eq(SelInquiryOfferEntity::getOfferer, customerId)));
                    if(Assert.notNull(offerEntity)) {
                        count =offerEntity.getCount();
                        price= offerEntity.getPrice();
                    }
                    newOffer.setCount(count);
                    newOffer.setPrice(price);
                    return newOffer;
                }
        ).collect(Collectors.toList()));
        return Result.success(listProduct);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "App询价商品管理--查询详情",notes="App询价商品管理--查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(selPlanProductDetailService.getById(id));
    }


}