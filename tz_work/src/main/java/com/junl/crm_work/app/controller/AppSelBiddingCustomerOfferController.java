package com.junl.crm_work.app.controller;


import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_work.service.SelBiddingCustomerOfferService;
import com.junl.crm_work.vo.SelBiddingCustomerOfferVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sel/selBiddingCustomerOffer")
@Api(tags = "App竞价报量管理")
public class AppSelBiddingCustomerOfferController extends AppParentController {

    @Autowired
    private SelBiddingCustomerOfferService selBiddingCustomerOfferService;

    /**
     * @description: 分页列表查询
     */
    /*@ApiOperation(value = "竞价企业报量表表--分页列表查询", notes = "竞价企业报量表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SelBiddingCustomerOfferVo selBiddingCustomerOfferVo) {
        PageEntity page = selBiddingCustomerOfferService.queryPage(selBiddingCustomerOfferVo);
        return Result.success(page);
    }*/

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "竞价企业报量表表--列表查询", notes = "竞价企业报量表表--列表查询")
    @PostMapping("/getList")
    public Result getList(@RequestBody SelBiddingCustomerOfferVo selBiddingCustomerOfferVo) {
        return selBiddingCustomerOfferService.queryList(
                selBiddingCustomerOfferVo,
                new SelCustomerEntity()
                        .setId(getCustomerId())
        );
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "竞价企业报量表查询详情", notes = "竞价企业报量表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id", required = true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") Long id) {
        return Result.success(selBiddingCustomerOfferService.getById(id));
    }




}