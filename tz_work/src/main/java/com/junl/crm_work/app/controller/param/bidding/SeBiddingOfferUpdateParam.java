package com.junl.crm_work.app.controller.param.bidding;

import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 竞价明细修改
* @author:      chenlong
**/
@ApiModel(description = "竞价明细修改")
@Data
public class SeBiddingOfferUpdateParam {
    @ApiModelProperty(notes = "轮次序列id",allowEmptyValue = true, required = false)
    private String sequenceId;

    @ApiModelProperty(notes = "竞价明细对象", allowEmptyValue = true, required = false)
    private SelBiddingCustomerOfferEntity offerEntitie;




}