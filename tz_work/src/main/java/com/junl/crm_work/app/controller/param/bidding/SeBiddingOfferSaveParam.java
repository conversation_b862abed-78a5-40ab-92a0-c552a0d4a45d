package com.junl.crm_work.app.controller.param.bidding;

import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* @description: 竞价明细批量添加
* @author:      chenlong
**/
@ApiModel(description = "竞价明细批量添加")
@Data
public class SeBiddingOfferSaveParam {
    @ApiModelProperty(notes = "轮次序列id",allowEmptyValue = true, required = false)
    private String sequenceId;

    @ApiModelProperty(notes = "竞价明细数组对象", allowEmptyValue = true, required = false)
    private List<SelBiddingCustomerOfferEntity> offerEntities;

    @ApiModelProperty(notes = "详细地址", required = false)
    private String detailAddress;

    @ApiModelProperty(notes = "备注", required = false)
    private String remark;


}