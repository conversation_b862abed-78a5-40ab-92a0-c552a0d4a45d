package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysHistoryMsgEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_work.bo.InitFlowBO;
import com.junl.crm_work.dao.FlowSelPlanProductDetailDao;
import com.junl.crm_work.dao.SelApprovalDefinitionDao;
import com.junl.crm_work.dao.SelBiddingDao;
import com.junl.crm_work.util.BusUtils;
import com.junl.msg.common.MsgType;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.common.PageEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.junl.crm_work.dao.SelPlanDao;
import com.junl.crm_work.service.*;
import com.junl.crm_work.status.*;
import com.junl.crm_work.util.CodeGenerate;
import com.junl.crm_work.vo.SelPlanVo;
import com.junl.system.UserMsgService;
import com.junl.system.UserService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelPlanServiceImpl
        extends ServiceImpl<SelPlanDao, SelPlanEntity>
        implements SelPlanService
{


    @Autowired
    private CodeGenerate codeGenerate;

    @Autowired
    private SelPlanProductDetailService selPlanProductDetailService;


    @Autowired
    private SelApprovalDefinitionDao selApprovalDefinitionDao;

    @Autowired
    private SelApprovalUnderwayService selApprovalUnderwayService;

    @Autowired
    private SelApprovalFlowService selApprovalFlowService;

    @Reference
    private UserService userService;

    @Reference
    private UserMsgService userMsgService;

    @Autowired
    private SelProductService selProductService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SelBiddingDao selBiddingDao;

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;

    @Autowired
    private SelBiddingCustomerOfferService selBiddingCustomerOfferService;

    @Autowired
    private SelBiddingProductService selBiddingProductService;

    @Autowired
    private SelBiddingFeeRecordService selBiddingFeeRecordService;

    @Autowired
    private SelBiddingSuccessService selBiddingSuccessService;

    @Autowired
    private SelBiddingSuccessProductService selBiddingSuccessProductService;

    @Autowired
    private FlowSelPlanService flowSelPlanService;

    @Autowired
    private FlowSelBiddingService flowSelBiddingService;

    @Autowired
    private FlowSelBiddingProductService flowSelBiddingProductService;

    @Autowired
    private FlowSelBiddingCustomerService flowSelBiddingCustomerService;

    @Autowired
    private FlowSelBiddingSuccessService flowSelBiddingSuccessService;

    @Autowired
    private FlowSelBiddingSuccessProductService flowSelBiddingSuccessProductService;





    /**
     * @description: 分页查询列表
     * @param selPlanVo
     */
    @Override
    public PageEntity queryPage(SelPlanVo selPlanVo) {
        if (Assert.notNull(selPlanVo.getUserId())) {
            SysUserEntity userInfo = userService.getUserInfo(selPlanVo.getUserId());
            Assert.notNull(userInfo,"系统无法识别的账户信息");
            if(Assert.notNull(userInfo.getDataId())&&!Opposite.ZERO.equals(userInfo.getDataId())){
                List<String> users = userService.getUsers(userInfo.getDataId());
                selPlanVo.setUserIds(users);
            }else{
                Integer identityCode = userInfo.getIdentityCode();

                if (identityCode.equals(IdentityStatus.DEPT_ADMIN.getCode())) {
                    List<String> deptUsers = userService.getDeptUsers(userInfo.getDeptId());
                    selPlanVo.setUserIds(deptUsers);
                }else if(identityCode.equals(IdentityStatus.USER.getCode())){
                    ArrayList<String> objects = new ArrayList<>();
                    objects.add(userInfo.getUserId());
                    selPlanVo.setUserIds(objects);
                }
            }
        }
        PageUtils.execute(selPlanVo);
        List<SelPlanEntity> list = baseMapper.queryList(selPlanVo);
        list.forEach(x->{
            if (Assert.notNull(x.getCompanyId())) {
                x.setCompanyName(redisUtils.get(RedisKey.COMPANY.getName()+x.getCompanyId()));
            }
        });
        return PageUtils.getData(list);
    }

    @Override
    public FlowSelPlanEntity getPlanInfo(String id) {
        SelApprovalFlowEntity byId = selApprovalFlowService.getById(id);
        Assert.notNull(byId,"流程节点错误!");
        SelApprovalUnderwayEntity underwayEntity = selApprovalUnderwayService.getById(byId.getUnderwayId());
        FlowSelPlanEntity planInfo = baseMapper.getPlanInfo(underwayEntity.getId());
        return planInfo;
    }

    @Override
    @Transactional
    public boolean savePlan(SelPlanEntity selPlanEntity) {
        if (!Assert.notNullCollect(selPlanEntity.getProductList())) {
            throw new RuntimeException("计划商品不能为空");
        }

        if (baseMapper.selectCount(new LambdaQueryWrapper<SelPlanEntity>()
                .and(e->e.eq(SelPlanEntity::getCompanyId,selPlanEntity.getCompanyId())
                        .eq(SelPlanEntity::getIsDelete, Opposite.ZERO)
                        .eq(SelPlanEntity::getPeriodNum,selPlanEntity.getPeriodNum())
                        .eq(SelPlanEntity::getPlanName,selPlanEntity.getPlanName())))>0) {
            throw new RuntimeException("销售计划名称重复,请修改.");
        }

        if(selPlanEntity.getPlanStartDate().compareTo(selPlanEntity.getPlanEndTime())>0){
            throw new RuntimeException("开始时间不能大于结束时间.");
        }
        selPlanEntity.setPlanCode(codeGenerate.getCode(BusPrefix.PLAN));

        List<SelPlanProductDetailEntity> productList = selPlanEntity.getProductList();
        //竞价竟量和零售计算可销售量
        long l = DateUtils.compareDay(selPlanEntity.getPlanStartDate(), selPlanEntity.getPlanEndTime());
        productList.forEach(x->{
            SelProductEntity byId = selProductService.getById(x.getProductId());
            x.setProductName(byId.getProductName());
            x.setId(SnowFlake.getUUId());
            if(selPlanEntity.getModeType().equals(PlanModel.BIDDING_AND_AMOUNT.getCode())||selPlanEntity.getModeType().equals(PlanModel.RETAIL.getCode())){
                x.setSaleQuantity(getCount(x,l));
            }
            x.setPlanId(selPlanEntity.getId());
        });

        //定价模式不用审核直接通过
        if(selPlanEntity.getModeType().equals(PlanModel.PRICING.getCode())){
            selPlanEntity.setPlanStatus(PlanStatus.PASS.getCode());
        }

        baseMapper.insert(selPlanEntity);

        selPlanProductDetailService.saveBatch(productList);
        return true;
    }

    @Override
    @Transactional
    public boolean updatePlan(SelPlanEntity selPlanEntity) {

        if (baseMapper.selectCount(new LambdaQueryWrapper<SelPlanEntity>()
                .and(e->e.eq(SelPlanEntity::getCompanyId,selPlanEntity.getCompanyId())
                        .eq(SelPlanEntity::getIsDelete, Opposite.ZERO)
                        .eq(SelPlanEntity::getPeriodNum,selPlanEntity.getPeriodNum())
                        .eq(SelPlanEntity::getPlanName,selPlanEntity.getPlanName())
                        .ne(SelPlanEntity::getId,selPlanEntity.getId())))>0) {
            throw new RuntimeException("销售计划名称重复,请修改.");
        }

        baseMapper.updateById(selPlanEntity);

        selPlanProductDetailService.remove(new LambdaQueryWrapper<SelPlanProductDetailEntity>().eq(SelPlanProductDetailEntity::getPlanId,selPlanEntity.getId()));

        List<SelPlanProductDetailEntity> productList = selPlanEntity.getProductList();
        //竞价竞量标识
        final boolean flag=selPlanEntity.getModeType().equals(PlanModel.BIDDING_AND_AMOUNT.getCode());

        long l = DateUtils.compareDay(selPlanEntity.getPlanStartDate(), selPlanEntity.getPlanEndTime());
        productList.forEach(x -> {
            SelProductEntity byId = selProductService.getById(x.getProductId());
            x.setProductName(byId.getProductName());
            x.setId(SnowFlake.getUUId());
            if(flag){
                x.setSaleQuantity(getCount(x, l));
            }
            x.setPlanId(selPlanEntity.getId());
        });

        selPlanProductDetailService.saveBatch(productList);
        return true;
    }

    @Override
    public SelPlanEntity getInfo(String id) {
        SelPlanEntity selPlanEntity = baseMapper.selectById(id);
        Assert.notNull(selPlanEntity,"没有找到该主键信息");
        List<SelPlanProductDetailEntity> list = selPlanProductDetailService.list(new LambdaQueryWrapper<SelPlanProductDetailEntity>().eq(SelPlanProductDetailEntity::getPlanId, id));
        selPlanEntity.setProductList(list);
        return selPlanEntity;
    }

    @Override
    public boolean audit(String id,String userId) {
        SelPlanEntity selPlanEntity = getInfo(id);
        Assert.notNull(selPlanEntity,"没有找到该主键信息");
        if (selPlanEntity.getPlanStatus().equals(PlanStatus.SUBMITTED.getCode())) {
            throw new RuntimeException("改计划已提交审核,请勿重复提交.");
        }
        //改变状态值
        selPlanEntity.setPlanStatus(PlanStatus.SUBMITTED.getCode());


        //判断该流程节点是否定义
        if (selApprovalDefinitionDao.selectCount(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selPlanEntity.getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.JH_SH.getCode())))<=0) {
            throw new RuntimeException("销售计划审核流程节点未定义,无法提交审核");
        }

        //初始化和保存流程节点
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.JH_SH.getCode().toString(),
                selPlanEntity.getCompanyId(),
                id,
                userId
        );

        //审批详情保存
        FlowSelPlanEntity flowSelPlanEntity=new FlowSelPlanEntity();
        BeanUtils.copyProperties(selPlanEntity,flowSelPlanEntity);
        flowSelPlanEntity.setUnderwayId(flowBO.getUnderWayId());
        flowSelPlanEntity.setId(SnowFlake.getUUId());
        flowSelPlanEntity.setPlanId(id);
        flowSelPlanService.save(flowSelPlanEntity);

        //审批详情关联的商品保存
        List<SelPlanProductDetailEntity> productList = selPlanEntity.getProductList();

        List<FlowSelPlanProductDetailEntity> flowPlanProducts=new ArrayList<>();
        if (Assert.notNullCollect(productList)) {
            productList.forEach(x->{
                FlowSelPlanProductDetailEntity f=new FlowSelPlanProductDetailEntity();
                BeanUtils.copyProperties(x,f);
                f.setId(SnowFlake.getUUId());
                f.setPlanId(flowSelPlanEntity.getId());
                flowPlanProducts.add(f);
            });
        }
        if (Assert.notNullCollect(flowPlanProducts)){
            SqlBatchUtil.execute(
                    flowPlanProducts,
                    FlowSelPlanProductDetailDao.class,
                    (t,m) -> m.insert(t)
            );
        }

        //通知第一位审批人
        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        id,
                                        flowBO.getFirstFlowId()
                                )
                        )
                        .setCompanyId(selPlanEntity.getCompanyId())
                        .setSpecific(ApproveStatus.JH_SH.getCode())
                        .setMsgContent(null)
                        .setMsgTitle("销售计划审批")

        );

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );

        //推送公众号信息给审批人
        ThreadPoolUtil.runTask(
                ()->{
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            userId,
                            "销售计划"
                    );
                }
        );

        baseMapper.updateById(selPlanEntity);
        return true;
    }

    @Override
    public SelApprovalFlowEntity getSchedule(String id) {
        //查询审批的节点
        SelApprovalUnderwayEntity underway = selApprovalUnderwayService.queryFlowAll(id);
        Assert.notNull(underway,"查询不到该条有正在进行中的审批节点信息");
        String underwayId = underway.getId();
        return getFlow(underwayId);
    }

    public SelApprovalFlowEntity getFlow(String underwayId){
        //查询出第一个节点
        SelApprovalFlowEntity flowEntity = selApprovalFlowService.getOne(new LambdaQueryWrapper<SelApprovalFlowEntity>().and(e ->
                e.eq(SelApprovalFlowEntity::getUnderwayId, underwayId)
                        .eq(SelApprovalFlowEntity::getParentId, IdentityStatus.GOD.getName())));
        Assert.notNull(flowEntity,"查询不到该节点的流程信息");


        getFlowChild(flowEntity,underwayId);


        return BusUtils.approvalTime(flowEntity);
    }

    @Override
    @Transactional
    public boolean submitBidding(SelPlanVo selPlanVo) {
        String id = selPlanVo.getId();
        SelPlanEntity selPlanEntity = baseMapper.selectById(id);
        Assert.notNull(selPlanEntity,"无法识别的主键,请核对.");
        if (selPlanEntity.getPlanStatus().equals(PlanStatus.SUBMIT.getCode())) {
            throw new RuntimeException("已提交过竞价,请勿重复提交");
        }

        //判断该流程节点是否定义
        if (selApprovalDefinitionDao.selectCount(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selPlanEntity.getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.ZJ_SH.getCode())))<=0) {
            throw new RuntimeException("竞价审核流程节点未定义,无法提交审核");
        }


        Date startDate = selPlanVo.getBiddingStartDate();
        Date endTime = selPlanVo.getBiddingEndTime();
        Date now = DateUtils.getNow();
        if(startDate.compareTo(now)<0){
            throw new RuntimeException("竞价开始时间必须大于当前时间.");
        }

        if (DateUtils.compareSecond(startDate,endTime)<60) {
            throw new RuntimeException("开始和结束时间必须相差大于1分钟.");
        }

        //定义竞价主对象
        SelBiddingEntity selBiddingEntity=new SelBiddingEntity();
        selBiddingEntity.setId(SnowFlake.getUUId());
        selBiddingEntity.setBiddingCode(codeGenerate.getCode(BusPrefix.BIDDING));
        selBiddingEntity.setPlanId(selPlanEntity.getId());
        selBiddingEntity.setCompanyId(selPlanEntity.getCompanyId());
        selBiddingEntity.setModeType(selPlanEntity.getModeType());
        selBiddingEntity.setRoundNum(Integer.valueOf(Opposite.SINGLE));
        selBiddingEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingEntity.setPlanName(selPlanEntity.getPlanName());
        selBiddingEntity.setPlanStartDate(startDate);
        selBiddingEntity.setPlanEndTime(endTime);
        selBiddingEntity.setPeriodNum(selPlanEntity.getPeriodNum());
        selBiddingEntity.setCreateBy(selPlanEntity.getCreateBy());
        selBiddingEntity.setQuality(selPlanVo.getQuality());
        selBiddingEntity.setDeliveryStartDate(selPlanVo.getDeliveryStartDate());
        selBiddingEntity.setDeliveryEndTime(selPlanVo.getDeliveryEndTime());
        selBiddingEntity.setTrafficMode(selPlanVo.getTrafficMode());
        selBiddingEntity.setDeliveryMode(selPlanVo.getDeliveryMode());
        selBiddingEntity.setDescribe(selPlanVo.getDescribe());
        selBiddingDao.insert(selBiddingEntity);


        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.ZJ_SH.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selPlanVo.getUserId()
        );

        //竞价轮次表
        SelBiddingSequenceEntity selBiddingSequenceEntity=new SelBiddingSequenceEntity();
        selBiddingSequenceEntity.setId(SnowFlake.getUUId());
        selBiddingSequenceEntity.setBiddingId(selBiddingEntity.getId());
        selBiddingSequenceEntity.setSequence(selBiddingEntity.getRoundNum());
        selBiddingSequenceEntity.setPlanStartDate(startDate);
        selBiddingSequenceEntity.setPlanEndTime(endTime);
        selBiddingSequenceEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingSequenceService.save(selBiddingSequenceEntity);

        //审批信息保存
        List<FlowSelBiddingCustomerEntity> flowCustomer=new ArrayList<>();

        //竞价企业
        List<SelBiddingCustomerEntity> customerList = selPlanVo.getCustomerList();
        customerList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequence(selBiddingSequenceEntity.getId());

            //审批流程信息保存
            FlowSelBiddingCustomerEntity f=new FlowSelBiddingCustomerEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowCustomer.add(f);

        });
        selBiddingCustomerService.saveBatch(customerList);

        //基价调整记录
        List<SelBiddingFeeRecordEntity> feeRecordList=new ArrayList<>();

        //竞价商品
        List<FlowSelBiddingProductEntity> flowProduct=new ArrayList<>();
        List<SelBiddingProductEntity> productList = selPlanVo.getProductList();
        productList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequenceId(selBiddingSequenceEntity.getId());

            //竞价基价信息记录
            SelBiddingFeeRecordEntity feeRecordEntity=new SelBiddingFeeRecordEntity();
            feeRecordEntity.setId(SnowFlake.getUUId());
            feeRecordEntity.setSequenceId(selBiddingSequenceEntity.getId());
            feeRecordEntity.setBaseFee(x.getBaseFee());
            feeRecordEntity.setProductId(x.getProductId());
            feeRecordEntity.setProductName(x.getProductName());
            feeRecordList.add(feeRecordEntity);

            FlowSelBiddingProductEntity f=new FlowSelBiddingProductEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowProduct.add(f);

        });
        selBiddingProductService.saveBatch(productList);

        //竞价初始基价
        selBiddingFeeRecordService.saveBatch(feeRecordList);


        SelPlanEntity temp=new SelPlanEntity();
        temp.setId(id);
        temp.setPlanStatus(PlanStatus.SUBMIT.getCode());
        baseMapper.updateById(temp) ;


        FlowSelBiddingEntity flowSelBiddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,flowSelBiddingEntity);
        flowSelBiddingEntity.setId(SnowFlake.getUUId());
        flowSelBiddingEntity.setUnderwayId(flowBO.getUnderWayId());
        flowSelBiddingEntity.setRoundId(selBiddingSequenceEntity.getId());
        flowSelBiddingEntity.setBiddingId(id);

        //保存审批流程相关的信息
        flowSelBiddingService.save(flowSelBiddingEntity);
        flowSelBiddingCustomerService.saveBatch(flowCustomer);
        flowSelBiddingProductService.saveBatch(flowProduct);

        //通知第一位审批人
        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("商品竞价审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        ).setCompanyId(selBiddingEntity.getCompanyId())
                        .setSpecific(ApproveStatus.ZJ_SH.getCode())
                        .setMsgContent(null)

        );

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );

        //通知微信公众号
        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selPlanVo.getUserId(),
                            "商品竞价"
                    );
                }
        );

        //发送消息通知
//        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//        sysUserMsgEntity.setMsgType(MsgType.EXAMINE.getCode());
//        sysUserMsgEntity.setBusCode(ApproveStatus.ZJ_SH.getCode());
//        sysUserMsgEntity.setMsgAccessory(selBiddingEntity.getId());
//        sysUserMsgEntity.setCompanyId(selPlanEntity.getCompanyId());
//        sysUserMsgEntity.setCreateBy(selPlanVo.getUserId());
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.EXAMINE.getKey());

        return true;
    }

    @Override
    @Transactional
    public boolean revocation(String id) {
        SelPlanEntity selPlanEntity = baseMapper.selectById(id);
        Assert.notNull(selPlanEntity,"查询不到该主键信息");

        SelApprovalUnderwayEntity selApprovalUnderwayEntity = selApprovalUnderwayService.queryFlowAll(id);
        if (selApprovalUnderwayEntity.getIsStart().equals(Integer.valueOf(Opposite.SINGLE))) {
            throw new RuntimeException("该审批已经开始审批,无法撤回.");
        }


        //更新审批流程状态
        SelApprovalUnderwayEntity temp=new SelApprovalUnderwayEntity();
        temp.setId(selApprovalUnderwayEntity.getId());
        temp.setIsDelete(Integer.valueOf(Opposite.SINGLE));
        temp.setIsRepeal(Integer.valueOf(Opposite.SINGLE));
        selApprovalUnderwayService.updateById(temp);

        //销售计划状态更新
        SelPlanEntity tempPlan=new SelPlanEntity();
        tempPlan.setPlanStatus(PlanStatus.REVOCATION.getCode());
        tempPlan.setId(id);
        baseMapper.updateById(tempPlan);


        SelApprovalFlowEntity flowEntity = selApprovalFlowService.getOne(
                new LambdaQueryWrapper<SelApprovalFlowEntity>()
                        .eq(SelApprovalFlowEntity::getUnderwayId, selApprovalUnderwayEntity.getId())
                        .last("limit 1")
        );

        //更新审批消息为已处理
        userMsgService.updateStatus(
                BusCode.USER_CHECK.getCode(),
                String.format(
                        "%s,%s",
                        id,
                        flowEntity.getId()
                )
        );

        return true;
    }

    @Override
    @Transactional
    public boolean submitPricing(SelPlanVo selPlanVo) {

        String id = selPlanVo.getId();
        SelPlanEntity selPlanEntity = baseMapper.selectById(id);
        Assert.notNull(selPlanEntity,"无法识别的主键,请核对.");
        if (selPlanEntity.getPlanStatus().equals(PlanStatus.SUBMIT.getCode())) {
            throw new RuntimeException("已提交过竞价,请勿重复提交");
        }


        //判断该流程节点是否定义
        if (selApprovalDefinitionDao.selectCount(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selPlanEntity.getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.JY_WC.getCode())))<=0) {
            throw new RuntimeException("竞价审核流程节点未定义,无法提交审核");
        }

        //定义竞价主对象
        SelBiddingEntity selBiddingEntity=new SelBiddingEntity();
        selBiddingEntity.setId(SnowFlake.getUUId());
        selBiddingEntity.setBiddingCode(codeGenerate.getCode(BusPrefix.BIDDING));
        selBiddingEntity.setPlanId(selPlanEntity.getId());
        selBiddingEntity.setCompanyId(selPlanEntity.getCompanyId());
        selBiddingEntity.setModeType(selPlanEntity.getModeType());
        selBiddingEntity.setRoundNum(Integer.valueOf(Opposite.SINGLE));
        selBiddingEntity.setBiddingStatus(BiddingStatus.BIDDING_AUDIT_OVER.getCode());
        selBiddingEntity.setPlanName(selPlanEntity.getPlanName());
        selBiddingEntity.setPeriodNum(selPlanEntity.getPeriodNum());
        selBiddingEntity.setCreateBy(selPlanEntity.getCreateBy());
        selBiddingEntity.setQuality(selPlanVo.getQuality());
        selBiddingEntity.setDeliveryStartDate(selPlanVo.getDeliveryStartDate());
        selBiddingEntity.setDeliveryEndTime(selPlanVo.getDeliveryEndTime());
        selBiddingEntity.setPlanStartDate(DateUtils.getNow());
        selBiddingEntity.setPlanEndTime(DateUtils.getNow());
        selBiddingEntity.setTrafficMode(selPlanVo.getTrafficMode());
        selBiddingEntity.setDeliveryMode(selPlanVo.getDeliveryMode());
        selBiddingEntity.setDescribe(selPlanVo.getDescribe());
        selBiddingDao.insert(selBiddingEntity);

        //竞价轮次表
        SelBiddingSequenceEntity selBiddingSequenceEntity=new SelBiddingSequenceEntity();
        selBiddingSequenceEntity.setId(SnowFlake.getUUId());
        selBiddingSequenceEntity.setBiddingId(selBiddingEntity.getId());
        selBiddingSequenceEntity.setSequence(selBiddingEntity.getRoundNum());
        selBiddingSequenceEntity.setBiddingStatus(BiddingStatus.BIDDING_AUDIT_OVER.getCode());
        selBiddingSequenceEntity.setPlanStartDate(DateUtils.getNow());
        selBiddingSequenceEntity.setPlanEndTime(DateUtils.getNow());
        selBiddingSequenceService.save(selBiddingSequenceEntity);


        //定义流程
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.JY_WC.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selPlanVo.getUserId()
        );


        //模拟竞价成功的企业
        List<SelBiddingSuccessEntity> successList=new ArrayList<>();


        //竞价企业
        List<SelBiddingCustomerEntity> customerList = selPlanVo.getCustomerList();
        //审批流程主信息
        List<FlowSelBiddingSuccessEntity> flowSelBiddingSuccessEntities=new ArrayList<>();
        //审批流程商品
        List<FlowSelBiddingSuccessProductEntity> flowSelBiddingSuccessProductEntities = new ArrayList<>();

        //成功的企业
        List<SelBiddingCustomerOfferEntity> offerCollect=new ArrayList<>();
        //成功的商品
        List<SelBiddingSuccessProductEntity> successProductList=new ArrayList<>();

        customerList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequence(selBiddingSequenceEntity.getId());

            //组装交易完成的数据 模拟手动选择竞价成功的企业
            SelBiddingSuccessEntity successEntity=new SelBiddingSuccessEntity();
            successEntity.setId(SnowFlake.getUUId());
            successEntity.setSequenceId(x.getSequence());
            successEntity.setBiddingId(selBiddingEntity.getId());
            successEntity.setOfferId(x.getCustomerId());
            successList.add(successEntity);


            FlowSelBiddingSuccessEntity f=new FlowSelBiddingSuccessEntity();
            BeanUtils.copyProperties(successEntity,f);
            f.setUnderwayId(flowBO.getUnderWayId());
            f.setBiddingId(selBiddingEntity.getId());
            f.setId(SnowFlake.getUUId());
            flowSelBiddingSuccessEntities.add(f);


            //模拟商家报价的数据 其实是后台手动填写的 目的是为了跟其他模式保持数据一致
            List<SelBiddingCustomerOfferEntity> offerList = x.getOfferList();
            offerList.forEach(e->{
                e.setId(SnowFlake.getUUId());
                e.setRelevanceId(x.getId());
                e.setOfferer(x.getCustomerId());

                //交易成功的报价商品详情
                SelBiddingSuccessProductEntity tempProduct=new SelBiddingSuccessProductEntity();
                tempProduct.setId(SnowFlake.getUUId());
                tempProduct.setSuccessId(successEntity.getId());
                tempProduct.setProductId(e.getProductId());
                tempProduct.setProductName(e.getProductName());
                tempProduct.setPrice(e.getPrice());
                tempProduct.setCount(new BigDecimal(e.getCount()));
                tempProduct.setOfferId(x.getCustomerId());
                successProductList.add(tempProduct);

                FlowSelBiddingSuccessProductEntity successProduct=new FlowSelBiddingSuccessProductEntity();
                BeanUtils.copyProperties(tempProduct,successProduct);
                successProduct.setId(SnowFlake.getUUId());
                successProduct.setSuccessId(f.getId());
                flowSelBiddingSuccessProductEntities.add(successProduct);


            });
            offerCollect.addAll(offerList);
//            selBiddingCustomerOfferService.saveBatch(offerList);

//            selBiddingSuccessProductService.saveBatch(successProductList);


        });

        //保存信息
        selBiddingCustomerOfferService.saveBatch(offerCollect);
        selBiddingSuccessProductService.saveBatch(successProductList);
        selBiddingCustomerService.saveBatch(customerList);
        selBiddingSuccessService.saveBatch(successList);
        flowSelBiddingSuccessService.saveBatch(flowSelBiddingSuccessEntities);
        flowSelBiddingSuccessProductService.saveBatch(flowSelBiddingSuccessProductEntities);


        //基价调整记录
        List<SelBiddingFeeRecordEntity> feeRecordList=new ArrayList<>();

        //竞价商品
        List<SelBiddingProductEntity> productList = selPlanVo.getProductList();
        productList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequenceId(selBiddingSequenceEntity.getId());

            //竞价基价信息记录
            SelBiddingFeeRecordEntity feeRecordEntity=new SelBiddingFeeRecordEntity();
            feeRecordEntity.setId(SnowFlake.getUUId());
            feeRecordEntity.setSequenceId(selBiddingSequenceEntity.getId());
            feeRecordEntity.setBaseFee(x.getBaseFee());
            feeRecordEntity.setProductId(x.getProductId());
            feeRecordEntity.setProductName(x.getProductName());
            feeRecordList.add(feeRecordEntity);


        });
        selBiddingProductService.saveBatch(productList);

        //竞价初始基价
        selBiddingFeeRecordService.saveBatch(feeRecordList);

        SelPlanEntity temp=new SelPlanEntity();
        temp.setId(id);
        temp.setPlanStatus(PlanStatus.SUBMIT.getCode());
        baseMapper.updateById(temp);

        //审核流程主信息
        FlowSelBiddingEntity biddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,biddingEntity);
        biddingEntity.setId(SnowFlake.getUUId());
        biddingEntity.setUnderwayId(flowBO.getUnderWayId());
        biddingEntity.setBiddingId(selBiddingEntity.getId());
        biddingEntity.setRoundId(selBiddingSequenceEntity.getId());
        flowSelBiddingService.save(biddingEntity);


        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("交易完成审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        )
                        .setCompanyId(selPlanEntity.getCompanyId())
                        .setSpecific(ApproveStatus.JY_WC.getCode())
                        .setMsgContent(null)
        );

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );


        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selPlanVo.getUserId(),
                            "交易完成"
                    );
                }
        );


        //发送交易交易完成审批
//        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//        sysUserMsgEntity.setMsgType(MsgType.EXAMINE.getCode());
//        sysUserMsgEntity.setBusCode(ApproveStatus.JY_WC.getCode());
//        sysUserMsgEntity.setMsgAccessory(selBiddingEntity.getId());
//        sysUserMsgEntity.setCompanyId(selBiddingEntity.getCompanyId());
//        sysUserMsgEntity.setCreateBy(selPlanVo.getUserId());
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.EXAMINE.getKey());
        return true;
    }

    @Override
    public List<SelPlanProductDetailEntity> queryProduct(String id) {
        List<SelPlanProductDetailEntity> list = selPlanProductDetailService.list(new LambdaQueryWrapper<SelPlanProductDetailEntity>()
                .eq(SelPlanProductDetailEntity::getPlanId, id));
        list.forEach(x->x.setCount(0.0));
        return list;
    }


    /** 获取预计可销售量 */
    public double getCount(SelPlanProductDetailEntity selPlanProductDetailEntity,long day){
        BigDecimal bigDecimal=new BigDecimal("0.00");
        //计算日产量x周期天数
        bigDecimal=bigDecimal.add(new BigDecimal(selPlanProductDetailEntity.getDailyQuantity()).multiply(new BigDecimal(day==0?1:day)));
        //加上库存量
        bigDecimal=bigDecimal.add(new BigDecimal(selPlanProductDetailEntity.getStockQuantity()));
        //减去合同量
        bigDecimal=bigDecimal.subtract(new BigDecimal(selPlanProductDetailEntity.getContractQuantity()));
        return bigDecimal.doubleValue();
    }

    //获取流程子节点
    private void getFlowChild(SelApprovalFlowEntity flowEntity,String underwayId){
        if (Assert.notNull(flowEntity)) {
            SelApprovalFlowEntity child = selApprovalFlowService.getOne(new LambdaQueryWrapper<SelApprovalFlowEntity>().and(e ->
                    e.eq(SelApprovalFlowEntity::getUnderwayId, underwayId)
                            .eq(SelApprovalFlowEntity::getParentId, flowEntity.getId())));
            flowEntity.setChild(child);
            getFlowChild(flowEntity.getChild(),underwayId);
        }
    }
}