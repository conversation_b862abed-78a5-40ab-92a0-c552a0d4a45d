package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.status.ResponseCode;
import com.junl.crm_common.tools.*;
import com.junl.crm_common.tools.excel.ExcelUtils;
import com.junl.crm_work.bo.InitFlowBO;
import com.junl.crm_work.dao.SelBiddingDao;
import com.junl.crm_work.dao.SelInquiryOfferDao;
import com.junl.crm_work.dao.SelPlanProductDetailDao;
import com.junl.crm_work.listener.SmsListener;
import com.junl.crm_work.threadTask.InquiryTaskQuery;
import com.junl.crm_work.util.BusUtils;
import com.junl.crm_work.vo.InquiryExcel;
import com.junl.crm_work.vo.SelInquiryOfferVo;
import com.junl.crm_work.vo.SelPlanProductDetailVo;
import com.junl.msg.common.MsgType;
import com.junl.crm_common.common.PageEntity;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.junl.crm_work.dao.SelInquiryDao;
import com.junl.crm_work.service.*;
import com.junl.crm_work.status.*;
import com.junl.crm_work.util.CodeGenerate;
import com.junl.crm_work.vo.SelInquiryVo;
import com.junl.msg.socket.WebSocketChannel;
import com.junl.system.UserMsgService;
import com.junl.system.UserService;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.unit.DataUnit;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
@Log4j2
public class SelInquiryServiceImpl extends ServiceImpl<SelInquiryDao, SelInquiryEntity> implements SelInquiryService {


    @Autowired
    private SelInquiryCustomerService selInquiryCustomerService;


    @Autowired
    private CodeGenerate codeGenerate;

    @Autowired
    private SelApprovalDefinitionService selApprovalDefinitionService;

    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;

    @Autowired
    private SelBiddingDao selBiddingDao;

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    @Autowired
    private SelBiddingFeeRecordService selBiddingFeeRecordService;

    @Autowired
    private SelBiddingProductService selBiddingProductService;


    @Autowired
    private SelPlanProductDetailDao selPlanProductDetailDao;

    @Autowired
    private SelInquiryOfferDao selInquiryOfferDao;

    @Reference(lazy = true)
    private UserService userService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    @Lazy
    private SmsListener smsListener;

    @Autowired
    private FlowSelBiddingService flowSelBiddingService;

    @Autowired
    private FlowSelBiddingProductService flowSelBiddingProductService;

    @Autowired
    private FlowSelBiddingCustomerService flowSelBiddingCustomerService;

    @Autowired
    private UserMsgService userMsgService;



    /**
     * @description: 分页查询列表
     * @param selInquiryVo
     */
    @Override
    public PageEntity queryPage(SelInquiryVo selInquiryVo) {
        if (Assert.notNull(selInquiryVo.getUserId())) {
            SysUserEntity userInfo = userService.getUserInfo(selInquiryVo.getUserId());
            Assert.notNull(userInfo,"系统无法识别的账户信息");

            if(Assert.notNull(userInfo.getDataId())&&!Opposite.ZERO.equals(userInfo.getDataId())){
                List<String> users = userService.getUsers(userInfo.getDataId());
                selInquiryVo.setUserIds(users);
            }else{
                Integer identityCode = userInfo.getIdentityCode();

                if (identityCode.equals(IdentityStatus.DEPT_ADMIN.getCode())) {
                    List<String> deptUsers = userService.getDeptUsers(userInfo.getDeptId());
                    selInquiryVo.setUserIds(deptUsers);
                }else if(identityCode.equals(IdentityStatus.USER.getCode())){
                    ArrayList<String> objects = new ArrayList<>();
                    objects.add(userInfo.getUserId());
                    selInquiryVo.setUserIds(objects);
                }
            }

        }

        PageUtils.execute(selInquiryVo);
        List<SelInquiryEntity> list = baseMapper.queryList(selInquiryVo);
        list.forEach(x->{
            if (Assert.notNull(x.getCompanyId())) {
                x.setCompanyName(redisUtils.get(RedisKey.COMPANY.getName()+x.getCompanyId()));
            }
        });
        return PageUtils.getData(list);
    }

    @Override
    public List<InquiryExcel> getInquiryInfo(String id) {
        SelInquiryEntity selInquiryEntity = baseMapper.selectById(id);
        Assert.notNull(selInquiryEntity,"查询不到该主键信息");

        List<InquiryExcel> list=new ArrayList<>();
        //查询关联的商品
        List<SelPlanProductDetailEntity> product = selPlanProductDetailDao.selectList(
                new LambdaQueryWrapper<SelPlanProductDetailEntity>()
                        .eq(SelPlanProductDetailEntity::getPlanId, selInquiryEntity.getPlanId())
        );
        final CountDownLatch countDownLatch=new CountDownLatch(product.size());
        List<Callable<List<InquiryExcel>>> task=new ArrayList<>();
        for (SelPlanProductDetailEntity x : product) {

            InquiryTaskQuery inquiryTaskQuery = new InquiryTaskQuery(
                    countDownLatch,
                    x,
                    baseMapper,
                    selInquiryOfferDao,
                    selInquiryEntity.getId()
            );
            task.add(inquiryTaskQuery);
        }

        List<List<InquiryExcel>> lists = ThreadPoolUtil.runTask(task, countDownLatch);
        lists.forEach(x->{
            list.addAll(x);
        });

        return list;
    }

    @Override
    public PageEntity queryCustomerOffer(SelInquiryVo selInquiryVo) {
        PageUtils.execute(selInquiryVo);
        List<SelInquiryCustomerEntity> selInquiryCustomerEntities = baseMapper.queryCustomerOffer(selInquiryVo);
        return PageUtils.getData(selInquiryCustomerEntities);
    }

    /**
     * @description: 分页查询列表
     * @param selInquiryVo
     */
    @Override
    public PageEntity queryAppPage(SelInquiryVo selInquiryVo) {
        PageUtils.execute(selInquiryVo);
        //系统参数
        String customerId = selInquiryVo.getCustomerId();
        List<SelInquiryEntity> list = baseMapper.queryAppList(selInquiryVo);
        PageEntity<SelInquiryEntity> data = PageUtils.getData(list);
        List<SelInquiryEntity> inquiryEntities = Lists.newArrayList();
        inquiryEntities.addAll(data.getList().stream().map(item->
                {
                    SelInquiryEntity newInquiry = new SelInquiryEntity();
                    BeanUtils.copyProperties(item, newInquiry);
                    String planId =item.getPlanId();
                    SelPlanProductDetailVo productDetailVo =new SelPlanProductDetailVo();
                    productDetailVo.setCustomerId(customerId);
                    productDetailVo.setPlanId(planId);
                    List<SelPlanProductDetailEntity> productlist =  selPlanProductDetailDao.queryAppList(productDetailVo);
                    newInquiry.setProductDetailList(productlist);
                    return newInquiry;
                }
        ).collect(Collectors.toList()));
        data.setList(inquiryEntities);
        return data;
    }


    @Override
    public SelInquiryEntity infoInquiry(String id) {

        return baseMapper.infoInquiry(id);
    }

    @Override
    public Map<String,String> exportExcel(String id) {
        SelInquiryEntity selInquiryEntity = baseMapper.selectById(id);
        Assert.notNull(selInquiryEntity,"查询不要主键信息");

        if (selInquiryEntity.getInquiryStatus().equals(InquiryStatus.NOT_ISSUE.getCode())) {
            throw new RuntimeException("未发布的询价,无可用信息导出");
        }

        List<InquiryExcel> inquiryInfo = getInquiryInfo(id);
        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        excelUtils.write(
                inquiryInfo,
                InquiryExcel.class,
                byteArrayOutputStream,
                InquiryExcel.getHead(
                        selInquiryEntity.getInquiryName(),
                        DateUtils.defaultFormat(selInquiryEntity.getInquiryStartDate()),
                        DateUtils.defaultFormat(selInquiryEntity.getInquiryEndTime())
                ),
                3,
                new int[]{0,4}
        );
        String excelBase64 = CommonUtil.getExcelBase64(byteArrayOutputStream);
        Map<String,String> map=new HashMap<>();
        map.put("name",selInquiryEntity.getInquiryName()+"询价报表");
        map.put("file",excelBase64);
        return map;
    }

    @Override
    public List<SelInquiryCustomerEntity> getInquiryCustomer(String id) {
        List<SelInquiryCustomerEntity> inquiryCustomer = baseMapper.getInquiryCustomer(id);
        Iterator<SelInquiryCustomerEntity> iterator = inquiryCustomer.iterator();

        //过滤掉未报价的
        while (iterator.hasNext()) {
            SelInquiryCustomerEntity next = iterator.next();
            if (selInquiryOfferDao.selectCount(
                    new LambdaQueryWrapper<SelInquiryOfferEntity>()
                            .eq(SelInquiryOfferEntity::getRelevancyId,next.getId())
            )
                    <=0
            ) {
                iterator.remove();
            }
        }
        return inquiryCustomer;
    }

    @Override
    public List<SelInquiryCustomerEntity> queryInquiryCustomer(String id) {

        return baseMapper.getInquiryCustomer(id);
    }

    @Override
    public boolean savePlan(SelInquiryEntity selInquiryEntity) {
        String id = selInquiryEntity.getId();
        Date startDate = selInquiryEntity.getInquiryStartDate();
        Date endTime = selInquiryEntity.getInquiryEndTime();
        Date now = DateUtils.getNow();
        if (endTime.compareTo(now)<0) {
            throw new RuntimeException("结束时间不能小于当前时间");
        }

        if (DateUtils.compareSecond(startDate,endTime)<60) {
            throw new RuntimeException("结束时间不得小于开始时间1分钟");
        }

        baseMapper.updateById(selInquiryEntity);


        selInquiryCustomerService.remove(
                new LambdaQueryWrapper<SelInquiryCustomerEntity>()
                .eq(SelInquiryCustomerEntity::getInquiryId,id)
        );

        List<SelInquiryCustomerEntity> customerList = selInquiryEntity.getCustomerList();
        customerList.forEach(x->{
            x.setInquiryId(selInquiryEntity.getId());
            x.setId(SnowFlake.getUUId());
        });

        selInquiryCustomerService.saveBatch(customerList);
        return true;
    }

    @Override
    public boolean updatePlan(SelInquiryEntity selInquiryEntity) {
        return savePlan(selInquiryEntity);
    }


    @Override
    public boolean issue(SelInquiryEntity selInquiryEntity) {
        String id = selInquiryEntity.getId();
        SelInquiryEntity temp = baseMapper.selectById(id);

        if (!temp.getIsDelete().equals(0)) {
            throw new RuntimeException("询价已删除,请刷新后操作");
        }

        if (temp.getInquiryStatus().equals(InquiryStatus.ISSUE.getCode())) {
            throw new RuntimeException("已发布询价,请勿重复提交");
        }


        Date startDate = selInquiryEntity.getInquiryStartDate();
        Date endTime = selInquiryEntity.getInquiryEndTime();
        Date now = DateUtils.getNow();

        if(startDate.compareTo(now)<0){
            throw new RuntimeException("开始时间不能小于当前时间");
        }

        if (endTime.compareTo(now)<0) {
            throw new RuntimeException("结束时间不能小于当前时间");
        }

        if (DateUtils.compareSecond(startDate,endTime)<60) {
            throw new RuntimeException("结束时间不得小于开始时间1分钟");
        }


        selInquiryEntity.setInquiryStatus(InquiryStatus.ISSUE.getCode());
        baseMapper.updateById(selInquiryEntity);

        selInquiryCustomerService.remove(
                new LambdaQueryWrapper<SelInquiryCustomerEntity>()
                .eq(SelInquiryCustomerEntity::getInquiryId,id)
        );

        List<SelInquiryCustomerEntity> customerList = selInquiryEntity.getCustomerList();
        customerList.forEach(x->{
            x.setInquiryId(selInquiryEntity.getId());
            x.setId(SnowFlake.getUUId());
        });

        selInquiryCustomerService.saveBatch(customerList);


        //开始延迟
        long start = DateUtils.compareSecond(now, startDate);
        if(start<=0){
            throw new RuntimeException("开始时间必须大于当前时间");
        }

        //短信通知各选入公司
        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
        sysUserMsgEntity.setMsgType(MsgType.SMS.getCode());
        sysUserMsgEntity.setBusCode(BusCode.INFORM_COMPANY.getCode());
        sysUserMsgEntity.setMsgAccessory(CommonUtil.collect(customerList,SelInquiryCustomerEntity::getCustomerId,","));
        sysUserMsgEntity.setMsgContent(selInquiryEntity.getId());
        sysUserMsgEntity.setCompanyId(selInquiryEntity.getCompanyId());
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.SMS.getKey());

        smsListener.dispose(sysUserMsgEntity);

        redisUtils.set(RedisKey.INQUIRY.getName()+selInquiryEntity.getId(),Opposite.SINGLE,start);

        //加入结束前10分钟再次通知用户
        long second = DateUtils.compareSecond(startDate, endTime);
        //大于10分钟
        if(second>600){
            redisUtils.set(RedisKey.INQUIRY_NOTICE.getName()+id,Opposite.SINGLE,second-600);
        }

        return true;
    }

    @Override
    @Transactional
    public boolean bidding(SelInquiryVo selInquiryVo) {
        SelInquiryEntity selInquiryEntity = baseMapper.selectById(selInquiryVo.getId());

        if (!selInquiryEntity.getIsDelete().equals(0)) {
            throw new RuntimeException("询价已删除,请刷新后操作");
        }

        if (selInquiryEntity.getInquiryStatus().equals(InquiryStatus.SUBMIT.getCode())) {
            throw new RuntimeException("该询价已提交过竞价,请勿重复提交");
        }
        selInquiryEntity.setInquiryStatus(InquiryStatus.SUBMIT.getCode());
        Assert.notNull(selInquiryEntity,"找不到主键信息");


        //竞价商品
        List<SelBiddingProductEntity> productList = selInquiryVo.getProductList();
        for (SelBiddingProductEntity selBiddingProductEntity : productList) {
            if(selBiddingProductEntity.getBaseFee().compareTo(BigDecimal.ZERO)<=0){
                throw new RuntimeException(selBiddingProductEntity.getBaseFee()+"的基价必须大于零");
            }
        }


        //判断该流程节点是否定义
        if (selApprovalDefinitionService.count(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selInquiryEntity.getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.ZJ_SH.getCode())))<=0) {
            throw new RuntimeException("竞价审核流程节点未定义,无法提交审核");
        }

        Date startDate = selInquiryVo.getBiddingStartDate();
        Date endTime = selInquiryVo.getBiddingEndTime();
        Date now = DateUtils.getNow();
        if(startDate.compareTo(now)<0){
            throw new RuntimeException("竞价开始时间必须大于当前时间.");
        }

        if (DateUtils.compareSecond(startDate,endTime)<60) {
            throw new RuntimeException("开始和结束时间必须相差大于1分钟.");
        }

        //定义竞价主对象
        SelBiddingEntity selBiddingEntity=new SelBiddingEntity();
        selBiddingEntity.setId(SnowFlake.getUUId());
        selBiddingEntity.setBiddingCode(codeGenerate.getCode(BusPrefix.BIDDING));
        selBiddingEntity.setPlanId(selInquiryEntity.getPlanId());
        selBiddingEntity.setCompanyId(selInquiryEntity.getCompanyId());
        selBiddingEntity.setModeType(selInquiryEntity.getModeType());
        selBiddingEntity.setRoundNum(Integer.valueOf(Opposite.SINGLE));
        selBiddingEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingEntity.setPlanName(selInquiryEntity.getInquiryName());
        selBiddingEntity.setPlanStartDate(startDate);
        selBiddingEntity.setPeriodNum(selInquiryEntity.getPeriodNum());
        selBiddingEntity.setPlanEndTime(endTime);
        selBiddingEntity.setCreateBy(selInquiryEntity.getCreateBy());
        selBiddingEntity.setQuality(selInquiryVo.getQuality());
        selBiddingEntity.setDeliveryStartDate(selInquiryVo.getDeliveryStartDate());
        selBiddingEntity.setDeliveryEndTime(selInquiryVo.getDeliveryEndTime());
        selBiddingEntity.setTrafficMode(selInquiryVo.getTrafficMode());
        selBiddingEntity.setDeliveryMode(selInquiryVo.getDeliveryMode());
        selBiddingEntity.setDescribe(selInquiryVo.getDescribe());
        selBiddingDao.insert(selBiddingEntity);

        //定义流程
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.ZJ_SH.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selInquiryVo.getUserId()
        );

        //竞价轮次表
        SelBiddingSequenceEntity selBiddingSequenceEntity=new SelBiddingSequenceEntity();
        selBiddingSequenceEntity.setId(SnowFlake.getUUId());
        selBiddingSequenceEntity.setBiddingId(selBiddingEntity.getId());
        selBiddingSequenceEntity.setSequence(selBiddingEntity.getRoundNum());
        selBiddingSequenceEntity.setPlanStartDate(startDate);
        selBiddingSequenceEntity.setPlanEndTime(endTime);
        selBiddingSequenceEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingSequenceService.save(selBiddingSequenceEntity);


        //审批信息保存
        List<FlowSelBiddingCustomerEntity> flowCustomer=new ArrayList<>();

        //竞价企业
        List<SelBiddingCustomerEntity> customerList = selInquiryVo.getCustomerList();
        customerList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequence(selBiddingSequenceEntity.getId());

            //审批流程信息保存
            FlowSelBiddingCustomerEntity f=new FlowSelBiddingCustomerEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowCustomer.add(f);
        });
        selBiddingCustomerService.saveBatch(customerList);

        //基价调整记录
        List<SelBiddingFeeRecordEntity> feeRecordList=new ArrayList<>();

        List<FlowSelBiddingProductEntity> flowProduct=new ArrayList<>();
        productList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequenceId(selBiddingSequenceEntity.getId());

            //竞价基价信息记录
            SelBiddingFeeRecordEntity feeRecordEntity=new SelBiddingFeeRecordEntity();
            feeRecordEntity.setId(SnowFlake.getUUId());
            feeRecordEntity.setSequenceId(selBiddingSequenceEntity.getId());
            feeRecordEntity.setBaseFee(x.getBaseFee());
            feeRecordEntity.setProductId(x.getProductId());
            feeRecordEntity.setProductName(x.getProductName());
            feeRecordList.add(feeRecordEntity);


            FlowSelBiddingProductEntity f=new FlowSelBiddingProductEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowProduct.add(f);

        });
        selBiddingProductService.saveBatch(productList);

        //竞价初始基价
        selBiddingFeeRecordService.saveBatch(feeRecordList);

        FlowSelBiddingEntity flowSelBiddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,flowSelBiddingEntity);
        flowSelBiddingEntity.setId(SnowFlake.getUUId());
        flowSelBiddingEntity.setUnderwayId(flowBO.getUnderWayId());
        flowSelBiddingEntity.setRoundId(selBiddingSequenceEntity.getId());
        flowSelBiddingEntity.setBiddingId(selBiddingEntity.getId());


        //保存审批流程相关的信息
        flowSelBiddingService.save(flowSelBiddingEntity);
        flowSelBiddingCustomerService.saveBatch(flowCustomer);
        flowSelBiddingProductService.saveBatch(flowProduct);

        //通知第一位审批人
        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("商品竞价审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        ).setCompanyId(selBiddingEntity.getCompanyId())
                        .setSpecific(ApproveStatus.ZJ_SH.getCode())
                        .setMsgContent(null)

        );

        SelInquiryEntity temp=new SelInquiryEntity();
        temp.setInquiryStatus(InquiryStatus.SUBMIT.getCode());
        temp.setId(selInquiryEntity.getId());
        baseMapper.updateById(temp);

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );


        //通知微信公众号
        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selInquiryVo.getUserId(),
                            "商品竞价"
                    );
                }
        );


        //发送消息通知
//        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//        sysUserMsgEntity.setMsgType(MsgType.EXAMINE.getCode());
//        sysUserMsgEntity.setBusCode(ApproveStatus.ZJ_SH.getCode());
//        sysUserMsgEntity.setMsgAccessory(selBiddingEntity.getId());
//        sysUserMsgEntity.setCompanyId(selInquiryEntity.getCompanyId());
//        sysUserMsgEntity.setCreateBy(selInquiryVo.getUserId());
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.EXAMINE.getKey());

        return true;
    }

    @Override
    public List<SelPlanProductDetailEntity> queryProduct(String id) {
        SelInquiryEntity selInquiryEntity = baseMapper.selectById(id);
        Assert.notNull(selInquiryEntity,"查询不到该主键信息");

        List<SelPlanProductDetailEntity> list = selPlanProductDetailDao.selectList(
                new LambdaQueryWrapper<SelPlanProductDetailEntity>()
                        .eq(SelPlanProductDetailEntity::getPlanId, selInquiryEntity.getPlanId())
        );
        return list;
    }
}