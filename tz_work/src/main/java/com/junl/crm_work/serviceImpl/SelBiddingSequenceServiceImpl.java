package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.junl.crm_common.constant.BiddingSequenceStatusEnum;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.dao.SelBiddingCustomerOfferDao;
import com.junl.crm_work.dao.SelBiddingProductDao;
import com.junl.crm_work.dao.SelBiddingSequenceDao;
import com.junl.crm_work.dao.SelPlanProductDetailDao;
import com.junl.crm_work.service.SelBiddingProductService;
import com.junl.crm_work.vo.SelBiddingProductVo;
import com.junl.crm_work.vo.SelBiddingSequenceVo;
import com.junl.crm_work.service.SelBiddingSequenceService;
import com.junl.crm_work.vo.SelPlanProductDetailVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.xml.crypto.Data;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelBiddingSequenceServiceImpl
        extends ServiceImpl<SelBiddingSequenceDao, SelBiddingSequenceEntity>
        implements SelBiddingSequenceService
{

    @Autowired
    private SelBiddingProductDao  selBiddingProductDao;

    @Autowired
    private SelBiddingCustomerOfferDao selBiddingCustomerOfferDao;

    /**
     * @param selBiddingSequenceVo
     * @description: 分页查询列表
     */
    @Override
    public PageEntity queryPage(SelBiddingSequenceVo selBiddingSequenceVo) {
        PageUtils.execute(selBiddingSequenceVo);
        List<SelBiddingSequenceEntity> list = baseMapper.queryList(selBiddingSequenceVo);
        return PageUtils.getData(list);
    }


    /**
     * @param selBiddingSequenceVo
     * @description: 分页查询列表
     */
    /*@Override
    public PageEntity queryAppPage(SelBiddingSequenceVo selBiddingSequenceVo) {
        //系统参数
        String customerId = selBiddingSequenceVo.getCustomerId();
        PageUtils.execute(selBiddingSequenceVo);
        List<SelBiddingSequenceEntity> list = baseMapper.queryList(selBiddingSequenceVo);
        List<SelBiddingSequenceEntity> sequenceEntities = Lists.newArrayList();
        sequenceEntities.addAll(list.stream().map(item ->
                {
                    SelBiddingSequenceEntity newSequence = new SelBiddingSequenceEntity();
                    BeanUtils.copyProperties(item, newSequence);
                    String sequenceId = item.getId();
                    SelBiddingProductVo biddingProductVo = new SelBiddingProductVo();
                    biddingProductVo.setSequenceId(sequenceId);
                    biddingProductVo.setCustomerId(customerId);
                    List<SelBiddingProductEntity> productlist = selBiddingProductDao.queryAppList(biddingProductVo);
                    newSequence.setProductList(productlist);
                    return newSequence;
                }
        ).collect(Collectors.toList()));
        return PageUtils.getData(sequenceEntities);
    }*/

    /**
     * @param selBiddingSequenceVo
     * @description: 分页查询列表
     */
    @Override
    public PageEntity queryAppPage(SelBiddingSequenceVo selBiddingSequenceVo) {
        //系统参数
        String customerId = selBiddingSequenceVo.getCustomerId();
        PageUtils.execute(selBiddingSequenceVo);
        List<SelBiddingSequenceEntity> list = baseMapper.queryAppList(selBiddingSequenceVo);
        PageEntity<SelBiddingSequenceEntity> data = PageUtils.getData(list);
        List<SelBiddingSequenceEntity> sequenceEntities = Lists.newArrayList();
        sequenceEntities.addAll(
                data.getList().stream().map(item -> {
                    SelBiddingSequenceEntity newSequence = new SelBiddingSequenceEntity();
                    SelBiddingSequenceVo sequenceVo = new SelBiddingSequenceVo();
                    sequenceVo.setBiddingId(item.getId());
                    sequenceVo.setCustomerId(customerId);
                    sequenceVo.setBiddingStatus(selBiddingSequenceVo.getBiddingStatus());
                    SelBiddingSequenceEntity sequenceEntity = baseMapper.queryAppOne(sequenceVo);
                    if (Assert.notNull(sequenceEntity)) {
                        BeanUtils.copyProperties(sequenceEntity, newSequence);
                        String sequenceId = sequenceEntity.getId();
                        SelBiddingProductVo biddingProductVo = new SelBiddingProductVo();
                        biddingProductVo.setSequenceId(sequenceId);
                        biddingProductVo.setCustomerId(customerId);
                        List<SelBiddingProductEntity> productlist = selBiddingProductDao.queryAppList(biddingProductVo);
                        newSequence.setProductList(productlist);
                    }
                    return newSequence;
                }
        ).collect(Collectors.toList()));
        data.setList(sequenceEntities);
        return data;
    }


    @Override
    public PageEntity queryAppList(SelBiddingSequenceVo selBiddingSequenceVo) {
        //系统参数
        String customerId = selBiddingSequenceVo.getCustomerId();
        PageUtils.execute(selBiddingSequenceVo);
        List<SelBiddingSequenceEntity> list = baseMapper.queryList(selBiddingSequenceVo);
        List<SelBiddingSequenceEntity> sequenceEntities = Lists.newArrayList();
        /*sequenceEntities.addAll(list.stream().map(item ->
                {
                    SelBiddingSequenceEntity newSequence = new SelBiddingSequenceEntity();
                    BeanUtils.copyProperties(item, newSequence);
                    String sequenceId = item.getId();
                    SelBiddingProductVo biddingProductVo = new SelBiddingProductVo();
                    biddingProductVo.setSequenceId(sequenceId);
                    biddingProductVo.setCustomerId(customerId);
                    List<SelBiddingProductEntity> productlist = selBiddingProductDao.queryAppList(biddingProductVo);
                    newSequence.setProductList(productlist);
                    return newSequence;
                }
        ).collect(Collectors.toList()));*/
        list.stream().forEach(bean-> {
            String relevanceId = bean.getRelevanceId();
           Integer rcount= selBiddingCustomerOfferDao.selectCount(
                   new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                           .eq(SelBiddingCustomerOfferEntity::getRelevanceId, relevanceId)
           );
           if(rcount>0) {
               sequenceEntities.addAll(list.stream().filter(x->x.getRelevanceId().equals(relevanceId)).map(item ->
                       {
                           SelBiddingSequenceEntity newSequence = new SelBiddingSequenceEntity();
                           BeanUtils.copyProperties(item, newSequence);
                           String sequenceId = item.getId();
                           SelBiddingProductVo biddingProductVo = new SelBiddingProductVo();
                           biddingProductVo.setSequenceId(sequenceId);
                           biddingProductVo.setCustomerId(customerId);
                           List<SelBiddingProductEntity> productlist = selBiddingProductDao.queryAppList(biddingProductVo);
                           newSequence.setProductList(productlist);
                           return newSequence;
                       }
               ).collect(Collectors.toList()));
           }
        });

        return PageUtils.getData(sequenceEntities);
    }

}