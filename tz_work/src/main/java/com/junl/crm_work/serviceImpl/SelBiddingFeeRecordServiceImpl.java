package com.junl.crm_work.serviceImpl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.work.SelBiddingFeeRecordEntity;
import com.junl.crm_common.pojo.work.SelBiddingSequenceEntity;
import com.junl.crm_work.dao.SelBiddingFeeRecordDao;
import com.junl.crm_work.service.SelBiddingFeeRecordService;
import com.junl.crm_work.service.SelBiddingSequenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelBiddingFeeRecordServiceImpl
        extends ServiceImpl<SelBiddingFeeRecordDao, SelBiddingFeeRecordEntity>
        implements SelBiddingFeeRecordService
{

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;


    @Override
    public List<SelBiddingSequenceEntity> queryRecords(String id) {

        List<SelBiddingSequenceEntity> list = selBiddingSequenceService.list(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .eq(SelBiddingSequenceEntity::getBiddingId, id)
        );

        list.forEach(x->{
            x.setFeeRecordList(baseMapper.selectList(
                    new LambdaQueryWrapper<SelBiddingFeeRecordEntity>()
                            .eq(SelBiddingFeeRecordEntity::getSequenceId,x.getId()))
            );
        });
        return list;
    }
}