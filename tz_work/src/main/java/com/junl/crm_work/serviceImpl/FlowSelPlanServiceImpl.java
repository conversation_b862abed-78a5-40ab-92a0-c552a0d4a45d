package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.FlowSelPlanDao;
import com.junl.crm_work.vo.FlowSelPlanVo;
import com.junl.crm_common.pojo.work.FlowSelPlanEntity;
import com.junl.crm_work.service.FlowSelPlanService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class FlowSelPlanServiceImpl extends ServiceImpl<FlowSelPlanDao, FlowSelPlanEntity>
        implements FlowSelPlanService
{
    /**
     * @description: 分页查询列表
     * @param flowSelPlanVo
     */
    @Override
    public PageEntity queryPage(FlowSelPlanVo flowSelPlanVo) {
        PageUtils.execute(flowSelPlanVo);
        List<FlowSelPlanEntity> list = baseMapper.queryList(flowSelPlanVo);
        return PageUtils.getData(list);
    }
}