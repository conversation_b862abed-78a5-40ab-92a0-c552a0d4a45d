package com.junl.crm_work.serviceImpl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.work.SelApprovalUnderwayEntity;
import com.junl.crm_work.dao.SelApprovalUnderwayDao;
import com.junl.crm_work.service.SelApprovalUnderwayService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelApprovalUnderwayServiceImpl
        extends ServiceImpl<SelApprovalUnderwayDao, SelApprovalUnderwayEntity>
        implements SelApprovalUnderwayService
{

    @Override
    public SelApprovalUnderwayEntity queryFlowAll(String id) {
        return baseMapper.queryFlowAll(id);
    }
}