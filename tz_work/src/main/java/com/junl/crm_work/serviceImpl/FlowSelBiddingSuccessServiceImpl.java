package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.FlowSelBiddingSuccessDao;
import com.junl.crm_work.vo.FlowSelBiddingSuccessVo;
import com.junl.crm_common.pojo.work.FlowSelBiddingSuccessEntity;
import com.junl.crm_work.service.FlowSelBiddingSuccessService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class FlowSelBiddingSuccessServiceImpl
        extends ServiceImpl<FlowSelBiddingSuccessDao, FlowSelBiddingSuccessEntity>
        implements FlowSelBiddingSuccessService
{
    /**
     * @description: 分页查询列表
     * @param flowSelBiddingSuccessVo
     */
    @Override
    public PageEntity queryPage(FlowSelBiddingSuccessVo flowSelBiddingSuccessVo) {
        PageUtils.execute(flowSelBiddingSuccessVo);
        List<FlowSelBiddingSuccessEntity> list = baseMapper.queryList(flowSelBiddingSuccessVo);
        return PageUtils.getData(list);
    }
}