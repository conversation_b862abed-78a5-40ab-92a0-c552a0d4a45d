package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.FlowSelBiddingDao;
import com.junl.crm_work.vo.FlowSelBiddingVo;
import com.junl.crm_common.pojo.work.FlowSelBiddingEntity;
import com.junl.crm_work.service.FlowSelBiddingService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class FlowSelBiddingServiceImpl extends ServiceImpl<FlowSelBiddingDao, FlowSelBiddingEntity>
        implements FlowSelBiddingService
{
    /**
     * @description: 分页查询列表
     * @param flowSelBiddingVo
     */
    @Override
    public PageEntity queryPage(FlowSelBiddingVo flowSelBiddingVo) {
        PageUtils.execute(flowSelBiddingVo);
        List<FlowSelBiddingEntity> list = baseMapper.queryList(flowSelBiddingVo);
        return PageUtils.getData(list);
    }
}