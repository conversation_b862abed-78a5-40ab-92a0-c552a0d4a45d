package com.junl.crm_work.serviceImpl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.AuditDto;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_work.dao.SelApprovalDefinitionDao;
import com.junl.crm_work.dao.SelInquiryDao;
import com.junl.crm_work.exception.ExamineException;
import com.junl.crm_work.listener.SmsListener;
import com.junl.crm_work.service.*;
import com.junl.crm_work.status.*;
import com.junl.crm_work.util.BusUtils;
import com.junl.crm_work.vo.SelApprovalFlowVo;
import com.junl.crm_work.wx.SendMsgEntity;
import com.junl.crm_work.wx.WxTemplateMsg;
import com.junl.crm_work.wx.WxTemplateMsgDev;
import com.junl.crm_work.wx.WxUtils;
import com.junl.msg.common.MsgType;
import com.junl.system.UserMsgService;
import com.junl.system.UserService;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
@Log4j2
public class SelApprovalDefinitionServiceImpl extends ServiceImpl<SelApprovalDefinitionDao, SelApprovalDefinitionEntity> implements SelApprovalDefinitionService {

    @Autowired
    private SelApprovalFlowService selApprovalFlowService;

    @Autowired
    private SelApprovalUnderwayService selApprovalUnderwayService;


    @Autowired
    private SelInquiryDao selInquiryDao;

    @Autowired
    @Lazy
    private SelBiddingService selBiddingService;

    @Autowired
    private SelBiddingSuccessService selBiddingSuccessService;

    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SelPlanService selPlanService;

    @Autowired
    private SelBiddingSuccessProductService selBiddingSuccessProductService;

    @Reference(lazy = true)
    private UserService userService;

    @Reference(lazy = true)
    private UserMsgService userMsgService;

    @Autowired
    private WxUtils wxUtils;

    @Autowired
    @Lazy
    private SmsListener smsListener;


    @Override
    public List<SelApprovalDefinitionEntity> getList(SysUserEntity sysUserEntity) {
        String companyId = sysUserEntity.getCompanyId();
        return baseMapper.getList(companyId);
    }

    @Override
    public SelApprovalDefinitionEntity getInfo(String id) {
        SelApprovalDefinitionEntity selApprovalDefinitionEntity = baseMapper.selectById(id);
        selApprovalDefinitionEntity.setTreeData(JSONObject.parseObject(selApprovalDefinitionEntity.getSchedule(), AuditDto.class));
        return selApprovalDefinitionEntity;
    }

    @Override
    public boolean submit(SelApprovalFlowVo selApprovalFlowVo) {
        /** 可改用策略模式完成审批处理 */
        //审核消息体
        SysUserMsgEntity sysUserMsgEntity = selApprovalFlowVo.getSysUserMsgEntity();
        //审批结果
        Integer result = selApprovalFlowVo.getResult();
        //业务参数  第一位 是业务主键  第二位是审批节点主键
        String[] param = sysUserMsgEntity.getMsgAccessory().split(",");
        //公司id
        String companyId = sysUserMsgEntity.getCompanyId();



        //流程节点
        SelApprovalFlowEntity flowEntity = selApprovalFlowService.getById(param[1]);

        SelApprovalUnderwayEntity byId = selApprovalUnderwayService.getById(flowEntity.getUnderwayId());


        if (byId.getIsRepeal().equals(Integer.valueOf(Opposite.SINGLE))) {
            //历史消息标识更新
            if(Assert.notNull(sysUserMsgEntity.getMsgId())){
                userMsgService.updateStatus(sysUserMsgEntity.getMsgId());
            }
            throw new ExamineException("审批已撤回.");
        }

        //更新主流程表示该节点已经进入审批阶段
        SelApprovalUnderwayEntity underwayEntity=new SelApprovalUnderwayEntity();
        underwayEntity.setId(byId.getId());
        underwayEntity.setIsStart(Integer.valueOf(Opposite.SINGLE));
        selApprovalUnderwayService.updateById(underwayEntity);




        if (!flowEntity.getResult().equals(Opposite.ZERO)) {
            throw new ExamineException("流程已审批.");
        }

        String message=null;

        //流程子节点
        SelApprovalFlowEntity child = selApprovalFlowService.getOne(
                new LambdaQueryWrapper<SelApprovalFlowEntity>()
                        .eq(SelApprovalFlowEntity::getParentId, flowEntity.getId())
        );

        /** start 销售计划处理 ***********************************************/
        if (sysUserMsgEntity.getSpecific().equals(ApproveStatus.JH_SH.getCode())) {
            SelPlanEntity planEntity = selPlanService.getById(param[0]);
            //审批被撤销  不做任何操作;

//            if (planEntity.getPlanStatus().equals(PlanStatus.UNCOMMITTED.getCode())) {
//                return false;
//            }
            //通过
            if (result.equals(Integer.valueOf(Opposite.SINGLE))) {
                //判断是否还有下一个节点
                if (Assert.notNull(child)) {
                    //有就发送给下一个节点的审批人
                    sendMsg(ApproveStatus.JH_SH.getCode(),param[0]+","+child.getId(),child.getApprover(),companyId);
                }else{
                    //没有就处理业务流程

                    SelPlanEntity tempPlan=new SelPlanEntity();

                    //竞价竟量模式
                    if(planEntity.getModeType().equals(PlanModel.BIDDING_AND_AMOUNT.getCode())){
                        SelInquiryEntity selInquiryEntity=new SelInquiryEntity();
                        selInquiryEntity.setId(SnowFlake.getUUId());
                        selInquiryEntity.setPlanId(planEntity.getId());
                        selInquiryEntity.setCompanyId(planEntity.getCompanyId());
                        selInquiryEntity.setModeType(planEntity.getModeType());
                        selInquiryEntity.setInquiryStatus(InquiryStatus.NOT_ISSUE.getCode());
                        selInquiryEntity.setPeriodNum(planEntity.getPeriodNum());
                        selInquiryEntity.setInquiryName(planEntity.getPlanName());
                        selInquiryEntity.setCreateBy(planEntity.getCreateBy());
                        selInquiryDao.insert(selInquiryEntity);

                    }

                    //更新状态
                    tempPlan.setId(planEntity.getId());
                    tempPlan.setPlanStatus(PlanStatus.PASS.getCode());
                    selPlanService.updateById(tempPlan);


                    /** 未完待续 */
                    updateFlowStatus(param[1]);
                }
            }else{
                //未通过
                //获取流程id
                String flowId =param[1];
                updateFlowStatus(flowId);

                message=planEntity.getPlanName()+"销售计划";
                //销售计划状态改变
                SelPlanEntity tempPlan=new SelPlanEntity();
                tempPlan.setPlanStatus(PlanStatus.REJECT.getCode());
                tempPlan.setId(planEntity.getId());
                selPlanService.updateById(tempPlan);

            }
            /**  end  销售计划处理 ***********************************************/

            /**  start  竞价审核处理 ***********************************************/
        }else if(sysUserMsgEntity.getSpecific().equals(ApproveStatus.ZJ_SH.getCode())){
            //竞价审核
            if (result.equals(Integer.valueOf(Opposite.SINGLE))) {
                //通过
                //判断是否还有下一个节点
                if (Assert.notNull(child)) {
                    //有就发送给下一个节点的审批人
                    sendMsg(ApproveStatus.ZJ_SH.getCode(),param[0]+","+child.getId(),child.getApprover(),companyId);
                }else {
                    SelBiddingEntity biddingEntity = selBiddingService.getById(param[0]);
                    SelBiddingEntity tempBidding=new SelBiddingEntity();
                    tempBidding.setBiddingStatus(BiddingStatus.AUDIT_SUCCESS.getCode());
                    tempBidding.setId(biddingEntity.getId());
                    selBiddingService.updateById(tempBidding);

                    //查询轮次信息
                    Integer roundNum = biddingEntity.getRoundNum();
                    SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                            new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                            .and(e -> e.eq(SelBiddingSequenceEntity::getSequence,roundNum)
                                    .eq(SelBiddingSequenceEntity::getBiddingId, biddingEntity.getId())));
                    sequence.setBiddingStatus(BiddingStatus.AUDIT_SUCCESS.getCode());
                    selBiddingSequenceService.updateById(sequence);


                    updateFlowStatus(param[1]);

                    //判断竞价状态是否需要改变
                    selBiddingService.updateStatus(biddingEntity.getId());

                    long start = DateUtils.compareSecond(DateUtils.getNow(), biddingEntity.getPlanStartDate());
                    if(start>0){
                        redisUtils.set(RedisKey.BIDDING.getName()+biddingEntity.getId(), Opposite.SINGLE,start);
                    }else{
                        long end = DateUtils.compareSecond(DateUtils.getNow(), biddingEntity.getPlanEndTime());
                        if (end>0) {
                            redisUtils.set(RedisKey.BIDDING.getName()+biddingEntity.getId(), Opposite.SINGLE,end);
                        }
                    }

                    //查询出该轮次邀请的客户
                    List<SelBiddingCustomerEntity> list = selBiddingCustomerService.list(
                            new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                            .eq(SelBiddingCustomerEntity::getSequence, sequence.getId())
                    );


                    //通知商家
                    SysUserMsgEntity msg=new SysUserMsgEntity();
                    msg.setMsgType(MsgType.SMS.getCode());
                    msg.setBusCode(BusCode.BIDDING_COMPANY.getCode());
                    msg.setMsgAccessory(CommonUtil.collect(list,SelBiddingCustomerEntity::getCustomerId,","));
                    msg.setMsgContent(biddingEntity.getId()+","+roundNum);
                    msg.setCompanyId(biddingEntity.getCompanyId());
//                    rabbitMqUtils.sendOnlyMsg(msg,MsgType.SMS.getKey());

                    smsListener.dispose(msg);

                    //加入结束前10分钟再次通知用户
                    long second = DateUtils.compareSecond(DateUtils.getNow(), biddingEntity.getPlanEndTime());
                    //大于10分钟
                    if(second>600){
                        redisUtils.set(
                                RedisKey.BIDDING_NOTICE.getName()+biddingEntity.getId(),
                                Opposite.SINGLE,second-600
                        );
                    }

                }
            }else{
                //获取流程id
                String flowId =param[1];
                updateFlowStatus(flowId);

                //未通过  更新状态即可
                SelBiddingEntity biddingEntity = selBiddingService.getById(param[0]);
                SelBiddingEntity tempBidding=new SelBiddingEntity();
                tempBidding.setBiddingStatus(BiddingStatus.AUDIT_ERROR.getCode());
                tempBidding.setId(biddingEntity.getId());
                selBiddingService.updateById(tempBidding);


                message=biddingEntity.getPlanName()+"竞价";

            }
            /**  end  竞价审核处理 ***********************************************/



            /**  start  交易完成处理 ***********************************************/
        }else if(sysUserMsgEntity.getSpecific().equals(ApproveStatus.JY_WC.getCode())){
            //交易完成
            SelBiddingEntity biddingEntity = selBiddingService.getById(param[0]);
            if (result.equals(Integer.valueOf(Opposite.SINGLE))) {
                //通过
                //判断是否还有下一个节点
                if (Assert.notNull(child)) {
                    //有就发送给下一个节点的审批人
                    sendMsg(ApproveStatus.JY_WC.getCode(),param[0]+","+child.getId(),child.getApprover(),companyId);
                }else {
                    //改变状态审核通过
                    SelBiddingEntity tempBidding=new SelBiddingEntity();
                    tempBidding.setBiddingStatus(BiddingStatus.SUCCESS.getCode());
                    tempBidding.setId(biddingEntity.getId());
                    selBiddingService.updateById(tempBidding);

                    //查询轮次信息
                    Integer roundNum = biddingEntity.getRoundNum();
                    SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                            new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                            .and(e -> e.eq(SelBiddingSequenceEntity::getSequence,roundNum)
                                    .eq(SelBiddingSequenceEntity::getBiddingId, biddingEntity.getId())));
                    sequence.setBiddingStatus(BiddingStatus.SUCCESS.getCode());
                    selBiddingSequenceService.updateById(sequence);



                    updateFlowStatus(param[1]);

                    //找到成功的商家
                    List<SelBiddingSuccessEntity> list = selBiddingSuccessService.list(
                            new QueryWrapper<SelBiddingSuccessEntity>()
                                    .select("offer_id")
                                    .eq("bidding_id", biddingEntity.getId())
                    );
                    List<String> collect = CommonUtil.collect(list, SelBiddingSuccessEntity::getOfferId);
                    //多个商品报价商家有重复 需要去重
                    Set<String>  set=new HashSet<>(collect);

                    /** 发送消息给商家*/
                    SysUserMsgEntity msg=new SysUserMsgEntity();
                    msg.setMsgType(MsgType.SMS.getCode());
                    msg.setBusCode(BusCode.TRANSACTION_SUCCESS_COMPANY.getCode());
                    msg.setMsgAccessory(CommonUtil.collect(set,String::toString,","));
                    msg.setMsgContent(biddingEntity.getId()+","+biddingEntity.getRoundNum());
                    msg.setCompanyId(biddingEntity.getCompanyId());

                    smsListener.dispose(msg);
                }
            }else{
                //获取流程id
                String flowId =param[1];
                updateFlowStatus(flowId);

                //未通过
                SelBiddingEntity tempBidding=new SelBiddingEntity();
                tempBidding.setBiddingStatus(BiddingStatus.BIDDING_AUDIT_OVER_ERROR.getCode());
                tempBidding.setId(biddingEntity.getId());
                selBiddingService.updateById(tempBidding);

                //查询轮次信息
                Integer roundNum = biddingEntity.getRoundNum();
                SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                        new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                                .and(e -> e.eq(SelBiddingSequenceEntity::getSequence,roundNum)
                                        .eq(SelBiddingSequenceEntity::getBiddingId, biddingEntity.getId())));
                sequence.setBiddingStatus(BiddingStatus.BIDDING_AUDIT_OVER_ERROR.getCode());
                selBiddingSequenceService.updateById(sequence);


                //删除成功的商家让操作人重新选择提交
//                selBiddingSuccessService.remove(new LambdaQueryWrapper<SelBiddingSuccessEntity>().eq(SelBiddingSuccessEntity::getBiddingId,biddingEntity.getId()));
                message=biddingEntity.getPlanName()+"交易成功";


                //查询报价成功的企业主表
                List<SelBiddingSuccessEntity> success_id = selBiddingSuccessService.list(
                        new QueryWrapper<SelBiddingSuccessEntity>()
                                .select("id")
                                .lambda()
                                .eq(SelBiddingSuccessEntity::getBiddingId, biddingEntity.getId())
                );

                //删除成功的报价企业信息
                selBiddingSuccessService.remove(
                        new LambdaQueryWrapper<SelBiddingSuccessEntity>()
                                .eq(SelBiddingSuccessEntity::getBiddingId,biddingEntity.getId())
                );

                //删除成功的商品
                selBiddingSuccessProductService.remove(
                        new LambdaQueryWrapper<SelBiddingSuccessProductEntity>()
                        .in(
                                SelBiddingSuccessProductEntity::getSuccessId,
                                CommonUtil.collect(success_id,SelBiddingSuccessEntity::getId)
                        )
                );

            }
            /**  end  交易完成处理 ***********************************************/
        }else {
            throw new RuntimeException("未知的审核任务类型"+sysUserMsgEntity.getSpecific());
        }

        //不等于1  驳回通知所有父节点
        if(!selApprovalFlowVo.getResult().equals(Integer.valueOf(Opposite.SINGLE))){
            //通知父节点已被驳回
            notice(flowEntity.getParentId(),message,userService.getName(flowEntity.getApprover()));
        }

        //把节点结果更新
        flowEntity.setResult(selApprovalFlowVo.getResult().toString());
        flowEntity.setRemark(selApprovalFlowVo.getRemark());
        flowEntity.setUpdateTime(DateUtils.getNow());
        selApprovalFlowService.updateById(flowEntity);


        //历史消息标识更新
        if(Assert.notNull(sysUserMsgEntity.getMsgId())){
            userMsgService.updateStatus(sysUserMsgEntity.getMsgId());
        }
        return true;
    }

    @Override
    public List<SelApprovalUnderwayEntity> getAuditRecord(SelApprovalFlowVo selApprovalFlowVo) {
        List<SelApprovalUnderwayEntity> auditRecord = baseMapper.getAuditRecord(selApprovalFlowVo);
        auditRecord.forEach(x->{
            x.setFlowEntityList(selPlanService.getFlow(x.getId()));
        });
        return auditRecord;
    }

    private void notice(String parentId,String message,String submit){
        if(!parentId.equals(IdentityStatus.GOD.getName())){
            SelApprovalFlowEntity byId = selApprovalFlowService.getById(parentId);
            String approver = byId.getApprover();
            SysUserMsgEntity msg=new SysUserMsgEntity();
            msg.setMsgId(SnowFlake.getUUId());
            msg.setMsgType(MsgType.USER.getCode());
            msg.setBusCode(BusCode.NOTICE.getCode());
            msg.setMsgTitle("审批驳回通知");
            msg.setMsgContent(message+"审批已被驳回.  驳回人:"+submit);
            msg.setReader(approver);
            userMsgService.saveHistory(msg);

            ThreadPoolUtil.runTask(
                    () -> {
                        BusUtils.sendMsg(approver);
                    }
            );

            notice(byId.getParentId(),message,submit);
        }
    }


    /**
     * @describe: 改变处理完的正在进行中的流程状态
     * <AUTHOR>
     * @date 2021/7/23 11:16
     * @param id
     */
    private void updateFlowStatus(String id){
        SelApprovalFlowEntity flowEntity = selApprovalFlowService.getById(id);
        //该流程结束
        SelApprovalUnderwayEntity underwayEntity = selApprovalUnderwayService.getById(flowEntity.getUnderwayId());
        SelApprovalUnderwayEntity tempEntity=new SelApprovalUnderwayEntity();
        tempEntity.setIsDelete(Integer.valueOf(Opposite.SINGLE));
        tempEntity.setId(underwayEntity.getId());
        selApprovalUnderwayService.updateById(tempEntity);
    }

    /**
     * @describe: 发送审批消息
     * <AUTHOR>
     * @date 2021/7/23 11:14
     * @param code 业务编码
     * @param param 参数
     * @param approver 读取人
     */
    private void sendMsg(Integer code,String param,String approver,String companyId){

        SendMsgEntity sendMsgEntity= new SendMsgEntity();

        SysUserMsgEntity userMsg=new SysUserMsgEntity();
        userMsg.setMsgId(SnowFlake.getUUId());
        userMsg.setBusCode(BusCode.USER_CHECK.getCode());
        userMsg.setMsgType(MsgType.EXAMINE.getCode());
        userMsg.setReader(approver);
        userMsg.setMsgAccessory(param);
        userMsg.setCompanyId(companyId);
        userMsg.setSpecific(code);
        if (code.equals(ApproveStatus.JH_SH.getCode())) {
            userMsg.setMsgTitle("销售计划审批");
            sendMsgEntity.setWord1("销售计划");
        }else if(code.equals(ApproveStatus.ZJ_SH.getCode())){
            userMsg.setMsgTitle("商品竞价审批");
            sendMsgEntity.setWord1("商品竞价");
        }else if(code.equals(ApproveStatus.JY_WC.getCode())){
            userMsg.setMsgTitle("交易完成审批");
            sendMsgEntity.setWord1("交易完成");
        }
        userMsgService.saveHistory(userMsg);

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(approver);
                }
        );

//        rabbitMqUtils.sendOnlyMsg(userMsg,MsgType.USER.getKey());

        SysUserSoleEntity sole = userService.getSole(approver);

        if(Assert.notNull(sole.getOpenId())){
            sendMsgEntity.setUser(sole.getOpenId());
            sendMsgEntity.setTitle("您有一条新的记录待审批");
            sendMsgEntity.setWord2(DateUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));

            //根据流程节点信息查询提交人
            String flowId = param.split(",")[1];
            SelApprovalFlowEntity byId = selApprovalFlowService.getById(flowId);
            String underwayId = byId.getUnderwayId();
            SelApprovalUnderwayEntity underwayEntity = selApprovalUnderwayService.getById(underwayId);
            String createBy = underwayEntity.getCreateBy();
            String name = userService.getName(createBy);


            sendMsgEntity.setWord3(name);
            sendMsgEntity.setRemark("提交公司： "+redisUtils.get(companyId));

            //发送公众号消息给审批人
            String properties = SpringUtil.getProperties("wx.template_msg1");

            try {
                if (SpringUtil.isProd()) {
                    WxTemplateMsg assembly = wxUtils.assembly(sendMsgEntity);
                    assembly.setTemplate_id(properties);
                    wxUtils.sendPost(assembly);
                }else {
                    WxTemplateMsgDev wxTemplateMsgDev = wxUtils.assemblyDev(sendMsgEntity);
                    wxTemplateMsgDev.setTemplate_id(properties);
                    wxUtils.sendPostDev(wxTemplateMsgDev);
                }
            }catch (Exception e){
                log.error("推送公众号信息失败: {}",e.getMessage());
            }

        }
    }

}