package com.junl.crm_work.serviceImpl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.SelBiddingProductDao;
import com.junl.crm_work.dao.SelBiddingSuccessDao;
import com.junl.crm_work.service.SelBiddingSuccessService;
import com.junl.crm_work.vo.SelBiddingProductVo;
import com.junl.crm_work.vo.SelBiddingSuccessVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelBiddingSuccessServiceImpl
        extends ServiceImpl<SelBiddingSuccessDao, SelBiddingSuccessEntity>
        implements SelBiddingSuccessService
{

    @Autowired
    private SelBiddingSuccessDao selBiddingSuccessDao;

    /**
     * @description: 分页查询列表
     * @param selBiddingSuccessVo
     */
    @Override
    public PageEntity queryPage(SelBiddingSuccessVo selBiddingSuccessVo) {
        PageUtils.execute(selBiddingSuccessVo);
        List<SelBiddingSuccessEntity> list = baseMapper.queryList(selBiddingSuccessVo);
        return PageUtils.getData(list);
    }


    /**
     * @description: 分页查询列表
     * @param selBiddingSuccessVo
     */
    @Override
    public PageEntity queryAppPage(SelBiddingSuccessVo selBiddingSuccessVo) {
        //系统参数
        String offerId = selBiddingSuccessVo.getOfferId();
        PageUtils.execute(selBiddingSuccessVo);
        List<SelBiddingSuccessEntity> list = baseMapper.queryAppList(selBiddingSuccessVo);
        PageEntity<SelBiddingSuccessEntity> data = PageUtils.getData(list);
        List<SelBiddingSuccessEntity> successEntities = Lists.newArrayList();
        successEntities.addAll(data.getList().stream().map(item ->
                {
                    SelBiddingSuccessEntity newSuccess = new SelBiddingSuccessEntity();
                    BeanUtils.copyProperties(item, newSuccess);
                    String sequenceId = item.getSequenceId();
                    SelBiddingSuccessVo biddingSuccessVo = new SelBiddingSuccessVo();
                    biddingSuccessVo.setSequenceId(sequenceId);
                    biddingSuccessVo.setOfferId(offerId);
                    List<SelBiddingSuccessProductEntity> productlist = selBiddingSuccessDao.getAppList(biddingSuccessVo);
                    newSuccess.setSuccessList(productlist);
                    return newSuccess;
                }
        ).collect(Collectors.toList()));
        data.setList(successEntities);
        return data;
    }

    /**
     * @description: 查询列表
     * @param selBiddingSuccessVo
     */
    @Override
    public Result queryList(SelBiddingSuccessVo selBiddingSuccessVo,SelCustomerEntity customerEntity) {
        selBiddingSuccessVo.setOfferId(customerEntity.getId());
        List<SelBiddingSuccessProductEntity> list = baseMapper.getAppList(selBiddingSuccessVo);
        return Result.success(list);
    }

    @Override
    public List<SelBiddingSuccessProductEntity> querySuccessCount(List<String> biddingIds, String productId,String customerId) {
        return baseMapper.querySuccessCount(biddingIds,productId,customerId);
    }
}