package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_work.dao.SelInquiryCustomerDao;
import com.junl.crm_work.vo.SelInquiryCustomerVo;
import com.junl.crm_common.pojo.work.SelInquiryCustomerEntity;
import com.junl.crm_work.service.SelInquiryCustomerService;
import org.springframework.stereotype.Service;

/**
* @author: daiqimeng
* @create: 2020-07-21 11:11
**/
@Service
public class SelInquiryCustomerServiceImpl
        extends ServiceImpl<SelInquiryCustomerDao, SelInquiryCustomerEntity>
        implements SelInquiryCustomerService
{
/**
* @description: 分页查询列表
* @param selInquiryCustomerVo
*/
@Override
public PageEntity queryPage(SelInquiryCustomerVo selInquiryCustomerVo) {
    PageUtils.execute(selInquiryCustomerVo);
    List<SelInquiryCustomerEntity> list = baseMapper.queryList(selInquiryCustomerVo);
    return PageUtils.getData(list);
  }
}