package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.ExcelResult;
import com.junl.crm_common.pojo.admin.SysPositionEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

import com.junl.crm_common.tools.excel.CustomSheetWriteHandler;
import com.junl.crm_common.tools.excel.ExcelUtils;
import com.junl.crm_work.dao.SelProductDao;
import com.junl.crm_work.excel.ProductService;
import com.junl.crm_work.util.CodeGenerate;
import com.junl.crm_work.vo.SelProductVo;
import com.junl.crm_common.pojo.work.SelProductEntity;
import com.junl.crm_work.service.SelProductService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelProductServiceImpl
        extends ServiceImpl<SelProductDao, SelProductEntity>
        implements SelProductService
{

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    private CodeGenerate codeGenerate;
    /**
     * @description: 分页查询列表
     * @param selProductVo
     */
    @Override
    public PageEntity queryPage(SelProductVo selProductVo) {
        PageUtils.execute(selProductVo);
        List<SelProductEntity> list = baseMapper.queryList(selProductVo);
        return PageUtils.getData(list);
    }

    @Override
    public String download(ByteArrayOutputStream byteArrayOutputStream) {
        ArrayList<SelProductEntity> selProductEntitys = new ArrayList<>();
        selProductEntitys.add(new SelProductEntity());
        String[] arg={"燃料油类","化工类","苯类","其它"};
        excelUtils.write(
                selProductEntitys,
                SelProductEntity.class,
                byteArrayOutputStream,
                null,
                new CustomSheetWriteHandler(2,10000,1,1,arg)
        );
        return CommonUtil.getExcelBase64(byteArrayOutputStream);
    }

    @Override
    public ExcelResult importExcel(String url, SysUserEntity sysUserEntity) {
        ExcelResult excelResult=new ExcelResult();
        excelUtils.read(
                url,
                SelProductEntity.class,
                new ProductService(codeGenerate,sysUserEntity,this)
                ,excelResult
        );
        return excelResult;
    }
}