package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelGroupRelevanceEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_work.dao.SelCustomerDao;
import com.junl.crm_work.dao.SelCustomerGroupDao;
import com.junl.crm_work.service.SelGroupRelevanceService;
import com.junl.crm_work.vo.SelCustomerGroupVo;
import com.junl.crm_common.pojo.work.SelCustomerGroupEntity;
import com.junl.crm_work.service.SelCustomerGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelCustomerGroupServiceImpl
        extends ServiceImpl<SelCustomerGroupDao, SelCustomerGroupEntity>
        implements SelCustomerGroupService
{

    @Autowired
    private SelGroupRelevanceService selGroupRelevanceService;

    @Autowired
    private SelCustomerDao selCustomerDao;


    /**
     * @description: 分页查询列表
     * @param selCustomerGroupVo
     */
    @Override
    public PageEntity queryPage(SelCustomerGroupVo selCustomerGroupVo) {
        PageUtils.execute(selCustomerGroupVo);
        List<SelCustomerGroupEntity> list = baseMapper.queryList(selCustomerGroupVo);
        return PageUtils.getData(list);
    }

    @Override
    public SelCustomerGroupEntity getInfo(String id) {
        return baseMapper.getInfo(id);
    }

    @Override
    @Transactional
    public boolean saveGroup(SelCustomerGroupEntity selCustomerGroupEntity) {
        //删除组内关系
        String id = selCustomerGroupEntity.getId();
        selGroupRelevanceService.remove(
                new LambdaQueryWrapper<SelGroupRelevanceEntity>()
                        .eq(SelGroupRelevanceEntity::getGroupId,id)
        );

        List<SelGroupRelevanceEntity> relevanceEntities = selCustomerGroupEntity.getRelevanceEntities();
        if(Assert.notNullCollect(relevanceEntities)){
            relevanceEntities.forEach(x->{
                x.setGroupId(id);
                x.setId(SnowFlake.getUUId());
            });
            selGroupRelevanceService.saveBatch(relevanceEntities);
        }
        return true;
    }

    @Override
    public List<SelGroupRelevanceEntity> queryGroupCustomer(String id) {

        return baseMapper.queryGroupCustomer(id);
    }
}