package com.junl.crm_work.serviceImpl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.work.SelBiddingProductEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import com.junl.crm_work.dao.SelBiddingProductDao;
import com.junl.crm_work.vo.SelBiddingProductVo;

import com.junl.crm_work.service.SelBiddingProductService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelBiddingProductServiceImpl
        extends ServiceImpl<SelBiddingProductDao, SelBiddingProductEntity>
        implements SelBiddingProductService
{
    /**
     * @param selBiddingProductVo
     * @description: 分页查询列表
     */
    @Override
    public PageEntity queryPage(SelBiddingProductVo selBiddingProductVo) {
        PageUtils.execute(selBiddingProductVo);
        List<SelBiddingProductEntity> list = baseMapper.queryList(selBiddingProductVo);
        return PageUtils.getData(list);
    }


    /**
     * @param selBiddingProductVo
     * @description: 查询列表
     */
    @Override
    public List<SelBiddingProductEntity> getAppList(SelBiddingProductVo selBiddingProductVo) {
        List<SelBiddingProductEntity> list = baseMapper.queryAppList(selBiddingProductVo);
        return list;
    }
}