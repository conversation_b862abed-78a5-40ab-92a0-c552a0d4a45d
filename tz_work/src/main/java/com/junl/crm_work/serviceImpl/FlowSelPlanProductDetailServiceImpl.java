package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.FlowSelPlanProductDetailDao;
import com.junl.crm_work.vo.FlowSelPlanProductDetailVo;
import com.junl.crm_common.pojo.work.FlowSelPlanProductDetailEntity;
import com.junl.crm_work.service.FlowSelPlanProductDetailService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class FlowSelPlanProductDetailServiceImpl
        extends ServiceImpl<FlowSelPlanProductDetailDao, FlowSelPlanProductDetailEntity>
        implements FlowSelPlanProductDetailService
{
    /**
     * @description: 分页查询列表
     * @param flowSelPlanProductDetailVo
     */
    @Override
    public PageEntity queryPage(FlowSelPlanProductDetailVo flowSelPlanProductDetailVo) {
        PageUtils.execute(flowSelPlanProductDetailVo);
        List<FlowSelPlanProductDetailEntity> list = baseMapper.queryList(flowSelPlanProductDetailVo);
        return PageUtils.getData(list);
    }
}