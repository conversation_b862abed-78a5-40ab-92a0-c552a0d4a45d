package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_work.dao.SelGroupRelevanceDao;
import com.junl.crm_work.vo.SelGroupRelevanceVo;
import com.junl.crm_common.pojo.work.SelGroupRelevanceEntity;
import com.junl.crm_work.service.SelGroupRelevanceService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelGroupRelevanceServiceImpl
        extends ServiceImpl<SelGroupRelevanceDao, SelGroupRelevanceEntity>
        implements SelGroupRelevanceService
{
    /**
     * @description: 分页查询列表
     * @param selGroupRelevanceVo
     */
    @Override
    public PageEntity queryPage(SelGroupRelevanceVo selGroupRelevanceVo) {
        PageUtils.execute(selGroupRelevanceVo);
        List<SelGroupRelevanceEntity> list = baseMapper.queryList(selGroupRelevanceVo);
        return PageUtils.getData(list);
    }
}