package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.FlowSelBiddingProductDao;
import com.junl.crm_work.vo.FlowSelBiddingProductVo;
import com.junl.crm_common.pojo.work.FlowSelBiddingProductEntity;
import com.junl.crm_work.service.FlowSelBiddingProductService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class FlowSelBiddingProductServiceImpl extends ServiceImpl<FlowSelBiddingProductDao, FlowSelBiddingProductEntity>
        implements FlowSelBiddingProductService
{
    /**
     * @description: 分页查询列表
     * @param flowSelBiddingProductVo
     */
    @Override
    public PageEntity queryPage(FlowSelBiddingProductVo flowSelBiddingProductVo) {
        PageUtils.execute(flowSelBiddingProductVo);
        List<FlowSelBiddingProductEntity> list = baseMapper.queryList(flowSelBiddingProductVo);
        return PageUtils.getData(list);
    }
}