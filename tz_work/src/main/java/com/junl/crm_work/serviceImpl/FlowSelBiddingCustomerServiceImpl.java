package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_work.dao.FlowSelBiddingCustomerDao;
import com.junl.crm_work.vo.FlowSelBiddingCustomerVo;
import com.junl.crm_common.pojo.work.FlowSelBiddingCustomerEntity;
import com.junl.crm_work.service.FlowSelBiddingCustomerService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class FlowSelBiddingCustomerServiceImpl extends ServiceImpl<FlowSelBiddingCustomerDao, FlowSelBiddingCustomerEntity>
        implements FlowSelBiddingCustomerService
{
    /**
     * @description: 分页查询列表
     * @param flowSelBiddingCustomerVo
     */
    @Override
    public PageEntity queryPage(FlowSelBiddingCustomerVo flowSelBiddingCustomerVo) {
        PageUtils.execute(flowSelBiddingCustomerVo);
        List<FlowSelBiddingCustomerEntity> list = baseMapper.queryList(flowSelBiddingCustomerVo);
        return PageUtils.getData(list);
    }
}