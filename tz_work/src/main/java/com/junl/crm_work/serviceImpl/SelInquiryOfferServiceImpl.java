package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

import com.junl.crm_work.dao.SelInquiryOfferDao;
import com.junl.crm_work.vo.SelInquiryOfferVo;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_work.service.SelInquiryOfferService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelInquiryOfferServiceImpl
        extends ServiceImpl<SelInquiryOfferDao, SelInquiryOfferEntity>
        implements SelInquiryOfferService
{
    /**
     * @param selInquiryOfferVo
     * @description: 分页查询列表
     */
    @Override
    public PageEntity queryPage(SelInquiryOfferVo selInquiryOfferVo) {
        PageUtils.execute(selInquiryOfferVo);
        List<SelInquiryOfferEntity> list = baseMapper.queryList(selInquiryOfferVo);
        return PageUtils.getData(list);
    }

    /**
     * @param selInquiryOfferVo
     * @description: 列表查询列表
     */
    @Override
    public Result queryList(SelInquiryOfferVo selInquiryOfferVo, SelCustomerEntity customerEntity) {
        selInquiryOfferVo.setCustomerId(customerEntity.getId());
        List<SelInquiryOfferEntity> list = baseMapper.queryList(selInquiryOfferVo);
        return  Result.success(list);
    }
}