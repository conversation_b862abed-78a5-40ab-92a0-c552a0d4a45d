package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_work.dao.SelModeDao;
import com.junl.crm_work.vo.SelModeVo;
import com.junl.crm_common.pojo.work.SelModeEntity;
import com.junl.crm_work.service.SelModeService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelModeServiceImpl
        extends ServiceImpl<SelModeDao, SelModeEntity>
        implements SelModeService
{
    /**
     * @description: 分页查询列表
     * @param selModeVo
     */
    @Override
    public PageEntity queryPage(SelModeVo selModeVo) {
        PageUtils.execute(selModeVo);
        List<SelModeEntity> list = baseMapper.queryList(selModeVo);
        return PageUtils.getData(list);
    }
}