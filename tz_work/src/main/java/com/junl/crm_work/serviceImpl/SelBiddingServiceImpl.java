package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.constant.BiddingModeTypeEnum;
import com.junl.crm_common.pojo.ReportPlan;
import com.junl.crm_common.pojo.SuccessExcel;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.work.*;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_common.tools.excel.ExcelUtils;
import com.junl.crm_work.bo.InitFlowBO;
import com.junl.crm_work.dao.*;
import com.junl.crm_work.service.*;
import com.junl.crm_work.status.ApproveStatus;
import com.junl.crm_work.status.BiddingStatus;
import com.junl.crm_work.status.BusCode;
import com.junl.crm_work.threadTask.BiddingTaskQuery;
import com.junl.crm_work.util.BusUtils;
import com.junl.crm_work.vo.*;
import com.junl.msg.common.MsgType;
import com.junl.system.UserMsgService;
import com.junl.system.UserService;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
@Log4j2
public class SelBiddingServiceImpl extends ServiceImpl<SelBiddingDao, SelBiddingEntity>
        implements SelBiddingService
{


    @Autowired
    private SelBiddingProductService selBiddingProductService;

    @Autowired
    private SelBiddingSequenceService selBiddingSequenceService;

    @Autowired
    private SelBiddingCustomerService selBiddingCustomerService;

    @Autowired
    private SelBiddingFeeRecordService selBiddingFeeRecordService;

    @Autowired
    private SelCustomerService selCustomerService;


    @Autowired
    private SelBiddingCustomerOfferService selBiddingCustomerOfferService;

    @Autowired
    private SelBiddingSuccessService selBiddingSuccessService;
    @Autowired
    private SelBiddingSuccessProductService selBiddingSuccessProductService;

    @Autowired
    private FlowSelBiddingCustomerService flowSelBiddingCustomerService;

    @Autowired
    private FlowSelBiddingProductService flowSelBiddingProductService;

    @Autowired
    private SelApprovalDefinitionDao selApprovalDefinitionDao;

    @Reference(lazy = true)
    private UserService userService;

    @Autowired
    private UserMsgService userMsgService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SelApprovalFlowService selApprovalFlowService;

    @Autowired
    private SelApprovalUnderwayService selApprovalUnderwayService;


    @Autowired
    private FlowSelBiddingSuccessService flowSelBiddingSuccessService;

    @Autowired
    private FlowSelBiddingSuccessProductService flowSelBiddingSuccessProductService;

    @Autowired
    private FlowSelBiddingService flowSelBiddingService;

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    private SelInquiryDao selInquiryDao;

    @Autowired
    private SelInquiryCustomerDao selInquiryCustomerDao;

    @Autowired
    private SelPlanProductDetailDao selPlanProductDetailDao;


    @Autowired
    private SelPlanDao selPlanDao;

    @Autowired
    private SelBiddingDao selBiddingDao;


    /**
     * @description: 分页查询列表
     * @param selBiddingVo
     */
    @Override
    public PageEntity queryPage(SelBiddingVo selBiddingVo) {
        if (Assert.notNull(selBiddingVo.getUserId())) {
            SysUserEntity userInfo = userService.getUserInfo(selBiddingVo.getUserId());
            Assert.notNull(userInfo,"系统无法识别的账户信息");
            if(Assert.notNull(userInfo.getDataId())&&!Opposite.ZERO.equals(userInfo.getDataId())){
                List<String> users = userService.getUsers(userInfo.getDataId());
                selBiddingVo.setUserIds(users);
            }else{
                Integer identityCode = userInfo.getIdentityCode();

                if (identityCode.equals(IdentityStatus.DEPT_ADMIN.getCode())) {
                    List<String> deptUsers = userService.getDeptUsers(userInfo.getDeptId());
                    selBiddingVo.setUserIds(deptUsers);
                }else if(identityCode.equals(IdentityStatus.USER.getCode())){
                    ArrayList<String> objects = new ArrayList<>();
                    objects.add(userInfo.getUserId());
                    selBiddingVo.setUserIds(objects);
                }
            }
        }
        PageUtils.execute(selBiddingVo);
        List<SelBiddingEntity> list = baseMapper.queryList(selBiddingVo);
        list.forEach(x->{
            if (Assert.notNull(x.getCompanyId())) {
                x.setCompanyName(redisUtils.get(RedisKey.COMPANY.getName()+x.getCompanyId()));
            }
        });
        return PageUtils.getData(list);
    }

    @Override
    public PageEntity queryBiddingPage(SelBiddingVo selBiddingVo) {
        //查询轮次信息
        SelBiddingSequenceEntity id = selBiddingSequenceService.getOne(
                new QueryWrapper<SelBiddingSequenceEntity>()
                        .select("id")
                        .lambda()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, selBiddingVo.getId())
                                .eq(SelBiddingSequenceEntity::getSequence, selBiddingVo.getRound())));
        Assert.notNull(id,"查询不到轮次信息");

        //根据轮次Id 分页查询企业和报价信息
        PageUtils.execute(selBiddingVo);
        List<SelBiddingCustomerEntity> selBiddingCustomerEntities = baseMapper.queryBiddingPage(id.getId());
        return PageUtils.getData(selBiddingCustomerEntities);
    }

    @Override
    public SelBiddingEntity getSuccessInfo(SelBiddingVo selBiddingVo) {

        return baseMapper.getSuccessInfo(selBiddingVo);
    }

    @Override
    public SelBiddingEntity info(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        //查询出轮次信息
        Integer roundNum = selBiddingEntity.getRoundNum();
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, selBiddingEntity.getId())
                                .eq(SelBiddingSequenceEntity::getSequence, roundNum)));


        //根据轮次查询竞价的商品
        List<SelBiddingProductEntity> productList = selBiddingProductService.list(
                new LambdaQueryWrapper<SelBiddingProductEntity>()
                        .eq(SelBiddingProductEntity::getSequenceId, sequenceEntity.getId())
        );
        selBiddingEntity.setProductList(productList);


        //查询竞价的伦次和每轮企业报价信息
        List<SelBiddingSequenceEntity> sequenceList=baseMapper.querySequence(id);
        selBiddingEntity.setSequenceList(sequenceList);

        return selBiddingEntity;
    }


    @Override
    public SelBiddingEntity getAppinfo(String id) {
        SelBiddingEntity biddingEntity =baseMapper.getAppInfo(id);
        return  biddingEntity;
    }

    @Override
    @Transactional
    public boolean again(SelInquiryVo selInquiryVo) {
        //查询竞价主表信息
        String id = selInquiryVo.getId();
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);

        if (!selBiddingEntity.getIsDelete().equals(0)) {
            throw new RuntimeException("竞价已废弃,请刷新后重新操作");
        }

        if (selBiddingEntity.getBiddingStatus().equals(BiddingStatus.BIDDING.getCode())) {
            throw new RuntimeException("正在竞价中,不能再次竞价,等待该轮竞价结束");
        }

        if (selBiddingEntity.getBiddingStatus().equals(BiddingStatus.AUDIT.getCode())) {
            throw new RuntimeException("竞价正在审核中,请勿重复提交");
        }

        //竞价企业
        List<SelBiddingCustomerEntity> customerList = selInquiryVo.getCustomerList();
        if (!Assert.notNullCollect(customerList)) {
            throw new RuntimeException("请至少选择一家企业参与竞价");
        }

        Assert.notNull(selBiddingEntity,"查询不到该主键信息");


        Date startDate = selInquiryVo.getBiddingStartDate();
        Date endTime = selInquiryVo.getBiddingEndTime();
        Date now = DateUtils.getNow();
        if(startDate.compareTo(now)<0){
            throw new RuntimeException("竞价开始时间必须大于当前时间.");
        }

        if (DateUtils.compareSecond(startDate,endTime)<60) {
            throw new RuntimeException("开始和结束时间必须相差大于1分钟.");
        }
        SelBiddingSequenceEntity sequenceTemp = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                                .eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum()))
        );


        List<SelBiddingProductEntity> productList = selInquiryVo.getProductList();

        //校验商品基价是否下调
        for (SelBiddingProductEntity selBiddingProductEntity : productList) {
            String productId = selBiddingProductEntity.getProductId();
            BigDecimal baseFee = selBiddingProductEntity.getBaseFee();
            SelBiddingFeeRecordEntity fee = selBiddingFeeRecordService.getOne(
                    new LambdaQueryWrapper<SelBiddingFeeRecordEntity>()
                            .and(e -> e.eq(SelBiddingFeeRecordEntity::getSequenceId, sequenceTemp.getId())
                                    .eq(SelBiddingFeeRecordEntity::getProductId, productId)));
            if (Assert.notNull(fee)) {
                if (baseFee.compareTo(fee.getBaseFee())<0) {
                    throw new RuntimeException(selBiddingProductEntity.getProductName()+"的基价只能上涨不能下调");
                }
            }

        }


        //轮次信息加+1 竞价主表
        selBiddingEntity.setRoundNum(selBiddingEntity.getRoundNum()+1);
        selBiddingEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingEntity.setPlanStartDate(selInquiryVo.getBiddingStartDate());
        selBiddingEntity.setPlanEndTime(selInquiryVo.getBiddingEndTime());
        selBiddingEntity.setUpdateBy(selInquiryVo.getUserId());
        baseMapper.updateById(selBiddingEntity);

        //定义流程
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.ZJ_SH.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selInquiryVo.getUserId()
        );

        //竞价轮次表
        SelBiddingSequenceEntity selBiddingSequenceEntity=new SelBiddingSequenceEntity();
        selBiddingSequenceEntity.setId(SnowFlake.getUUId());
        selBiddingSequenceEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingSequenceEntity.setBiddingId(selBiddingEntity.getId());
        selBiddingSequenceEntity.setSequence(selBiddingEntity.getRoundNum());
        selBiddingSequenceEntity.setPlanStartDate(selInquiryVo.getBiddingStartDate());
        selBiddingSequenceEntity.setPlanEndTime(selInquiryVo.getBiddingEndTime());
        selBiddingSequenceService.save(selBiddingSequenceEntity);

        //审批信息保存
        List<FlowSelBiddingCustomerEntity> flowCustomer=new ArrayList<>();


        customerList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequence(selBiddingSequenceEntity.getId());

            //审批流程信息保存
            FlowSelBiddingCustomerEntity f=new FlowSelBiddingCustomerEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowCustomer.add(f);

        });
        selBiddingCustomerService.saveBatch(customerList);

        //基价调整记录
        List<SelBiddingFeeRecordEntity> feeRecordList=new ArrayList<>();

        List<FlowSelBiddingProductEntity> flowProduct=new ArrayList<>();

        productList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequenceId(selBiddingSequenceEntity.getId());
            //竞价基价信息记录
            SelBiddingFeeRecordEntity feeRecordEntity=new SelBiddingFeeRecordEntity();
            feeRecordEntity.setId(SnowFlake.getUUId());
            feeRecordEntity.setSequenceId(selBiddingSequenceEntity.getId());
            feeRecordEntity.setBaseFee(x.getBaseFee());
            feeRecordEntity.setProductId(x.getProductId());
            feeRecordEntity.setProductName(x.getProductName());
            feeRecordList.add(feeRecordEntity);


            FlowSelBiddingProductEntity f=new FlowSelBiddingProductEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowProduct.add(f);

        });

        selBiddingProductService.saveBatch(productList);
        selBiddingFeeRecordService.saveBatch(feeRecordList);


        FlowSelBiddingEntity flowSelBiddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,flowSelBiddingEntity);
        flowSelBiddingEntity.setId(SnowFlake.getUUId());
        flowSelBiddingEntity.setUnderwayId(flowBO.getUnderWayId());
        flowSelBiddingEntity.setRoundId(selBiddingSequenceEntity.getId());
        flowSelBiddingEntity.setBiddingId(selBiddingEntity.getId());


        //保存审批流程相关的信息
        flowSelBiddingService.save(flowSelBiddingEntity);
        flowSelBiddingCustomerService.saveBatch(flowCustomer);
        flowSelBiddingProductService.saveBatch(flowProduct);


        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("商品竞价审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        ).setCompanyId(selBiddingEntity.getCompanyId())
                        .setSpecific(ApproveStatus.ZJ_SH.getCode())
                        .setMsgContent(null)

        );

        //实时通知
        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );

        //通知微信公众号
        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selInquiryVo.getUserId(),
                            "商品竞价"
                    );
                }
        );

//        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//        sysUserMsgEntity.setMsgType(MsgType.EXAMINE.getCode());
//        sysUserMsgEntity.setBusCode(ApproveStatus.ZJ_SH.getCode());
//        sysUserMsgEntity.setMsgAccessory(selBiddingEntity.getId());
//        sysUserMsgEntity.setCompanyId(selBiddingEntity.getCompanyId());
//        sysUserMsgEntity.setCreateBy(selInquiryVo.getUserId());
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.EXAMINE.getKey());

        return true;
    }

    @Override
    public List<SelBiddingSequenceEntity> queryFee(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        List<SelBiddingSequenceEntity> selBiddingSequenceEntities = selBiddingFeeRecordService.queryRecords(id);
        return selBiddingSequenceEntities;
    }

    @Override
    @Transactional
    public boolean success(SelBiddingVo selBiddingVo) {
        String id = selBiddingVo.getId();
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);

        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        if (!selBiddingEntity.getIsDelete().equals(0)) {
            throw new RuntimeException("竞价已废弃,请刷新后操作");
        }

        if (selBiddingEntity.getBiddingStatus().equals(BiddingStatus.BIDDING_AUDIT_OVER.getCode())) {
            throw new RuntimeException("交易正在审批中,请勿重复提交");
        }


        //判断该流程节点是否定义
        if (selApprovalDefinitionDao.selectCount(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selBiddingEntity.getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.JY_WC.getCode())))<=0) {
            throw new RuntimeException("销售计划审核流程节点未定义,无法提交审核");
        }


        selBiddingEntity.setBiddingStatus(BiddingStatus.BIDDING_AUDIT_OVER.getCode());
        //获取关联表id 表示这几家竞选成功
        List<SelBiddingCustomerEntity> biddingCustomers = selBiddingVo.getCustomerList();

        //查询成功的轮次信息
        SelBiddingSequenceEntity biddingSequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                                .eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum()))
        );

        //定义审批流程
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.JY_WC.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selBiddingVo.getUserId()
        );

        //模拟竞价成功的企业
        List<SelBiddingSuccessEntity> successList=new ArrayList<>();

        //审批流程主信息
        List<FlowSelBiddingSuccessEntity> flowSelBiddingSuccessEntities=new ArrayList<>();
        //审批流程商品
        List<FlowSelBiddingSuccessProductEntity> flowSelBiddingSuccessProductEntities = new ArrayList<>();

        //成功的商品
        List<SelBiddingSuccessProductEntity> successProductList=new ArrayList<>();



        for (SelBiddingCustomerEntity biddingCustomer : biddingCustomers) {
            //封装主体数据
            SelBiddingSuccessEntity success=new SelBiddingSuccessEntity();
            success.setId(SnowFlake.getUUId());
            success.setBiddingId(selBiddingEntity.getId());
            success.setSequenceId(biddingSequenceEntity.getId());
            success.setOfferId(biddingCustomer.getId());
            success.setCreateBy(selBiddingVo.getUserId());
            successList.add(success);

            FlowSelBiddingSuccessEntity f=new FlowSelBiddingSuccessEntity();
            BeanUtils.copyProperties(success,f);
            f.setUnderwayId(flowBO.getUnderWayId());
            f.setBiddingId(selBiddingEntity.getId());
            f.setId(SnowFlake.getUUId());
            flowSelBiddingSuccessEntities.add(f);


            //查询竞价对应轮次的主表关联公司数据
            SelBiddingCustomerEntity customer = selBiddingCustomerService.getOne(
                    new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                            .and(e -> e.eq(SelBiddingCustomerEntity::getSequence, biddingSequenceEntity.getId())
                                    .eq(SelBiddingCustomerEntity::getCustomerId, biddingCustomer.getId()))
            );

            //查询出该企业的报价信息
            List<SelBiddingCustomerOfferEntity> offerEntities = selBiddingCustomerOfferService.list(
                    new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                            .eq(SelBiddingCustomerOfferEntity::getRelevanceId, customer.getId())
            );

            //封装成功的报价信息
            offerEntities.forEach(x->{
                SelBiddingSuccessProductEntity p=new SelBiddingSuccessProductEntity();
                p.setId(SnowFlake.getUUId());
                p.setSuccessId(success.getId());
                p.setCount(new BigDecimal(x.getCount()));
                p.setPrice(x.getPrice());
                p.setProductId(x.getProductId());
                p.setProductName(x.getProductName());
                successProductList.add(p);

                FlowSelBiddingSuccessProductEntity successProduct=new FlowSelBiddingSuccessProductEntity();
                BeanUtils.copyProperties(p,successProduct);
                successProduct.setId(SnowFlake.getUUId());
                successProduct.setSuccessId(f.getId());
                flowSelBiddingSuccessProductEntities.add(successProduct);

            });

            //更新主表数据
//            selBiddingSuccessService.save(success);
            //更新商品表数据
//            selBiddingSuccessProductService.saveBatch(productEntities);

        }

        selBiddingSuccessProductService.saveBatch(successProductList);
        selBiddingSuccessService.saveBatch(successList);
        flowSelBiddingSuccessService.saveBatch(flowSelBiddingSuccessEntities);
        flowSelBiddingSuccessProductService.saveBatch(flowSelBiddingSuccessProductEntities);


        //审核流程主信息
        FlowSelBiddingEntity biddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,biddingEntity);
        biddingEntity.setId(SnowFlake.getUUId());
        biddingEntity.setUnderwayId(flowBO.getUnderWayId());
        biddingEntity.setBiddingId(selBiddingEntity.getId());
        biddingEntity.setRoundId(biddingSequenceEntity.getId());
        flowSelBiddingService.save(biddingEntity);
//        flowSelBiddingSuccessService.saveBatch(flowSelBiddingSuccessEntities);
//        flowSelBiddingSuccessProductService.saveBatch(flowSelBiddingSuccessProductEntities);

        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("交易完成审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        )
                        .setCompanyId(selBiddingEntity.getCompanyId())
                        .setSpecific(ApproveStatus.JY_WC.getCode())
                        .setMsgContent(null)
        );

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );


        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selBiddingVo.getUserId(),
                            "交易完成"
                    );
                }
        );

//        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//        sysUserMsgEntity.setMsgType(MsgType.EXAMINE.getCode());
//        sysUserMsgEntity.setBusCode(BusCode.USER_CHECK.getCode());
//        sysUserMsgEntity.setSpecific(ApproveStatus.JY_WC.getCode());
//        sysUserMsgEntity.setMsgAccessory(id);
//        sysUserMsgEntity.setCompanyId(selBiddingEntity.getCompanyId());
//        sysUserMsgEntity.setCreateBy(selBiddingVo.getUserId());
//        userMsgService.saveHistory(
//                sysUserMsgEntity
//        );



//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.EXAMINE.getKey());

        baseMapper.updateById(selBiddingEntity);
        return true;
    }

    @Override
    @Transactional
    public boolean againSubmitSuccess(SelBiddingVo selBiddingVo) {
//        String id = selBiddingVo.getId();
//        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
//        Assert.notNull(selBiddingEntity,"查询不到该主键信息");
//
//        if (selBiddingEntity.getBiddingStatus().equals(BiddingStatus.BIDDING_AUDIT_OVER.getCode())) {
//            throw new RuntimeException("交易正在审核中,请勿重复提交.");
//        }
//
//        //判断该流程节点是否定义
//        if (selApprovalDefinitionDao.selectCount(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
//                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selBiddingEntity.getCompanyId())
//                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.JY_WC.getCode())))<=0) {
//            throw new RuntimeException("销售计划审核流程节点未定义,无法再次提交审核");
//        }

        return success(selBiddingVo);
    }

    @Override
    public void updateStatus(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);

        Integer roundNum = selBiddingEntity.getRoundNum();
        //找出对应的场次信息
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, selBiddingEntity.getId())
                                .eq(SelBiddingSequenceEntity::getSequence, roundNum)));

        //开始时间处理
        Date start = selBiddingEntity.getPlanStartDate();
        Date endTime = selBiddingEntity.getPlanEndTime();

        //当前日期
        Date now = new Date();
        if(now.compareTo(start)>=0){
            selBiddingEntity.setBiddingStatus(BiddingStatus.BIDDING.getCode());
            sequenceEntity.setBiddingStatus(BiddingStatus.BIDDING.getCode());
            SelBiddingEntity temp=new SelBiddingEntity();
            temp.setId(selBiddingEntity.getId());
            temp.setBiddingStatus(selBiddingEntity.getBiddingStatus());
            baseMapper.updateById(temp);
            // 更改场次竞价状态
            selBiddingSequenceService.updateById(sequenceEntity);
        }
        if(now.compareTo(endTime)>=0){

            selBiddingEntity.setBiddingStatus(BiddingStatus.BIDDING_OVER.getCode());
            sequenceEntity.setBiddingStatus(BiddingStatus.BIDDING_OVER.getCode());
            SelBiddingEntity temp=new SelBiddingEntity();
            temp.setId(selBiddingEntity.getId());
            temp.setBiddingStatus(selBiddingEntity.getBiddingStatus());

            baseMapper.updateById(temp);

            // 更改场次竞价状态
            selBiddingSequenceService.updateById(sequenceEntity);
        }
    }

    @Override
    @Transactional
    public boolean againSubmit(SelBiddingEntity selBiddingVo) {
        //查询该条竞价记录
        String biddingId = selBiddingVo.getId();
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(biddingId);

        if (!selBiddingEntity.getIsDelete().equals(0)) {
            throw new RuntimeException("竞价已废弃,请刷新后再操作");
        }

        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        if (selBiddingEntity.getBiddingStatus().equals(BiddingStatus.AUDIT.getCode())) {
            throw new RuntimeException("竞价正在审核中,请勿重复提交.");
        }
        selBiddingVo.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        baseMapper.updateById(selBiddingVo);

        //判断该流程节点是否定义
        if (selApprovalDefinitionDao.selectCount(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selBiddingEntity.getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.ZJ_SH.getCode())))<=0) {
            throw new RuntimeException("竞价审核流程节点未定义,无法再次提交审核");
        }

        //查询出轮次信息
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum())
                                .eq(SelBiddingSequenceEntity::getBiddingId, selBiddingEntity.getId()))
        );


        //删除掉添加的公司和商品和基价
        selBiddingCustomerService.remove(
                new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                        .eq(SelBiddingCustomerEntity::getSequence,sequenceEntity.getId())
        );

        selBiddingProductService.remove(
                new LambdaQueryWrapper<SelBiddingProductEntity>()
                        .eq(SelBiddingProductEntity::getSequenceId,sequenceEntity.getId())
        );

        selBiddingFeeRecordService.remove(
                new LambdaQueryWrapper<SelBiddingFeeRecordEntity>()
                        .eq(SelBiddingFeeRecordEntity::getSequenceId,sequenceEntity.getId())
        );
        //删除轮次信息
        selBiddingSequenceService.removeById(sequenceEntity.getId());



        //定义流程
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.ZJ_SH.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selBiddingVo.getUpdateBy()
        );


        SelBiddingSequenceEntity selBiddingSequenceEntity=new SelBiddingSequenceEntity();
        selBiddingSequenceEntity.setId(SnowFlake.getUUId());
        selBiddingSequenceEntity.setBiddingId(selBiddingEntity.getId());
        selBiddingSequenceEntity.setSequence(selBiddingEntity.getRoundNum());
        selBiddingSequenceEntity.setPlanStartDate(selBiddingVo.getPlanStartDate());
        selBiddingSequenceEntity.setPlanEndTime(selBiddingVo.getPlanEndTime());
        selBiddingSequenceEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingSequenceService.save(selBiddingSequenceEntity);

        //审批信息保存
        List<FlowSelBiddingCustomerEntity> flowCustomer=new ArrayList<>();

        //新的竞价企业处理
        List<SelBiddingCustomerEntity> biddingCustomers = selBiddingVo.getCustomerList();
        biddingCustomers.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequence(selBiddingSequenceEntity.getId());

            //审批流程信息保存
            FlowSelBiddingCustomerEntity f=new FlowSelBiddingCustomerEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowCustomer.add(f);

        });
        selBiddingCustomerService.saveBatch(biddingCustomers);

        //新的竞价商品处理
        List<SelBiddingProductEntity> productList = selBiddingVo.getProductList();

        //基价调整记录
        List<SelBiddingFeeRecordEntity> feeRecordList=new ArrayList<>();

        List<FlowSelBiddingProductEntity> flowProduct=new ArrayList<>();

        //竞价商品
        productList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequenceId(selBiddingSequenceEntity.getId());
            //竞价基价信息记录
            SelBiddingFeeRecordEntity feeRecordEntity=new SelBiddingFeeRecordEntity();
            feeRecordEntity.setId(SnowFlake.getUUId());
            feeRecordEntity.setSequenceId(selBiddingSequenceEntity.getId());
            feeRecordEntity.setBaseFee(x.getBaseFee());
            feeRecordEntity.setProductId(x.getProductId());
            feeRecordEntity.setProductName(x.getProductName());
            feeRecordList.add(feeRecordEntity);

            FlowSelBiddingProductEntity f=new FlowSelBiddingProductEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowProduct.add(f);

        });
        selBiddingProductService.saveBatch(productList);

        //竞价初始基价
        selBiddingFeeRecordService.saveBatch(feeRecordList);

        FlowSelBiddingEntity flowSelBiddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,flowSelBiddingEntity);
        flowSelBiddingEntity.setId(SnowFlake.getUUId());
        flowSelBiddingEntity.setUnderwayId(flowBO.getUnderWayId());
        flowSelBiddingEntity.setRoundId(selBiddingSequenceEntity.getId());
        flowSelBiddingEntity.setBiddingId(selBiddingEntity.getId());


        //保存审批流程相关的信息
        flowSelBiddingService.save(flowSelBiddingEntity);
        flowSelBiddingCustomerService.saveBatch(flowCustomer);
        flowSelBiddingProductService.saveBatch(flowProduct);


        //改变状态
        SelBiddingEntity temp=new SelBiddingEntity();
        temp.setId(selBiddingEntity.getId());
        temp.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        baseMapper.updateById(temp);


        //通知第一位审批人
        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("商品竞价审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        ).setCompanyId(selBiddingEntity.getCompanyId())
                        .setSpecific(ApproveStatus.ZJ_SH.getCode())
                        .setMsgContent(null)

        );

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );


        //通知微信公众号
        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selBiddingVo.getUpdateBy(),
                            "商品竞价"
                    );
                }
        );

//        //发送消息通知
//        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//        sysUserMsgEntity.setMsgType(MsgType.EXAMINE.getCode());
//        sysUserMsgEntity.setBusCode(ApproveStatus.ZJ_SH.getCode());
//        sysUserMsgEntity.setMsgAccessory(selBiddingEntity.getId());
//        sysUserMsgEntity.setCompanyId(selBiddingEntity.getCompanyId());
//        sysUserMsgEntity.setCreateBy(selBiddingVo.getUpdateBy());
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.EXAMINE.getKey());
        return true;
    }

    @Override
    public FlowSelBiddingEntity getFlowBiddingInfo(String id) {
        //根据流程id 查询主流程id
        SelApprovalFlowEntity byId = selApprovalFlowService.getById(id);
        Assert.notNull(byId,"流程节点错误!");
        SelApprovalUnderwayEntity underwayEntity = selApprovalUnderwayService.getById(byId.getUnderwayId());
        FlowSelBiddingEntity flowSuccessInfo = baseMapper.getFlowBiddingInfo(underwayEntity.getId());
        return flowSuccessInfo;
    }

    @Override
    public FlowSelBiddingEntity getFlowSuccessInfo(String id) {


        SelApprovalFlowEntity byId = selApprovalFlowService.getById(id);
        Assert.notNull(byId,"流程节点错误!");

        List<SuccessExcel> temp=new ArrayList<>();

        //查询审批主流程信息
        SelApprovalUnderwayEntity underwayEntity = selApprovalUnderwayService.getById(byId.getUnderwayId());
        //查询竞价的主信息
        FlowSelBiddingEntity flowSelBiddingEntity = flowSelBiddingService.getOne(
                new LambdaQueryWrapper<FlowSelBiddingEntity>()
                        .eq(FlowSelBiddingEntity::getUnderwayId, underwayEntity.getId())
        );
        //查询交易成功的主信息
        List<FlowSelBiddingSuccessEntity> list = flowSelBiddingSuccessService.list(
                new LambdaQueryWrapper<FlowSelBiddingSuccessEntity>()
                        .and(e->{
//            e.eq(FlowSelBiddingSuccessEntity::getUnderwayId, underwayEntity.getId())
                            e.eq(FlowSelBiddingSuccessEntity::getUnderwayId,underwayEntity.getId());
                        }));
        //查询交易成功的报价信息
        list.forEach(x->{

            SelCustomerEntity customer = selCustomerService.getOne(
                    new QueryWrapper<SelCustomerEntity>()
                            .select("customer_name")
                            .lambda()
                            .eq(SelCustomerEntity::getId, x.getOfferId())
            );

            List<FlowSelBiddingSuccessProductEntity> productEntities = flowSelBiddingSuccessProductService.list(
                    new LambdaQueryWrapper<FlowSelBiddingSuccessProductEntity>()
                            .eq(FlowSelBiddingSuccessProductEntity::getSuccessId, x.getId())
            );

            for (FlowSelBiddingSuccessProductEntity productEntity : productEntities) {
                SuccessExcel successExcel=new SuccessExcel();
                successExcel.setCustomerName(customer.getCustomerName());
                successExcel.setProductName(productEntity.getProductName());

                if (Assert.notNull(productEntity.getPrice())) {
                    successExcel.setPrice(productEntity.getPrice().doubleValue());
                }

                if(Assert.notNull(productEntity.getCount())){
                    successExcel.setCount(productEntity.getCount().doubleValue());
                }

                if(Assert.notNull(productEntity.getAddPrice())){
                    successExcel.setAddPrice(productEntity.getAddPrice().doubleValue());
                }

                if(Assert.notNull(productEntity.getAddCount())){
                    successExcel.setAddCount(productEntity.getAddCount().doubleValue());
                }

                temp.add(successExcel);

            }
        });

        //返回结果集
        List<SuccessExcel> result=new ArrayList<>();
        //以产品维度分组
        Map<String, List<SuccessExcel>> collect = temp.stream().collect(Collectors.groupingBy(SuccessExcel::getProductName));

        for (Map.Entry<String, List<SuccessExcel>> x : collect.entrySet()) {
            List<SuccessExcel> value = x.getValue();
            if (Assert.notNull(value)) {
                value.sort((z,y)->-z.getCount().compareTo(y.getCount()));
                value.get(0).setSize(value.size());
                result.addAll(value);

                SuccessExcel successExcel=new SuccessExcel();

//                if (Assert.notNull(value.get(0).getPrice())) {
//                    successExcel.setPrice(value.stream().mapToDouble(SuccessExcel::getPrice).sum());
//                }

                if(Assert.notNull(value.get(0).getCount())){
                    successExcel.setCount(value.stream().mapToDouble(SuccessExcel::getCount).sum());
                }

                if(Assert.notNull(value.get(0).getAddPrice())){
                    successExcel.setAddPrice(value.stream().mapToDouble(SuccessExcel::getAddPrice).sum());
                }

                if(Assert.notNull(value.get(0).getAddCount())){
                    successExcel.setAddCount(value.stream().mapToDouble(SuccessExcel::getAddCount).sum());
                }

                successExcel.setProductName("小计:");
                successExcel.setSize(1);
                result.add(successExcel);
            }

        }

        flowSelBiddingEntity.setSuccessData(result);

        return flowSelBiddingEntity;
    }

    @Override
    public Map<String,String> exportExcel(SelBiddingVo selBiddingVo) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(selBiddingVo.getId());
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        List<BiddingExcel> list = queryBiddingInfo(selBiddingVo);

        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        excelUtils.write(
                list,
                BiddingExcel.class,byteArrayOutputStream,
                BiddingExcel.getHead(
                        selBiddingEntity.getPlanName(),
                        DateUtils.defaultFormat(selBiddingEntity.getPlanStartDate()),
                        DateUtils.defaultFormat(selBiddingEntity.getPlanEndTime()),
                        selBiddingVo.getRound()),
                3,
                new int[]{0}
        );
        String excelBase64 = CommonUtil.getExcelBase64(byteArrayOutputStream);

        Map<String,String> map=new HashMap<>();
        map.put("file",excelBase64);
        map.put("name",selBiddingEntity.getPlanName()+"竞价报表");
        return map;
    }

    @Override
    public Map<String,String> exportSuccessExcel(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        SelBiddingEntity temp = querySuccessInfo(id);

        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        excelUtils.write(temp.getSuccessData(), SuccessExcel.class,byteArrayOutputStream,
                SuccessExcel.getHead(selBiddingEntity.getPlanName(),selBiddingEntity.getRoundNum()),
                2,new int[]{0});
        String excelBase64 = CommonUtil.getExcelBase64(byteArrayOutputStream);
        Map<String,String> map=new HashMap<>();
        map.put("name",selBiddingEntity.getPlanName()+"交易成功报表");
        map.put("file",excelBase64);
        return map;
    }

    @Override
    public List<SelInquiryOfferEntity> queryInquiry(SelBiddingVo selBiddingVo) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(selBiddingVo.getId());
        Assert.notNull(selBiddingEntity,"查询不到改主键信息");

        String planId = selBiddingEntity.getPlanId();
        SelInquiryEntity selInquiryEntity = selInquiryDao.selectOne(
                new LambdaQueryWrapper<SelInquiryEntity>()
                        .eq(SelInquiryEntity::getPlanId, planId)
        );

        SelInquiryCustomerEntity selInquiryCustomerEntity = selInquiryCustomerDao.selectOne(
                new LambdaQueryWrapper<SelInquiryCustomerEntity>()
                .and(e->e.eq(SelInquiryCustomerEntity::getInquiryId, selInquiryEntity.getId())
                        .eq(SelInquiryCustomerEntity::getCustomerId,selBiddingVo.getCustomerId())));

        if(Assert.notNull(selInquiryCustomerEntity)){
            selBiddingVo.setRelevancyId(selInquiryCustomerEntity.getId());

            List<SelInquiryOfferEntity> selInquiryOfferEntities = baseMapper.queryInquiry(selBiddingVo);
            return selInquiryOfferEntities;
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    public SelBiddingEntity querySuccessInfo(String id) {

        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");


        List<SuccessExcel> list=new ArrayList<>();
        //查询所有成功的主表信息
        List<SelBiddingSuccessEntity> success = selBiddingSuccessService.list(
                new LambdaQueryWrapper<SelBiddingSuccessEntity>()
                        .eq(SelBiddingSuccessEntity::getBiddingId, id)
        );
        success.forEach(x->{
            SelCustomerEntity customer = selCustomerService.getOne(
                    new QueryWrapper<SelCustomerEntity>()
                            .select("customer_name")
                            .lambda()
                            .eq(SelCustomerEntity::getId, x.getOfferId())
            );

            //查询公司报价信息
            List<SelBiddingSuccessProductEntity> productEntities = selBiddingSuccessProductService.list(
                    new LambdaQueryWrapper<SelBiddingSuccessProductEntity>()
                            .eq(SelBiddingSuccessProductEntity::getSuccessId, x.getId())
            );

            for (SelBiddingSuccessProductEntity productEntity : productEntities) {
                SuccessExcel successExcel=new SuccessExcel();
                successExcel.setCustomerName(customer.getCustomerName());
                successExcel.setProductName(productEntity.getProductName());

                if (Assert.notNull(productEntity.getPrice())) {
                    successExcel.setPrice(productEntity.getPrice().doubleValue());
                }

                if(Assert.notNull(productEntity.getCount())){
                    successExcel.setCount(productEntity.getCount().doubleValue());
                }

                if(Assert.notNull(productEntity.getAddPrice())){
                    successExcel.setAddPrice(productEntity.getAddPrice().doubleValue());
                }

                if(Assert.notNull(productEntity.getAddCount())){
                    successExcel.setAddCount(productEntity.getAddCount().doubleValue());
                }

                list.add(successExcel);

            }
        });
        //返回结果集
        List<SuccessExcel> result=new ArrayList<>();
        //以产品维度分组
        Map<String, List<SuccessExcel>> collect = list.stream().collect(Collectors.groupingBy(SuccessExcel::getProductName));

        for (Map.Entry<String, List<SuccessExcel>> x : collect.entrySet()) {
            List<SuccessExcel> value = x.getValue();
            if (Assert.notNull(value)) {
                value.sort((z,y)->-z.getCount().compareTo(y.getCount()));
                value.get(0).setSize(value.size());
                result.addAll(value);

                SuccessExcel successExcel=new SuccessExcel();


                if(Assert.notNull(value.get(0).getCount())){
                    successExcel.setCount(value.stream().mapToDouble(SuccessExcel::getCount).sum());
                }

                if(Assert.notNull(value.get(0).getAddPrice())){
                    successExcel.setAddPrice(value.stream().mapToDouble(SuccessExcel::getAddPrice).sum());
                }

                if(Assert.notNull(value.get(0).getAddCount())){
                    successExcel.setAddCount(value.stream().mapToDouble(SuccessExcel::getAddCount).sum());
                }

                successExcel.setProductName("小计:");
                successExcel.setSize(1);
                result.add(successExcel);
            }

        }

        selBiddingEntity.setSuccessData(result);

        return selBiddingEntity;
    }

    @Override
    public List<BiddingExcel> queryBiddingInfo(SelBiddingVo selBiddingVo) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(selBiddingVo.getId());
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        //查询出轮次信息
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                .and(e -> e.eq(SelBiddingSequenceEntity::getSequence, selBiddingVo.getRound())
                        .eq(SelBiddingSequenceEntity::getBiddingId, selBiddingEntity.getId()))
        );


        List<BiddingExcel> list=new ArrayList<>();

        List<SelBiddingProductEntity> product = selBiddingProductService.list(
                new LambdaQueryWrapper<SelBiddingProductEntity>()
                        .eq(SelBiddingProductEntity::getSequenceId, sequenceEntity.getId())
        );
        final CountDownLatch countDownLatch=new CountDownLatch(product.size());
        List<Callable<List<BiddingExcel>>> task=new ArrayList<>();
        for (SelBiddingProductEntity selBiddingProductEntity : product) {
            BiddingTaskQuery biddingTaskQuery = new BiddingTaskQuery(
                    countDownLatch,
                    sequenceEntity,
                    selBiddingProductEntity,
                    baseMapper,
                    selBiddingCustomerOfferService
            );
            task.add(biddingTaskQuery);
        }
        List<List<BiddingExcel>> lists = ThreadPoolUtil.runTask(task, countDownLatch);
        lists.forEach(x->{
            list.addAll(x);
        });

        return list;
    }

    @Override
    public PageEntity getPlanReport(ReportPage reportPage) {


        PageUtils.execute(reportPage);
        List<SelBiddingEntity> selBiddingEntities = baseMapper.selectList(
                new LambdaQueryWrapper<SelBiddingEntity>()
                .and(e->e.eq(SelBiddingEntity::getPeriodNum, reportPage.getTime())
                        .eq(SelBiddingEntity::getModeType,reportPage.getModeType()))
        );
        if (!Assert.notNullCollect(selBiddingEntities)) {
            return PageUtils.getData(selBiddingEntities);
        }

        //不是空只会有一条
        SelBiddingEntity selBiddingEntity = selBiddingEntities.get(0);
        selBiddingEntity.setReportPlans(getReportExcel(selBiddingEntity));

        return PageUtils.getData(selBiddingEntities);
    }

    @Override
    public Map<String, String> exportReport(ReportPage reportPage) {

        Map<String, String> map=new HashMap<>();

        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();

        //指定页数
        if(Assert.notNull(reportPage.getNumber())){
            reportPage.setPage(reportPage.getNumber());
            PageUtils.execute(reportPage);
            List<SelBiddingEntity> selBiddingEntities =baseMapper.getSelectList(reportPage);
            SelBiddingEntity selBiddingEntity = selBiddingEntities.get(0);
            List<ReportPlan> reportExcel = getReportExcel(selBiddingEntity);

            String year = DateUtils.format(selBiddingEntity.getCreateTime(), "yyyy");
            String head=year+"年第"+selBiddingEntity.getPeriodNum()+"期"+selBiddingEntity.getPlanName()+"产品销售计划表";
            excelUtils.write(
                    reportExcel,
                    ReportPlan.class,
                    byteArrayOutputStream,
                    ReportPlan.getHead(head),
                    2,
                    new int[]{0,1,6}
            );
            String excelBase64 = CommonUtil.getExcelBase64(byteArrayOutputStream);
            map.put("name",head);
            map.put("file",excelBase64);
            return map;

        }else{

            List<ReportPlan> result=new ArrayList<>();
            //未指定页数 全部导出
            List<SelBiddingEntity> selBiddingEntities = baseMapper.getSelectList(reportPage);


            for (SelBiddingEntity selBiddingEntity : selBiddingEntities) {
                List<ReportPlan> reportExcel = getReportExcel(selBiddingEntity);
                /**
                 * 修改为整期导出到一个sheet页中 2021.09.23
                 */
                if (Assert.notNullCollect(reportExcel)) {
                    result.addAll(reportExcel);
                }


            }

            SelBiddingEntity selBiddingEntity = selBiddingEntities.get(0);

            if(Assert.notNullCollect(result)){
                //过滤掉合计并且按产品名称收集
                Map<String, List<ReportPlan>> productGroup = result.stream().filter(x -> !x.getCustomerName().equals("合计")).collect(Collectors.groupingBy(ReportPlan::getProductName));
                //清空结果集重新组装数据
                result.clear();
                for (Map.Entry<String, List<ReportPlan>> stringListEntry : productGroup.entrySet()) {
                    List<ReportPlan> value = stringListEntry.getValue();


                    value.sort((z,y)->-z.getCount().compareTo(y.getCount()));
                    value.get(0).setSize(value.size()+1);
                    result.addAll(value);

                    ReportPlan reportPlan=new ReportPlan();
                    reportPlan.setProductName(value.get(0).getProductName());
                    reportPlan.setPrice(value.get(0).getPrice());
                    reportPlan.setSellCount(value.get(0).getSellCount());
                    reportPlan.setCustomerName("合计");
                    reportPlan.setFlag(Boolean.TRUE);
                    reportPlan.setCount(value.stream().mapToDouble(x->x.getCount()).sum());
                    result.add(reportPlan);

                }
            }

            String year = DateUtils.format(selBiddingEntity.getCreateTime(), "yyyy");
            String head=year+"年第"+selBiddingEntity.getPeriodNum()+"期"+selBiddingEntity.getPlanName()+"产品销售计划表";
            excelUtils.write(
                    result,
                    ReportPlan.class,
                    byteArrayOutputStream,
                    ReportPlan.getHead(head),
                    2,
                    new int[]{0,1,6}
            );
            String excelBase64 = CommonUtil.getExcelBase64(byteArrayOutputStream);
            map.put("name",head);
            map.put("file",excelBase64);

            return map;
        }
    }

    @Override
    public List<Integer> getListTime(ReportPage reportPage) {

        return baseMapper.getListTime(reportPage);
    }

    @Override
    @Transactional
    public boolean biddingRevocation(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        SelApprovalUnderwayEntity selApprovalUnderwayEntity = selApprovalUnderwayService.queryFlowAll(id);
        if (selApprovalUnderwayEntity.getIsStart().equals(Integer.valueOf(Opposite.SINGLE))) {
            throw new RuntimeException("该审批记录已经开始审批,无法撤回.");
        }

        //更新审批流程状态
        SelApprovalUnderwayEntity temp=new SelApprovalUnderwayEntity();
        temp.setId(selApprovalUnderwayEntity.getId());
        temp.setIsDelete(Integer.valueOf(Opposite.SINGLE));
        temp.setIsRepeal(Integer.valueOf(Opposite.SINGLE));
        selApprovalUnderwayService.updateById(temp);


        SelBiddingEntity tempBidding=new SelBiddingEntity();
        tempBidding.setBiddingStatus(BiddingStatus.REVOCATION.getCode());
        tempBidding.setId(id);
        //轮次大于1 是再次竟量的审批被撤回 所以需要把轮次退回到上一轮次
        if(selBiddingEntity.getRoundNum()>Integer.valueOf(Opposite.SINGLE)){
            tempBidding.setRoundNum(selBiddingEntity.getRoundNum()-1);
        }
        baseMapper.updateById(tempBidding);

        SelApprovalFlowEntity flowEntity = selApprovalFlowService.getOne(
                new LambdaQueryWrapper<SelApprovalFlowEntity>()
                        .eq(SelApprovalFlowEntity::getUnderwayId, selApprovalUnderwayEntity.getId())
                        .last("limit 1")
        );

        //更新审批消息为已处理
        userMsgService.updateStatus(
                BusCode.USER_CHECK.getCode(),
                String.format(
                        "%s,%s",
                        id,
                        flowEntity.getId()
                )
        );

        return true;
    }

    @Override
    @Transactional
    public boolean successRevocation(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");


        SelApprovalUnderwayEntity selApprovalUnderwayEntity = selApprovalUnderwayService.queryFlowAll(id);
        if (selApprovalUnderwayEntity.getIsStart().equals(Integer.valueOf(Opposite.SINGLE))) {
            throw new RuntimeException("该审批记录已经开始审批,无法撤回.");
        }


        //更新审批流程状态
        SelApprovalUnderwayEntity temp=new SelApprovalUnderwayEntity();
        temp.setId(selApprovalUnderwayEntity.getId());
        temp.setIsDelete(Integer.valueOf(Opposite.SINGLE));
        temp.setIsRepeal(Integer.valueOf(Opposite.SINGLE));
        selApprovalUnderwayService.updateById(temp);



        SelBiddingEntity tempBidding=new SelBiddingEntity();
        tempBidding.setBiddingStatus(BiddingStatus.SUCCESS_REVOCATION.getCode());
        tempBidding.setId(id);
        baseMapper.updateById(tempBidding);


        //查询报价成功的企业主表
        List<SelBiddingSuccessEntity> success_id = selBiddingSuccessService.list(
                new QueryWrapper<SelBiddingSuccessEntity>()
                        .select("id")
                .lambda().eq(SelBiddingSuccessEntity::getBiddingId, id)
        );

        //删除成功的报价企业信息
        selBiddingSuccessService.remove(
                new LambdaQueryWrapper<SelBiddingSuccessEntity>()
                        .eq(SelBiddingSuccessEntity::getBiddingId,id)
        );

        //删除成功的商品
        selBiddingSuccessProductService.remove(
                new LambdaQueryWrapper<SelBiddingSuccessProductEntity>()
                .in(
                        SelBiddingSuccessProductEntity::getSuccessId,
                        CommonUtil.collect(success_id,SelBiddingSuccessEntity::getId)
                )
        );

        SelApprovalFlowEntity flowEntity = selApprovalFlowService.getOne(
                new LambdaQueryWrapper<SelApprovalFlowEntity>()
                        .eq(SelApprovalFlowEntity::getUnderwayId, selApprovalUnderwayEntity.getId())
                        .last("limit 1")
        );

        //更新审批消息为已处理
        userMsgService.updateStatus(
                BusCode.USER_CHECK.getCode(),
                String.format(
                        "%s,%s",
                        id,
                        flowEntity.getId()
                )
        );

        return true;
    }

    @Override
    @Transactional
    public boolean successPricing(SelPlanVo selPlanVo) {
        //查询主表信息
        String id = selPlanVo.getId();
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);

        if (!selBiddingEntity.getIsDelete().equals(0)) {
            throw new RuntimeException("竞价已废弃,请刷新后重新操作");
        }

        if (selBiddingEntity.getBiddingStatus().equals(BiddingStatus.BIDDING_AUDIT_OVER.getCode())) {
            throw new RuntimeException("正在审核中,请勿重复提交");
        }


        //查询轮次信息
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                        .eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum()))
        );

        //查询参与的企业
        List<SelBiddingCustomerEntity> customer = selBiddingCustomerService.list(
                new QueryWrapper<SelBiddingCustomerEntity>()
                .select("id").lambda()
                        .eq(SelBiddingCustomerEntity::getSequence, sequenceEntity.getId())
        );

        //删除基价信息
        selBiddingFeeRecordService.remove(
                new LambdaQueryWrapper<SelBiddingFeeRecordEntity>()
                .eq(SelBiddingFeeRecordEntity::getSequenceId,sequenceEntity.getId())
        );


        //删除参与企业的报价信息
        selBiddingCustomerOfferService.remove(
                new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                .in(
                        SelBiddingCustomerOfferEntity::getRelevanceId,
                        CommonUtil.collect(customer,SelBiddingCustomerEntity::getId))
        );


        //删除参与企业的主表信息
        selBiddingCustomerService.removeByIds(CommonUtil.collect(customer,SelBiddingCustomerEntity::getId));

        //删除竞价的商品
        selBiddingProductService.remove(
                new LambdaQueryWrapper<SelBiddingProductEntity>()
                .eq(SelBiddingProductEntity::getSequenceId,sequenceEntity.getId())
        );

        //删除轮次信息
        selBiddingSequenceService.removeById(sequenceEntity.getId());

        /**
         * 把新的信息添加  类似与修改操作
         */


        //竞价轮次表
        SelBiddingSequenceEntity selBiddingSequenceEntity=new SelBiddingSequenceEntity();
        selBiddingSequenceEntity.setId(SnowFlake.getUUId());
        selBiddingSequenceEntity.setBiddingId(selBiddingEntity.getId());
        selBiddingSequenceEntity.setSequence(selBiddingEntity.getRoundNum());
        selBiddingSequenceEntity.setBiddingStatus(BiddingStatus.BIDDING_AUDIT_OVER.getCode());
        selBiddingSequenceService.save(selBiddingSequenceEntity);

        //定义流程
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.JY_WC.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selPlanVo.getUserId()
        );


        //模拟竞价成功的企业
        List<SelBiddingSuccessEntity> successList=new ArrayList<>();


        //竞价企业
        List<SelBiddingCustomerEntity> customerList = selPlanVo.getCustomerList();
        //审批流程主信息
        List<FlowSelBiddingSuccessEntity> flowSelBiddingSuccessEntities=new ArrayList<>();
        //审批流程商品
        List<FlowSelBiddingSuccessProductEntity> flowSelBiddingSuccessProductEntities = new ArrayList<>();

        //成功的企业
        List<SelBiddingCustomerOfferEntity> offerCollect=new ArrayList<>();
        //成功的商品
        List<SelBiddingSuccessProductEntity> successProductList=new ArrayList<>();


        customerList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequence(selBiddingSequenceEntity.getId());

            //组装交易完成的数据 模拟手动选择竞价成功的企业
            SelBiddingSuccessEntity successEntity=new SelBiddingSuccessEntity();
            successEntity.setId(SnowFlake.getUUId());
            successEntity.setSequenceId(x.getSequence());
            successEntity.setBiddingId(selBiddingEntity.getId());
            successEntity.setOfferId(x.getCustomerId());
            successList.add(successEntity);

            FlowSelBiddingSuccessEntity f=new FlowSelBiddingSuccessEntity();
            BeanUtils.copyProperties(successEntity,f);
            f.setUnderwayId(flowBO.getUnderWayId());
            f.setBiddingId(selBiddingEntity.getId());
            f.setId(SnowFlake.getUUId());
            flowSelBiddingSuccessEntities.add(f);


            //模拟商家报价的数据 其实是后台手动填写的 目的是为了跟其他模式保持数据一致
            List<SelBiddingCustomerOfferEntity> offerList = x.getOfferList();
            offerList.forEach(e->{
                e.setId(SnowFlake.getUUId());
                e.setRelevanceId(x.getId());
                e.setOfferer(x.getCustomerId());

                //交易成功的报价商品详情
                SelBiddingSuccessProductEntity tempProduct=new SelBiddingSuccessProductEntity();
                tempProduct.setId(SnowFlake.getUUId());
                tempProduct.setSuccessId(successEntity.getId());
                tempProduct.setProductId(e.getProductId());
                tempProduct.setProductName(e.getProductName());
                tempProduct.setPrice(e.getPrice());
                tempProduct.setCount(new BigDecimal(e.getCount()));
                tempProduct.setOfferId(x.getCustomerId());
                successProductList.add(tempProduct);

                FlowSelBiddingSuccessProductEntity successProduct=new FlowSelBiddingSuccessProductEntity();
                BeanUtils.copyProperties(tempProduct,successProduct);
                successProduct.setId(SnowFlake.getUUId());
                successProduct.setSuccessId(f.getId());
                flowSelBiddingSuccessProductEntities.add(successProduct);

            });

            offerCollect.addAll(offerCollect);
//            selBiddingCustomerOfferService.saveBatch(offerList);
//
//            selBiddingSuccessProductService.saveBatch(successProductList);


        });
        selBiddingCustomerOfferService.saveBatch(offerCollect);
        selBiddingSuccessProductService.saveBatch(successProductList);
        selBiddingCustomerService.saveBatch(customerList);
        selBiddingSuccessService.saveBatch(successList);

        //基价调整记录
        List<SelBiddingFeeRecordEntity> feeRecordList=new ArrayList<>();

        //竞价商品
        List<SelBiddingProductEntity> productList = selPlanVo.getProductList();
        productList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequenceId(selBiddingSequenceEntity.getId());

            //竞价基价信息记录
            SelBiddingFeeRecordEntity feeRecordEntity=new SelBiddingFeeRecordEntity();
            feeRecordEntity.setId(SnowFlake.getUUId());
            feeRecordEntity.setSequenceId(selBiddingSequenceEntity.getId());
            feeRecordEntity.setBaseFee(x.getBaseFee());
            feeRecordEntity.setProductId(x.getProductId());
            feeRecordEntity.setProductName(x.getProductName());
            feeRecordList.add(feeRecordEntity);


        });
        selBiddingProductService.saveBatch(productList);

        //竞价初始基价
        selBiddingFeeRecordService.saveBatch(feeRecordList);


        SelBiddingEntity temp=new SelBiddingEntity();
        temp.setId(id);
        temp.setBiddingStatus(BiddingStatus.BIDDING_AUDIT_OVER.getCode());
        baseMapper.updateById(temp);


        //审核流程主信息
        FlowSelBiddingEntity biddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,biddingEntity);
        biddingEntity.setId(SnowFlake.getUUId());
        biddingEntity.setUnderwayId(flowBO.getUnderWayId());
        biddingEntity.setBiddingId(selBiddingEntity.getId());
        biddingEntity.setRoundId(selBiddingSequenceEntity.getId());
        flowSelBiddingService.save(biddingEntity);
        flowSelBiddingSuccessService.saveBatch(flowSelBiddingSuccessEntities);
        flowSelBiddingSuccessProductService.saveBatch(flowSelBiddingSuccessProductEntities);

        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("交易完成审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        )
                        .setCompanyId(selBiddingEntity.getCompanyId())
                        .setSpecific(ApproveStatus.JY_WC.getCode())
                        .setMsgContent(null)
        );

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );


        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selPlanVo.getUserId(),
                            "交易完成"
                    );
                }
        );


        //发送交易交易完成审批
//        SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
//        sysUserMsgEntity.setMsgType(MsgType.EXAMINE.getCode());
//        sysUserMsgEntity.setBusCode(ApproveStatus.JY_WC.getCode());
//        sysUserMsgEntity.setMsgAccessory(selBiddingEntity.getId());
//        sysUserMsgEntity.setCompanyId(selBiddingEntity.getCompanyId());
//        sysUserMsgEntity.setCreateBy(selPlanVo.getUserId());
//        rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.EXAMINE.getKey());
        return true;
    }

    @Override
    public SelBiddingEntity getPricing(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");


        //查询出轮次信息
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                .and(e -> e.eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum())
                        .eq(SelBiddingSequenceEntity::getBiddingId, selBiddingEntity.getId()))
        );


        //竞价商品
        selBiddingEntity.setProductList(selBiddingProductService.list(
                new LambdaQueryWrapper<SelBiddingProductEntity>()
                .eq(SelBiddingProductEntity::getSequenceId,sequenceEntity.getId()))
        );

        //报价公司
        List<SelBiddingCustomerEntity> customerList = selBiddingCustomerService.list(
                new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                .eq(SelBiddingCustomerEntity::getSequence, sequenceEntity.getId())
        );

        customerList.forEach(x->{

            x.setCustomerName(selCustomerService.getById(x.getCustomerId()).getCustomerName());
            //报价公司详情
            x.setOfferList(selBiddingCustomerOfferService.list(
                    new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                    .eq(SelBiddingCustomerOfferEntity::getRelevanceId,x.getId()))
            );
        });

        selBiddingEntity.setCustomerList(customerList);

        return selBiddingEntity;
    }

    @Override
    @Transactional
    public boolean submitBidding(SelPlanVo selPlanVo) {
        String id = selPlanVo.getId();
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);

        if (!selBiddingEntity.getIsDelete().equals(0)) {
            throw new RuntimeException("竞价已废弃,请刷新后操作");
        }

        if (selBiddingEntity.getBiddingStatus().equals(BiddingStatus.AUDIT.getCode())) {
            throw new RuntimeException("已提交竞价,请勿重复提交");
        }

        //判断该流程节点是否定义
        if (selApprovalDefinitionDao.selectCount(new LambdaQueryWrapper<SelApprovalDefinitionEntity>()
                .and(e->e.eq(SelApprovalDefinitionEntity::getCompanyId,selBiddingEntity.getCompanyId())
                        .eq(SelApprovalDefinitionEntity::getBusCode, ApproveStatus.ZJ_SH.getCode())))<=0) {
            throw new RuntimeException("竞价审核流程节点未定义,无法提交审核");
        }

        //查询轮次信息
        SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                        .eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum()))
        );

        //删除基价信息
        selBiddingFeeRecordService.remove(
                new LambdaQueryWrapper<SelBiddingFeeRecordEntity>()
                .eq(SelBiddingFeeRecordEntity::getSequenceId,sequence.getId())
        );

        //删除商品信息
        selBiddingProductService.remove(
                new LambdaQueryWrapper<SelBiddingProductEntity>()
                .eq(SelBiddingProductEntity::getSequenceId,sequence.getId())
        );

        //删除邀请的公司信息
        selBiddingCustomerService.remove(
                new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                .eq(SelBiddingCustomerEntity::getSequence,sequence.getId())
        );

        //删除轮次信息
        selBiddingSequenceService.removeById(sequence.getId());


        //竞价轮次表
        SelBiddingSequenceEntity selBiddingSequenceEntity=new SelBiddingSequenceEntity();
        selBiddingSequenceEntity.setId(SnowFlake.getUUId());
        selBiddingSequenceEntity.setBiddingId(selBiddingEntity.getId());
        selBiddingSequenceEntity.setSequence(selBiddingEntity.getRoundNum());
        selBiddingSequenceEntity.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        selBiddingSequenceEntity.setPlanStartDate(selBiddingEntity.getPlanStartDate());
        selBiddingSequenceEntity.setPlanEndTime(selBiddingEntity.getPlanEndTime());
        selBiddingSequenceService.save(selBiddingSequenceEntity);


        //审批需要复制一份信息
        List<FlowSelBiddingCustomerEntity> flowCustomer=new ArrayList<>();

        //竞价企业
        List<SelBiddingCustomerEntity> customerList = selPlanVo.getCustomerList();
        customerList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequence(selBiddingSequenceEntity.getId());

            //审批流程信息保存
            FlowSelBiddingCustomerEntity f=new FlowSelBiddingCustomerEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowCustomer.add(f);

        });
        selBiddingCustomerService.saveBatch(customerList);

        //基价调整记录
        List<SelBiddingFeeRecordEntity> feeRecordList=new ArrayList<>();

        //竞价商品
        List<SelBiddingProductEntity> productList = selPlanVo.getProductList();

        //审批信息复制
        List<FlowSelBiddingProductEntity> flowProduct=new ArrayList<>();

        productList.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setSequenceId(selBiddingSequenceEntity.getId());

            //竞价基价信息记录
            SelBiddingFeeRecordEntity feeRecordEntity=new SelBiddingFeeRecordEntity();
            feeRecordEntity.setId(SnowFlake.getUUId());
            feeRecordEntity.setSequenceId(selBiddingSequenceEntity.getId());
            feeRecordEntity.setBaseFee(x.getBaseFee());
            feeRecordEntity.setProductId(x.getProductId());
            feeRecordEntity.setProductName(x.getProductName());
            feeRecordList.add(feeRecordEntity);

            FlowSelBiddingProductEntity f=new FlowSelBiddingProductEntity();
            BeanUtils.copyProperties(x,f);
            f.setId(SnowFlake.getUUId());
            flowProduct.add(f);


        });
        selBiddingProductService.saveBatch(productList);

        //竞价初始基价
        selBiddingFeeRecordService.saveBatch(feeRecordList);

        SelBiddingEntity temp=new SelBiddingEntity();
        temp.setId(id);
        temp.setBiddingStatus(BiddingStatus.AUDIT.getCode());
        baseMapper.updateById(temp);


        //定义流程
        InitFlowBO flowBO = BusUtils.definitionFlow(
                ApproveStatus.ZJ_SH.getCode().toString(),
                selBiddingEntity.getCompanyId(),
                selBiddingEntity.getId(),
                selPlanVo.getUserId()
        );


        //保存流程主信息
        FlowSelBiddingEntity flowSelBiddingEntity=new FlowSelBiddingEntity();
        BeanUtils.copyProperties(selBiddingEntity,flowSelBiddingEntity);
        flowSelBiddingEntity.setId(SnowFlake.getUUId());
        flowSelBiddingEntity.setUnderwayId(flowBO.getUnderWayId());
        flowSelBiddingEntity.setRoundId(sequence.getId());
        flowSelBiddingEntity.setBiddingId(id);

        //保存审批流程相关的信息
        flowSelBiddingService.save(flowSelBiddingEntity);
        flowSelBiddingCustomerService.saveBatch(flowCustomer);
        flowSelBiddingProductService.saveBatch(flowProduct);

        //通知第一位审批人
        userMsgService.saveHistory(
                new SysUserMsgEntity()
                        .setMsgId(SnowFlake.getUUId())
                        .setMsgTitle("商品竞价审批")
                        .setBusCode(BusCode.USER_CHECK.getCode())
                        .setMsgType(MsgType.EXAMINE.getCode())
                        .setReader(flowBO.getLeadsId())
                        .setMsgAccessory(
                                String.format(
                                        "%s,%s",
                                        selBiddingEntity.getId(),
                                        flowBO.getFirstFlowId()
                                )
                        ).setCompanyId(selBiddingEntity.getCompanyId())
                        .setSpecific(ApproveStatus.ZJ_SH.getCode())
                        .setMsgContent(null)

        );

        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.sendMsg(flowBO.getLeadsId());
                }
        );

        //通知微信公众号
        ThreadPoolUtil.runTask(
                () -> {
                    BusUtils.wxAuditPush(
                            flowBO.getLeadsId(),
                            selPlanVo.getUserId(),
                            "商品竞价"
                    );
                }
        );
        return true;
    }

    @Override
    public List<SelBiddingCustomerEntity> filter(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"查询不到该主键信息");

        //查询轮次信息
        SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                        .eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum()))
        );

        //查询出邀请的公司
        List<SelBiddingCustomerEntity> list = selBiddingCustomerService.list(
                new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                .eq(SelBiddingCustomerEntity::getSequence,sequence.getId())
        );


        Iterator<SelBiddingCustomerEntity> iterator = list.iterator();
        while (iterator.hasNext()) {
            SelBiddingCustomerEntity next = iterator.next();
            //表示报过价
            if (selBiddingCustomerOfferService.count(
                    new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                    .eq(SelBiddingCustomerOfferEntity::getRelevanceId,next.getId()))>0
            ) {

                next.setCustomerName(selCustomerService.getById(next.getCustomerId()).getCustomerName());

            }else{
                iterator.remove();
            }
        }

        return list;
    }

    @Override
    public List<SelBiddingCustomerEntity> getCustomerAll(String id) {
        SelBiddingEntity selBiddingEntity = baseMapper.selectById(id);
        Assert.notNull(selBiddingEntity,"错误的主键信息.");


        //查询出轮次信息
        SelBiddingSequenceEntity sequence = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                .and(e -> e.eq(SelBiddingSequenceEntity::getBiddingId, id)
                        .eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum()))
        );

        //根据轮次查询出该轮报价的客户
        List<SelBiddingCustomerEntity> customer = selBiddingCustomerService.list(
                new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                .eq(SelBiddingCustomerEntity::getSequence, sequence.getId())
        );

        //过滤出已报过价的
        Iterator<SelBiddingCustomerEntity> iterator = customer.iterator();

        while (iterator.hasNext()) {
            SelBiddingCustomerEntity next = iterator.next();
            //表示报过价
            if (selBiddingCustomerOfferService.count(
                    new LambdaQueryWrapper<SelBiddingCustomerOfferEntity>()
                    .eq(SelBiddingCustomerOfferEntity::getRelevanceId,next.getId()))>0)
            {
                next.setCustomerName(selCustomerService.getById(next.getCustomerId()).getCustomerName());
                continue;
            }else{
                iterator.remove();
            }
        }

        return customer;
    }

    /**
     * 根据季度查询需要获取的开始时间和结束时间
     * @param quarterVo
     */
    private void getTimeDifference(QuarterVo quarterVo){
        String s = String.valueOf(quarterVo.getYear());

        switch (quarterVo.getQuarter()){
            case 1:
                quarterVo.setStartDate(DateUtils.defaultParse(s+"-01-01"));
                quarterVo.setEndDate(DateUtils.defaultParse(s+"-03-31"));
                break;
            case 2:
                quarterVo.setStartDate(DateUtils.defaultParse(s+"-04-01"));
                quarterVo.setEndDate(DateUtils.defaultParse(s+"-06-31"));
                break;
            case 3:
                quarterVo.setStartDate(DateUtils.defaultParse(s+"-07-01"));
                quarterVo.setEndDate(DateUtils.defaultParse(s+"-09-30"));
                break;
            case 4:
                quarterVo.setStartDate(DateUtils.defaultParse(s+"-10-01"));
                quarterVo.setEndDate(DateUtils.defaultParse(s+"-12-30"));
                break;
            default:
                throw new RuntimeException("错误的季度表示");
        }
    }


    @Override
    public List<QuarterReport> queryLivenessAndCloseRateReport(QuarterVo quarterVo) {

        List<QuarterReport> result=new ArrayList<>();



        getTimeDifference(quarterVo);

        //交易总量
        double successCount=0.0;


        //查询季度的所有询价销售计划
        List<String> ids = selPlanDao.getIds(quarterVo);
        if (!Assert.notNullCollect(ids)) {
            return new ArrayList<>();
        }

        //根据销售计划查询所有竞价
        List<SelBiddingEntity> selBiddingEntities = baseMapper.selectList(
                new QueryWrapper<SelBiddingEntity>()
                .select("id")
                .lambda()
                .and(e -> e.eq(SelBiddingEntity::getIsDelete, Opposite.ZERO)
                        .in(SelBiddingEntity::getPlanId, ids))
        );


        if (Assert.notNullCollect(selBiddingEntities)) {
            List<String> collect = CommonUtil.collect(selBiddingEntities, SelBiddingEntity::getId);

            //查询交易总量
            List<SelBiddingSuccessProductEntity> productEntities = selBiddingSuccessService.querySuccessCount(
                    collect, quarterVo.getProductId(), null
            );
            if (Assert.notNullCollect(productEntities)) {
                successCount = productEntities.stream().mapToDouble(x -> x.getCount().doubleValue()).sum();
            }


            //查询该公司所有企业用户
            List<SelCustomerEntity> list = selCustomerService.list(new QueryWrapper<SelCustomerEntity>()
                    .select("customer_name", "id")
                    .lambda().eq(SelCustomerEntity::getCompanyId, quarterVo.getCompanyId()));

            //封装每个公司的活跃度等信息
            if (Assert.notNullCollect(list)) {
                for (SelCustomerEntity selCustomerEntity : list) {

                    QuarterReport quarterReport = new QuarterReport();
                    //总量 总交互次数 总询价次数
                    quarterReport.setSuccessCount(successCount);
                    quarterReport.setMutualNum(
                            baseMapper.queryMutualNum(
                                    collect,
                                    quarterVo.getProductId(),
                                    selCustomerEntity.getId()
                            )+ids.size()
                    );
                    quarterReport.setPlanNum(selInquiryDao.queryInquiryCount(ids,selCustomerEntity.getId()));

                    //购买量 参与次数 成交次数
                    List<SelBiddingSuccessProductEntity> product = selBiddingSuccessService.querySuccessCount(
                            collect,
                            quarterVo.getProductId(),
                            selCustomerEntity.getId()
                    );
                    quarterReport.setBuyCount(
                            Assert.notNullCollect(product)?
                                    product.stream().mapToDouble(x->x.getCount().doubleValue()).sum():0.0
                    );
                    //查询出邀请该企业的
                    List<String> biddingCustomerIds = baseMapper.queryCustomerIds(
                            collect,
                            quarterVo.getProductId(),
                            selCustomerEntity.getId()
                    );
                    //查询竞价参与的次数
                    int offerCount = Assert.notNullCollect(biddingCustomerIds)?
                            baseMapper.queryCustomerOfferCount(biddingCustomerIds,quarterVo.getProductId()):0;
                    //查询询价邀请该企业的
                    List<String> inquiryCustomerIds = selInquiryDao.queryInquiryCustomerIds(
                            ids, selCustomerEntity.getId()
                    );
                    //查询询价参与的次数
                    int offerNum =Assert.notNullCollect(inquiryCustomerIds)?
                            selInquiryDao.queryInquiryCustomerOfferNum(inquiryCustomerIds,quarterVo.getProductId()):0;


                    quarterReport.setTakeNum(offerCount+offerNum);

                    //查询销售计划内交易成功的次数
                    quarterReport.setSuccessNum(product.size());

                    BigDecimal bigDecimal = new BigDecimal("100");

                    //计算成交量占比
                    if(quarterReport.getSuccessCount()==0.0){
                        quarterReport.setSuccessCountRatio(100.00);
                    }else{
                        BigDecimal divide = new BigDecimal(quarterReport.getBuyCount())
                                .divide(
                                        new BigDecimal(quarterReport.getSuccessCount()),
                                        4,
                                        BigDecimal.ROUND_DOWN
                                );
                        quarterReport.setSuccessCountRatio(divide.multiply(bigDecimal).doubleValue());
                    }
                    //计算活跃度
                    if(quarterReport.getMutualNum()==0){
                        quarterReport.setLiveness(100.00);
                    }else{
                        BigDecimal divide = new BigDecimal(quarterReport.getTakeNum())
                                .divide(
                                        new BigDecimal(quarterReport.getMutualNum()),
                                        4,
                                        BigDecimal.ROUND_DOWN
                                );
                        quarterReport.setLiveness(divide.multiply(bigDecimal).doubleValue());
                    }

                    //计算成交率
                    if(quarterReport.getPlanNum()==0){
                        quarterReport.setSuccessRatio(100.00);
                    }else{
                        BigDecimal divide = new BigDecimal(quarterReport.getSuccessNum())
                                .divide(
                                        new BigDecimal(quarterReport.getPlanNum()),
                                        4,
                                        BigDecimal.ROUND_DOWN
                                );
                        quarterReport.setSuccessRatio(divide.multiply(bigDecimal).doubleValue());
                    }
                    quarterReport.setCustomerName(selCustomerEntity.getCustomerName());
                    result.add(quarterReport);

                }
            } else {
                return new ArrayList<>();
            }
        }else{
            return new ArrayList<>();
        }
        return result;
    }

    @Override
    public Map<String, Object> exportLivenessAndCloseRateReport(QuarterVo quarterVo) {
        Map<String,Object> map=new HashMap<>();
        String title=quarterVo.getYear() + "年第" + quarterVo.getQuarter() + "季度汇总报表";
        List<List<String>> head = QuarterReport.getHead(title);
        List<QuarterReport> quarterReports = queryLivenessAndCloseRateReport(quarterVo);
        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();

        excelUtils.write(quarterReports,QuarterReport.class,byteArrayOutputStream,head);
        String excelBase64 = CommonUtil.getExcelBase64(byteArrayOutputStream);
        map.put("name",title);
        map.put("file",excelBase64);
        return map;
    }

    @Override
    @Transactional
    public boolean deleteById(String id) {

        SelBiddingEntity byId = selBiddingDao.selectById(id);

        if (!byId.getBiddingStatus().equals(BiddingStatus.BIDDING_OVER.getCode())&&
                !byId.getBiddingStatus().equals(BiddingStatus.AUDIT_ERROR.getCode())
        ) {
            throw new RuntimeException("状态已变更,刷新后再操作");
        }

        if (!byId.getIsDelete().toString().equals(Opposite.ZERO)) {
            throw new RuntimeException("数据已删除,刷新后再操作");
        }

        if (byId.getModeType().equals(BiddingModeTypeEnum.VOLUME_COUNT.getCode())) {

            //删除询价
            selInquiryDao.update(
                    new SelInquiryEntity()
                            .setIsDelete(1),
                    new LambdaQueryWrapper<SelInquiryEntity>()
                            .eq(SelInquiryEntity::getPlanId,byId.getPlanId())
            );
        }
        //删除销售计划
        selPlanDao.updateById(
                new SelPlanEntity()
                        .setId(byId.getPlanId())
                        .setIsDelete(1)
        );

        // 竞价更新
        selBiddingDao.updateById(
                new SelBiddingEntity()
                        .setId(id)
                        .setIsDelete(1)
        );

        return false;
    }


    /** 获取报表对象 */
    private List<ReportPlan> getReportExcel(SelBiddingEntity selBiddingEntity){
        List<ReportPlan> reportPlans=new ArrayList<>();

        //查询出轮次信息
        SelBiddingSequenceEntity sequenceEntity = selBiddingSequenceService.getOne(
                new LambdaQueryWrapper<SelBiddingSequenceEntity>()
                        .and(e -> e.eq(SelBiddingSequenceEntity::getSequence, selBiddingEntity.getRoundNum())
                                .eq(SelBiddingSequenceEntity::getBiddingId, selBiddingEntity.getId())));

        //查询出竞价商品信息
        List<SelBiddingProductEntity> product = selBiddingProductService.list(
                new LambdaQueryWrapper<SelBiddingProductEntity>()
                        .eq(SelBiddingProductEntity::getSequenceId, sequenceEntity.getId())
        );
        for (SelBiddingProductEntity selBiddingProductEntity : product) {

            //查询预计可销售量
            String planId = selBiddingEntity.getPlanId();
            Double sale_quantity = selPlanProductDetailDao.selectOne(
                    new QueryWrapper<SelPlanProductDetailEntity>()
                    .select("sale_quantity").lambda().and(e -> {
                        e.eq(SelPlanProductDetailEntity::getPlanId, planId)
                                .eq(SelPlanProductDetailEntity::getProductId, selBiddingProductEntity.getProductId());
                    })).getSaleQuantity();


            //查询轮次的基价
            BigDecimal base_fee = selBiddingFeeRecordService.getOne(
                    new QueryWrapper<SelBiddingFeeRecordEntity>()
                            .select("base_fee").lambda()
                    .and(e -> e.eq(SelBiddingFeeRecordEntity::getSequenceId, sequenceEntity.getId())
                            .eq(SelBiddingFeeRecordEntity::getProductId,selBiddingProductEntity.getProductId())))
                    .getBaseFee();


            //查询公司关联
            List<SelBiddingCustomerEntity> customer = selBiddingCustomerService.list(
                    new LambdaQueryWrapper<SelBiddingCustomerEntity>()
                            .eq(SelBiddingCustomerEntity::getSequence, sequenceEntity.getId())
            );

            List<String> collect = CommonUtil.collect(customer, SelBiddingCustomerEntity::getId);

            List<ReportPlan> temp=new ArrayList<>();
            //查询该次竞价改商品的关联报价信息
            List<SelBiddingCustomerOfferEntity> offerList = baseMapper.getOfferList(
                    selBiddingProductEntity.getProductId(),collect
            );
            offerList.forEach(x->{
                ReportPlan reportPlan=new ReportPlan();
                reportPlan.setProductName(x.getProductName());
                reportPlan.setCustomerName(x.getOffererName());
                reportPlan.setCount(x.getCount());
                reportPlan.setSellCount(sale_quantity);
                reportPlan.setPrice(base_fee.doubleValue());
                temp.add(reportPlan);
            });


            if (Assert.notNullCollect(temp)) {
                temp.sort((z,y)->-z.getCount().compareTo(y.getCount()));
                temp.get(0).setSize(temp.size()+1);
                reportPlans.addAll(temp);

                ReportPlan reportPlan=new ReportPlan();
                reportPlan.setProductName(temp.get(0).getProductName());
                reportPlan.setPrice(temp.get(0).getPrice());
                reportPlan.setSellCount(temp.get(0).getSellCount());
                reportPlan.setCustomerName("合计");
                reportPlan.setFlag(Boolean.TRUE);
                reportPlan.setCount(temp.stream().mapToDouble(x->x.getCount()).sum());
                reportPlans.add(reportPlan);

            }
        }

        return reportPlans;
    }

}