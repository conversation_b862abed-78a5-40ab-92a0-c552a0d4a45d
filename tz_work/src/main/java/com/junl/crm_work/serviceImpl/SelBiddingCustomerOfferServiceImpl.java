package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

import com.junl.crm_work.dao.SelBiddingCustomerOfferDao;
import com.junl.crm_work.vo.SelBiddingCustomerOfferVo;
import com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity;
import com.junl.crm_work.service.SelBiddingCustomerOfferService;
import com.junl.crm_work.vo.SelInquiryOfferVo;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelBiddingCustomerOfferServiceImpl
        extends ServiceImpl<SelBiddingCustomerOfferDao, SelBiddingCustomerOfferEntity>
        implements SelBiddingCustomerOfferService
{
    /**
     * @param selBiddingCustomerOfferVo
     * @description: 分页查询列表
     */
    @Override
    public PageEntity queryPage(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo) {
        PageUtils.execute(selBiddingCustomerOfferVo);
        List<SelBiddingCustomerOfferEntity> list = baseMapper.queryList(selBiddingCustomerOfferVo);
        return PageUtils.getData(list);
    }

    /**
     * @param selBiddingCustomerOfferVo
     * @description: 列表查询列表
     */
    @Override
    public Result queryList(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo, SelCustomerEntity customerEntity) {
        selBiddingCustomerOfferVo.setCustomerId(customerEntity.getId());
        List<SelBiddingCustomerOfferEntity> list = baseMapper.queryList(selBiddingCustomerOfferVo);
        return  Result.success(list);
    }


    /**
     * @param selBiddingCustomerOfferVo
     * @description: 返回列表查询列表
     */
    @Override
    public List<SelBiddingCustomerOfferEntity> getList(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo) {
        return  baseMapper.queryList(selBiddingCustomerOfferVo);
    }


    /**
     * @param selBiddingCustomerOfferVo
     * @description: 查询一条记录
     */
    @Override
    public SelBiddingCustomerOfferEntity selectOfferOne(SelBiddingCustomerOfferVo selBiddingCustomerOfferVo) {
        return  baseMapper.selectOfferOne(selBiddingCustomerOfferVo);
    }
}