package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.ExcelResult;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.junl.crm_common.tools.excel.ExcelUtils;
import com.junl.crm_work.dao.SelCustomerDao;
import com.junl.crm_work.excel.CustomerService;
import com.junl.crm_work.util.CodeGenerate;
import com.junl.crm_work.vo.CustomerExcel;
import com.junl.crm_work.vo.SelCustomerVo;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_work.service.SelCustomerService;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelCustomerServiceImpl
        extends ServiceImpl<SelCustomerDao, SelCustomerEntity>
        implements SelCustomerService
{

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    private CodeGenerate codeGenerate;
    /**
     * @description: 分页查询列表
     * @param selCustomerVo
     */
    @Override
    public PageEntity queryPage(SelCustomerVo selCustomerVo) {
        PageUtils.execute(selCustomerVo);
        List<SelCustomerEntity> list = baseMapper.queryList(selCustomerVo);
        return PageUtils.getData(list);
    }

    @Override
    public String download(ByteArrayOutputStream outputStream) {
        ArrayList<SelCustomerEntity> selCustomerEntities = new ArrayList<>();
        selCustomerEntities.add(new SelCustomerEntity());
        excelUtils.write(selCustomerEntities,SelCustomerEntity.class,outputStream);
        String excelBase64 = CommonUtil.getExcelBase64(outputStream);
        return excelBase64;
    }

    @Override
    public ExcelResult importExcel(String url, SysUserEntity sysUserEntity) {
        ExcelResult excelResult=new ExcelResult();
        excelUtils.read(
                url,
                SelCustomerEntity.class,
                new CustomerService(codeGenerate,sysUserEntity,this),
                excelResult
        );
        return excelResult;
    }


    @Override
    public int updateAppCustomerById(SelCustomerEntity customerEntity) {
        return baseMapper.updateAppCustomerById(customerEntity);
    }

    @Override
    public boolean updateWxAuth(String id) {
        return baseMapper.updateWxAuth(id);
    }

    @Override
    public Map<String, String> exportExcel(SelCustomerVo selCustomerVo) {
        Map<String, String> map=new HashMap<>();
        List<SelCustomerEntity> selCustomerEntities = baseMapper.queryList(selCustomerVo);
        List<CustomerExcel> list=new ArrayList<>();
        selCustomerEntities.forEach(x->{
            CustomerExcel customerExcel=new CustomerExcel();
            customerExcel.setCustomerName(x.getCustomerName());
            customerExcel.setCustomerCode(x.getCustomerCode());
            customerExcel.setManageProduct(x.getManageProduct()!=null?getType(Integer.valueOf(x.getManageProduct())):null);
            customerExcel.setDeputy(x.getDeputy());
            customerExcel.setDeputyCardNo(x.getDeputyCardNo());
            customerExcel.setDeputyPhone(x.getDeputyPhone());
            customerExcel.setRegisterFee(x.getRegisterFee()!=null?x.getRegisterFee().doubleValue():null);
            customerExcel.setTelephone(x.getTelephone());
            customerExcel.setIs_auth(x.getIsAuth().equals(Integer.valueOf(Opposite.ZERO))?"未认证":"已认证");
            customerExcel.setProvince(x.getProvince());
            customerExcel.setCity(x.getCity());
            customerExcel.setRegion(x.getRegion());
            customerExcel.setPostalCode(x.getPostalCode());
            customerExcel.setDetailAddress(x.getDetailAddress());
            customerExcel.setIsWxAuth(x.getIsWxAuth().equals(Integer.valueOf(Opposite.ZERO))?"未认证":"已认证");
            list.add(customerExcel);
        });
        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        excelUtils.write(list,CustomerExcel.class,byteArrayOutputStream);
        String excelBase64 = CommonUtil.getExcelBase64(byteArrayOutputStream);
        map.put("name","企业信息明细");
        map.put("file",excelBase64);
        return map;
    }

    private String getType(Integer type){
        switch (type){
            case 1:
                return "燃料油类";
            case 2:
                return "化工类";
            case 3:
                return "苯类";
            default:
                return "其他";
        }
    }
}