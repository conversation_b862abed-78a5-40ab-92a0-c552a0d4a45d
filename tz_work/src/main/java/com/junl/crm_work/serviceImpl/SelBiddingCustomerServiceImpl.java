package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_work.dao.SelBiddingCustomerDao;
import com.junl.crm_work.vo.SelBiddingCustomerVo;
import com.junl.crm_common.pojo.work.SelBiddingCustomerEntity;
import com.junl.crm_work.service.SelBiddingCustomerService;
import com.junl.crm_work.vo.SelBiddingVo;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelBiddingCustomerServiceImpl
        extends ServiceImpl<SelBiddingCustomerDao, SelBiddingCustomerEntity>
        implements SelBiddingCustomerService
{
    /**
     * @description: 分页查询列表
     * @param selBiddingCustomerVo
     */
    @Override
    public PageEntity queryPage(SelBiddingCustomerVo selBiddingCustomerVo) {
        PageUtils.execute(selBiddingCustomerVo);
        List<SelBiddingCustomerEntity> list = baseMapper.queryList(selBiddingCustomerVo);
        return PageUtils.getData(list);
    }

}