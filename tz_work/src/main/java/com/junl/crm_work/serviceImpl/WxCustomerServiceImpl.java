package com.junl.crm_work.serviceImpl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_work.dao.WxCustomerDao;
import com.junl.crm_work.service.WxCustomerService;
import com.junl.crm_work.work.vo.WxCustomerVo;
import com.junl.crm_common.pojo.work.WxCustomerEntity;
import org.springframework.stereotype.Service;

/**
* @author: daiqimeng
* @create: 2020-07-21 11:11
**/
@Service
public class WxCustomerServiceImpl
        extends ServiceImpl<WxCustomerDao, WxCustomerEntity>
        implements WxCustomerService
{
/**
* @description: 分页查询列表
* @param wxCustomerVo
*/
@Override
public PageEntity queryPage(WxCustomerVo wxCustomerVo) {
    PageUtils.execute(wxCustomerVo);
    List<WxCustomerEntity> list = baseMapper.queryList(wxCustomerVo);
    return PageUtils.getData(list);
  }
}