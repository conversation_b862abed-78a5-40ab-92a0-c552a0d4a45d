package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SelInquiryOfferEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_work.dao.SelPlanProductDetailDao;
import com.junl.crm_work.vo.SelPlanProductDetailVo;
import com.junl.crm_common.pojo.work.SelPlanProductDetailEntity;
import com.junl.crm_work.service.SelPlanProductDetailService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SelPlanProductDetailServiceImpl
        extends ServiceImpl<SelPlanProductDetailDao, SelPlanProductDetailEntity>
        implements SelPlanProductDetailService
{
    /**
     * @description: 分页查询列表
     * @param selPlanProductDetailVo
     */
    @Override
    public PageEntity queryPage(SelPlanProductDetailVo selPlanProductDetailVo) {
        PageUtils.execute(selPlanProductDetailVo);
        List<SelPlanProductDetailEntity> list = baseMapper.queryList(selPlanProductDetailVo);
        return PageUtils.getData(list);
    }
}