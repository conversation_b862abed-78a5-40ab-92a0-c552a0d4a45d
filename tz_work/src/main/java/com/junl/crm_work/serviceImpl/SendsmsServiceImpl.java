package com.junl.crm_work.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.work.SendsmsEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

import com.junl.crm_work.dao.SendsmsDao;
import com.junl.crm_work.service.SendsmsService;
import com.junl.crm_work.vo.SendsmsVo;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SendsmsServiceImpl
        extends ServiceImpl<SendsmsDao, SendsmsEntity>
        implements SendsmsService
{
    /**
     * @param sendsmsVo
     * @description: 分页查询列表
     */
    @Override
    public PageEntity queryPage(SendsmsVo sendsmsVo) {
        PageUtils.execute(sendsmsVo);
        List<SendsmsEntity> list = baseMapper.queryList(sendsmsVo);
        return PageUtils.getData(list);
    }
}