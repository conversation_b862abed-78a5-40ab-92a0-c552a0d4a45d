package com.junl.crm_work.serviceImpl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.work.RecvsmsEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

import com.junl.crm_work.dao.RecvsmsDao;
import com.junl.crm_work.service.RecvsmsService;
import com.junl.crm_work.vo.RecvsmsVo;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class RecvsmsServiceImpl extends ServiceImpl<RecvsmsDao, RecvsmsEntity>
        implements RecvsmsService
{
    /**
     * @param recvsmsVo
     * @description: 分页查询列表
     */
    @Override
    public PageEntity queryPage(RecvsmsVo recvsmsVo) {
        PageUtils.execute(recvsmsVo);
        List<RecvsmsEntity> list = baseMapper.queryList(recvsmsVo);
        return PageUtils.getData(list);
    }
}