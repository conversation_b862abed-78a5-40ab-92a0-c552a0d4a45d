package com.junl.crm_work.wx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_common.tools.SpringUtil;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/3016:51
 */
@Component
@Log4j2
public class WxUtils {


    @Value("${wx.appid}")
    private String appid;

    @Value("${wx.appsecret}")
    private String appsecret;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RedisUtils redisUtils;




    @Setter
    @Getter
    @Accessors(chain = true)
    public static class WxUser{

        private Integer subscribe;
        private String openid;
        private String unionid;
    }


    public List<WxUser> getUnionIdAll(List<String> openIds){

        List<WxUser> result =new ArrayList<>();

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        JSONArray args = new JSONArray();
        for (String openId : openIds) {
            JSONObject param = new JSONObject();
            param.put("openid",openId);
            args.add(param);
        }

        JSONObject bodyArg = new JSONObject();
        bodyArg.put("user_list",args);

        HttpEntity<String> httpEntity = new HttpEntity<>(
                JSONObject.toJSONString(bodyArg),
                httpHeaders
        );

        ResponseEntity<String> response = restTemplate.postForEntity(
                "https://api.weixin.qq.com/cgi-bin/user/info/batchget?access_token=" + getAccessToken(),
                httpEntity,
                String.class
        );

        if (response.getStatusCodeValue()==200) {
            String body = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(body);
            JSONArray userInfoList = jsonObject.getJSONArray("user_info_list");
            for (Object o : userInfoList) {
                JSONObject temp= (JSONObject) o;
                result.add(
                        new WxUser()
                                .setSubscribe(temp.getInteger("subscribe"))
                                .setOpenid(temp.getString("openid"))
                                .setUnionid(temp.getString("unionid"))
                );
            }

        }else {
            log.error("批量获取uniId错误: {}",response.getStatusCodeValue());
        }
        return result;
    }


    //获取公众号所有关注用户
    public  List<String> getUserOpenIds(){
        List<String> list = new ArrayList<>();

        ResponseEntity<String> forEntity = restTemplate.getForEntity(
                String.format(
                        "https://api.weixin.qq.com/cgi-bin/user/get?access_token=%s&next_openid=",
                        getAccessToken()
                ),
                String.class
        );

        if (forEntity.getStatusCodeValue()==200) {

            String body = forEntity.getBody();
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("data")) {
                JSONObject o = jsonObject.getJSONObject("data");
                JSONArray openid = o.getJSONArray("openid");
                for (Object object : openid) {
                    list.add(object.toString());
                }
            }
            return list;
        }else {
            log.warn("获取公众号列表失败：{}",forEntity.getStatusCodeValue());
            return list;
        }

    }

    /**
     * @describe:  获取accessToken
     * <AUTHOR>
     * @date 2021/7/30 17:00
     * @param
     * @return: {@link String}
     */
    private String getAccessToken(){
        String accessToken = redisUtils.get("accessToken");
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        ResponseEntity<Map> forEntity = restTemplate.getForEntity("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + appsecret, Map.class);
        Map body = forEntity.getBody();
        body.forEach((x,y)->{
            log.info("x : {}      y  : {} ",x,y);
        });
        if (!body.containsKey("access_token")) {
            throw new RuntimeException("获取access_token失败");
        }
        Object access_token = body.get("access_token");
        String aToken=(String) access_token;
        redisUtils.set("accessToken",aToken,5);
        return aToken;
    }

    public Optional<String> getUniId(String code){
        Environment env = SpringUtil.getBean(Environment.class);
        String appId = env.getProperty("wxminapp.appId");
        String appSecret = env.getProperty("wxminapp.appSecret");
        String url = env.getProperty("wxminapp.wxLoginUrl");


        ResponseEntity<String> forEntity = restTemplate.getForEntity(
                String.format(
                        "%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                        url,
                        appId,
                        appSecret,
                        code
                ),
                String.class
        );

        if (forEntity.getStatusCodeValue()==200) {
            String body = forEntity.getBody();

            JSONObject jsonObject = JSONObject.parseObject(body);

            if (jsonObject.containsKey("unionid")) {
                log.info("获取unionId成功------------------------------------");
                return Optional.of(jsonObject.getString("unionid"));
            }else {
                log.warn("获取uniId意外: {}",JSONObject.toJSONString(body));
                return Optional.ofNullable(null);
            }
        }else {
            log.error("获取uniId错误: {}",forEntity.getStatusCode());
            return Optional.ofNullable(null);
        }

    }

    /**
     * 发送模板消息
     * @param wxMessage
     * @return
     */
    public boolean sendPost(WxTemplateMsg wxMessage){
        String s = JSONObject.toJSONString(wxMessage);
        HttpHeaders headers = new HttpHeaders();
        // 以表单的方式提交
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity<>(s,headers);
        Map body = restTemplate.exchange("https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + getAccessToken(), HttpMethod.POST, httpEntity, Map.class).getBody();
        if (body.containsKey("errmsg")) {
            if (body.get("errmsg").equals("ok")) {
                return true;
            }
        }
        return false;
    }

    public boolean sendPostDev(WxTemplateMsgDev wxMessage){
        String s = JSONObject.toJSONString(wxMessage);
        HttpHeaders headers = new HttpHeaders();
        // 以表单的方式提交
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity<>(s,headers);
        String body = restTemplate.exchange("https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + getAccessToken(), HttpMethod.POST, httpEntity, String.class).getBody();
        log.info("公众号模板消息推送: {}",body);
        JSONObject jsonObject = JSONObject.parseObject(body, JSONObject.class);
        if (jsonObject.containsKey("errmsg")) {
            if (jsonObject.getString("errmsg").equals("ok")) {
                return true;
            }
        }
        return false;
    }


    /**
     * 获取OpenId
     * @param code
     * @return
     */
    public String getOpenId(String code){
        String body = restTemplate.getForEntity("https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appid + "&secret=" + appsecret + "&code=" + code + "&grant_type=authorization_code", String.class).getBody();
        Map map = JSONObject.parseObject(body, Map.class);
        if(map.containsKey("openid")){
            return (String) map.get("openid");
        }
        throw new RuntimeException("系统错误获取身份标识失败.");
    }

    /**
     * 创建菜单
     * @return
     */
    public boolean createMenu(){
        Map<String, List<Map>> menus = createMenus();
        System.out.println(JSONObject.toJSONString(menus, SerializerFeature.PrettyFormat));
        String body = restTemplate.postForEntity("https://api.weixin.qq.com/cgi-bin/menu/create?access_token=" + getAccessToken(), JSONObject.toJSONString(createMenus()), String.class).getBody();
        System.out.println(body);
        return true;
    }

    public boolean deleteMenu(){
        Map body = restTemplate.getForEntity("https://api.weixin.qq.com/cgi-bin/menu/delete?access_token=" + getAccessToken(), Map.class).getBody();
        return body.get("errcode").equals(0);
    }




    private static Map<String,List<Map>> createMenus(){
        Map<String,List<Map>> map=new HashMap<>();
        List<Map> list1=new ArrayList<>();

        //进入小程序按钮
        List<Map> applet=new ArrayList<>();
        Map app1=new HashMap();
        try {
            app1.put("type","miniprogram");
            app1.put("name","企业端");
            app1.put("url","weixin://dl/business/?t=4NWLN747Zmr");
            app1.put("appid","wx88279284fe3f4ada");
            app1.put("pagepath","pages/index/index");

            Map app2=new HashMap();
            app2.put("type","miniprogram");
            app2.put("name","管理端");
            app2.put("url","weixin://dl/business/?t=ia4e7ZvWrol");
            app2.put("appid","wxa39e0ff624927344");
            app2.put("pagepath","pages/index/index");

            applet.add(app2);
            applet.add(app1);

            Map map1=new HashMap();
            map1.put("name","进入小程序");
            map1.put("sub_button",applet);


            //用户认证按钮
            List<Map> auth=new ArrayList<>();
            Map aurh1=new HashMap();
            aurh1.put("type","view");
            aurh1.put("name","企业端");
            aurh1.put("url","http://tzjja.tzpec.com.cn/tx");

            Map auth2=new HashMap();
            auth2.put("type","view");
            auth2.put("name","管理端");
            auth2.put("url","http://tzjja.tzpec.com.cn/systx");
            auth.add(aurh1);
            auth.add(auth2);

            Map map2=new HashMap();
            map2.put("name","用户认证");
            map2.put("sub_button",auth);
            list1.add(map1);
            list1.add(map2);
            map.put("button",list1);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return map;
    }


    /**
     * 上传图片
     * @param filePath
     * @return
     */
    public String uploadImage(String filePath){
        String url = UriComponentsBuilder.fromHttpUrl("https://api.weixin.qq.com/cgi-bin/material/add_material")
                .queryParam("access_token", getAccessToken())
                .queryParam("type", "image")
                .build().toUriString();


        MultiValueMap<String,Object> data = new LinkedMultiValueMap<>();
        FileSystemResource fileSystemResource=new FileSystemResource(filePath);
        data.add("media",fileSystemResource);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
        httpHeaders.setContentLength(fileSystemResource.getFile().length());

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(data,
                httpHeaders);

        String  body= restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class).getBody();
        Map map = JSONObject.parseObject(body, Map.class);
        Object media_id = map.get("media_id");

        return media_id!=null?(String) media_id:null;

    }


    //组装发送的消息
    public WxTemplateMsg assembly(SendMsgEntity sendMsgEntity){
        WxTemplateMsg wx=new WxTemplateMsg();
        wx.setTouser(sendMsgEntity.getUser());
        WxTemplateMsg.Datasets data = wx.getData();
        data.getFirst().setValue(sendMsgEntity.getTitle());
        data.getKeyword1().setValue(sendMsgEntity.getWord1());
        data.getKeyword2().setValue(sendMsgEntity.getWord2());
        data.getKeyword3().setValue(StringUtils.isBlank(sendMsgEntity.getWord3())?"":sendMsgEntity.getWord3());
        data.getRemark().setValue(sendMsgEntity.getRemark());
        return wx;
    }

    public WxTemplateMsgDev assemblyDev(SendMsgEntity sendMsgEntity){
        WxTemplateMsgDev wxTemplateMsgDev = new WxTemplateMsgDev();
        wxTemplateMsgDev.setTouser(sendMsgEntity.getUser());
        WxTemplateMsgDev.DataValue data = wxTemplateMsgDev.getData();
        data.getFirst().setValue(sendMsgEntity.getTitle());
        data.getKeyword1().setValue(sendMsgEntity.getWord1());
        data.getKeyword2().setValue(sendMsgEntity.getWord2());
        data.getKeyword3().setValue(StringUtils.isBlank(sendMsgEntity.getWord3())?"占用字段":sendMsgEntity.getWord3());
        data.getKeyword4().setValue("占用字段");
        data.getRemark().setValue(sendMsgEntity.getRemark());
        return wxTemplateMsgDev;
    }
}
