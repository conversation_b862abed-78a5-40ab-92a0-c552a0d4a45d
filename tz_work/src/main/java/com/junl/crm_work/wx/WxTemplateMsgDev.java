package com.junl.crm_work.wx;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Description:
 *
 * <AUTHOR> <PERSON>
 * @date 2024/3/25 13:51
 */

@Setter
@Getter
@Accessors(chain = true)
public class WxTemplateMsgDev {

    private String touser;
    private String template_id;
//    private String page = "index";
    private DataValue data=new DataValue();


    @Data
    public static class DataValue{

        private Element first=new Element();

        private Element keyword1 = new Element();
        private Element keyword2 = new Element();

        private Element keyword3 =new Element();
        private Element keyword4 =new Element();
        private Element remark =new Element();

        @Data
        static class Element{
            private String value;
            private String color="#173177";
        }
    }

}
