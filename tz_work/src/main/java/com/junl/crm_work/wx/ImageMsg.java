package com.junl.crm_work.wx;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlCData;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.junl.crm_work.vo.InquiryExcel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/8/1722:15
 */
@JacksonXmlRootElement(localName = "xml")
@Data
public class ImageMsg {

    @JacksonXmlProperty(localName = "MediaId")
    @JacksonXmlCData
    private String mediaId;

}
