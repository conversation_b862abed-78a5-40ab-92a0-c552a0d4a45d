package com.junl.crm_work.wx;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.constant.DySmsEnum;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.pojo.work.SmsRecordEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_common.tools.sms.SmsRestResult;
import com.junl.crm_common.tools.sms.SmsSendParam;
import com.junl.crm_common.tools.sms.TzSmsHelper;
import com.junl.crm_work.dao.SmsErrorDao;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.crm_work.vo.SelCustomerVo;
import com.junl.system.UserService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * @description: 微信消息通知
 * @author: daiqimeng
 * @date: 2021/7/3016:51
 */

@Log4j2
@RestController
@RequestMapping("/wx")
public class WxController {

    @Autowired
    private WxUtils wxUtils;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private TzSmsHelper tzSmsHelper;

    @Autowired
    private SelCustomerService selCustomerService;

    @Reference
    private UserService userService;

    @Autowired
    private SmsErrorDao smsErrorDao;



    @Setter
    @Getter
    public static class LoginInfo{
        private String code;
        private String phone;
    }

    @PostMapping("/loginBind")
    public Result loginBind(@RequestBody  LoginInfo loginInfo){
        //获取uniId
        Optional<String> uniId = wxUtils.getUniId(loginInfo.getCode());
        if (uniId.isPresent()) {
            //redis存在才绑定
            if (redisUtils.hashKey(RedisKey.OPEN_ID.getName(), uniId.get())) {
                String openId = (String) redisUtils.hGet(RedisKey.OPEN_ID.getName(), uniId.get());

                //判断手机号是用户还是客户
                //先判断用户原则
                SelCustomerEntity customer = selCustomerService.getOne(new LambdaQueryWrapper<SelCustomerEntity>().and(e->{
                    e.eq(SelCustomerEntity::getDeputyPhone, loginInfo.getPhone())
                            .eq(SelCustomerEntity::getIsDelete,Opposite.ZERO);
                }));

                if (ObjectUtils.isEmpty(customer)) {
                    SysUserSoleEntity soleId = userService.getSoleId(loginInfo.phone);
                    Assert.notNull(soleId,"手机号错误,查询不到该用户");

                    userService.updateOpenId(openId,soleId.getSoleId());
                }else {
                    SelCustomerEntity temp=new SelCustomerEntity();
                    temp.setIsWxAuth(Integer.valueOf(Opposite.SINGLE));
                    temp.setOpenId(openId);
                    temp.setId(customer.getId());
                    selCustomerService.updateById(temp);
                }

            }
        }
        return Result.success();
    }


    @PostMapping(value = "/msg",produces = {MediaType.TEXT_XML_VALUE},consumes = {MediaType.APPLICATION_XML_VALUE,MediaType.TEXT_XML_VALUE})
    public WxMessage msg(@RequestBody WxMessage wxMessage, HttpServletRequest request){
        String msgType = wxMessage.getMsgType();
        if (msgType.equals("event")) {
            //订阅
            if (wxMessage.getEvent().equals("subscribe")) {
                return wxMessage(wxMessage.getToUserName(),wxMessage.getFromUserName());
                //取消订阅
            }else if(wxMessage.getEvent().equals("unsubscribe")){
                String openId = wxMessage.getFromUserName();
                SelCustomerEntity customerEntity = selCustomerService.getOne(new LambdaQueryWrapper<SelCustomerEntity>()
                        .eq(SelCustomerEntity::getOpenId, openId));
                if (Assert.notNull(customerEntity)) {
                    selCustomerService.updateWxAuth(customerEntity.getId());
                }
            }
        }else if(msgType.equals("text")){
            String content = wxMessage.getContent();
            if (content.equals("1")) {
                return appletMessage(wxMessage.getToUserName(),wxMessage.getFromUserName());
            }else if(content.equals("2")){
                return authMessage(wxMessage.getToUserName(),wxMessage.getFromUserName());
            }else{
                return defaultMessage(wxMessage.getToUserName(),wxMessage.getFromUserName());
            }
        }
        return null;
    }

    @GetMapping("/sendSms/{phone}")
    public Result sendSms(@PathVariable("phone")String phone){
        SmsSendParam smsSendParam=new SmsSendParam();
        smsSendParam.setUserNumber(phone);
        String randomNumber = RandomsUtil.getRandomNumber(6);
        smsSendParam.setMessageContent(randomNumber);

        log.info("认证短信验证码为: {}",randomNumber);

        SmsRestResult smsRestResult = tzSmsHelper.sendSms(smsSendParam, DySmsEnum.LOGIN_TEMPLATE_CODE);
        if (smsRestResult.getResult()==Integer.valueOf(Opposite.ZERO)) {
            redisUtils.set(RedisKey.PHONE.getName()+phone,randomNumber,300);
            smsErrorDao.insert(
                    new SmsRecordEntity()
                            .setPhone(phone)
                            .setDes(
                                    String.format(
                                            "%s: %s",
                                            randomNumber,
                                            smsRestResult.getDescription()
                                    )
                            )
                            .setStage(0)
            );
            return Result.success();
        }else{
            redisUtils.set(RedisKey.PHONE.getName()+phone,randomNumber,300);
            smsErrorDao.insert(
                    new SmsRecordEntity()
                            .setPhone(phone)
                            .setDes(
                                    String.format(
                                            "%s: %s",
                                            randomNumber,
                                            smsRestResult.getDescription()
                                    )
                            )
                            .setStage(1)
            );
            return Result.error("短信繁忙,请稍后重试.");
        }
    }


//    @GetMapping("/sysSendSms/{phone}")
//    public Result sysSendSms(@PathVariable("phone")String phone){
//        SmsSendParam smsSendParam=new SmsSendParam();
//        smsSendParam.setUserNumber(phone);
//        String randomNumber = RandomsUtil.getRandomNumber(6);
//        smsSendParam.setMessageContent(randomNumber);
//
//
//
//        SmsRestResult smsRestResult = tzSmsHelper.sendSms(smsSendParam, DySmsEnum.LOGIN_TEMPLATE_CODE);
//        if (smsRestResult.getResult()==Integer.valueOf(Opposite.ZERO)) {
//            redisUtils.set(RedisKey.PHONE_SYS.getName()+phone,randomNumber,300);
//            return Result.success();
//        }else{
//            smsErrorDao.insert(
//                    new SmsErrorEntity()
//                            .setPhone(phone)
//                            .setDes(smsRestResult.getDescription())
//            );
//
//            return Result.error("短信繁忙,请稍后重试.");
//        }
//    }

    @GetMapping("/msg")
    public String msg(@RequestParam("echostr")String echostr){
        return echostr;
    }


    @PostMapping("/auth")
    public Result auth(@RequestBody SelCustomerVo selCustomerVo){
        Validate.startValidate(new NotNullVerification(selCustomerVo,null));
        String smsCode = selCustomerVo.getSmsCode();
        String phone = selCustomerVo.getPhone();
        String s = redisUtils.get(RedisKey.PHONE.getName()+phone);


        Assert.notNull(s,"验证码已过期,请重新发送验证码.");

        if (!smsCode.equals(s)) {
            throw new RuntimeException("验证码错误,请重新输入验证码.");
        }


        SelCustomerEntity customer = selCustomerService.getOne(new LambdaQueryWrapper<SelCustomerEntity>().and(e->{
            e.eq(SelCustomerEntity::getDeputyPhone, selCustomerVo.getPhone())
                    .eq(SelCustomerEntity::getIsDelete,Opposite.ZERO);
        }));

        if (ObjectUtils.isEmpty(customer)) {
            SysUserSoleEntity soleId = userService.getSoleId(phone);
            Assert.notNull(soleId,"手机号错误,查询不到该用户");

            String openId = wxUtils.getOpenId(selCustomerVo.getCode());
            userService.updateOpenId(openId,soleId.getSoleId());
        }else {
            String openId = wxUtils.getOpenId(selCustomerVo.getCode());

            SelCustomerEntity temp=new SelCustomerEntity();
            temp.setIsWxAuth(Integer.valueOf(Opposite.SINGLE));
            temp.setOpenId(openId);
            temp.setId(customer.getId());
            selCustomerService.updateById(temp);
        }
        return Result.success();
    }

//    @PostMapping("/sysAuth")
//    public Result sysAuth(@RequestBody SelCustomerVo selCustomerVo){
//        Validate.startValidate(new NotNullVerification(selCustomerVo,null));
//
//        String smsCode = selCustomerVo.getSmsCode();
//        String phone = selCustomerVo.getPhone();
//        String s = redisUtils.get(RedisKey.PHONE_SYS.getName()+phone);
//
//        Assert.notNull(s,"验证码已过期,请重新发送验证码.");
//
//        if (!smsCode.equals(s)) {
//            throw new RuntimeException("验证码错误,请重新输入验证码.");
//        }
//
//        SysUserSoleEntity soleId = userService.getSoleId(phone);
//        Assert.notNull(soleId,"手机号错误,查询不到该用户");
//
//        String openId = wxUtils.getOpenId(selCustomerVo.getCode());
//        userService.updateOpenId(openId,soleId.getSoleId());
//
//        return Result.success();
//    }




    //关注公共号回复
    private WxMessage wxMessage(String toUserName,String fromUserName){
        WxMessage wxMessage=new WxMessage();
        wxMessage.setMsgType("text");
        wxMessage.setToUserName(fromUserName);
        wxMessage.setFromUserName(toUserName);
        wxMessage.setCreateTime(System.currentTimeMillis()/1000);
        wxMessage.setContent("欢迎关注泰州石化微服务公众号.\n\n\n\n" +
                "如果您是我公司客户,请回复序号获取服务：\n\n\n" +
                "    1.进入小程序\n\n" +
                "    2.客户认证");
        return wxMessage;
    }

    //访问小程序
    private WxMessage appletMessage(String toUserName,String fromUserName){
        WxMessage wxMessage=new WxMessage();
        wxMessage.setMsgType("image");
        wxMessage.setToUserName(fromUserName);
        wxMessage.setFromUserName(toUserName);
        wxMessage.setCreateTime(System.currentTimeMillis()/1000);

        ImageMsg imageMsg=new ImageMsg();
        imageMsg.setMediaId("iOS1fRfwc-zVYfRaNW_9tosp0x9AWxk_D8ehWD3uDJk");
        wxMessage.setImage(imageMsg);
        return wxMessage;
    }

    //客户认证
    private WxMessage authMessage(String toUserName,String fromUserName){
        WxMessage wxMessage=new WxMessage();
        wxMessage.setMsgType("text");
        wxMessage.setToUserName(fromUserName);
        wxMessage.setFromUserName(toUserName);
        wxMessage.setCreateTime(System.currentTimeMillis()/1000);
        Environment env = SpringUtil.getBean(Environment.class);
        String url = env.getProperty("wx.auth_url");
        wxMessage.setContent(String.format("用户认证：   %s",url));
        return wxMessage;
    }

    //默认什么都不回复
    private WxMessage defaultMessage(String toUserName,String fromUserName){
        WxMessage wxMessage=new WxMessage();
        wxMessage.setMsgType("text");
        wxMessage.setToUserName(fromUserName);
        wxMessage.setFromUserName(toUserName);
        wxMessage.setCreateTime(System.currentTimeMillis()/1000);
        wxMessage.setContent("如果您是我公司客户,请回复序号获取服务：\n\n\n" +
                "    1.进入小程序\n\n" +
                "    2.客户认证");
        return wxMessage;
    }



//    @GetMapping("/test")
//    public Result test(){
//        wxUtils.createMenu();
//        return Result.success();
//    }

    @GetMapping("/unit")
    public Result unit(){
        return Result.success("访问成功");
    }





}
