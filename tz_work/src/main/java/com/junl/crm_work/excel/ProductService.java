package com.junl.crm_work.excel;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.ExcelResult;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelProductEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_common.tools.excel.ExcelService;
import com.junl.crm_work.service.SelProductService;
import com.junl.crm_work.status.BusPrefix;
import com.junl.crm_work.util.CodeGenerate;
import lombok.extern.log4j.Log4j2;

import java.util.List;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/1511:42
 */
@Log4j2
public class ProductService implements ExcelService<SelProductEntity> {

    private CodeGenerate codeGenerate;
    private SysUserEntity sysUserEntity;
    private SelProductService selProductService;
    public ProductService(CodeGenerate codeGenerate, SysUserEntity sysUserEntity, SelProductService selProductService){
        this.codeGenerate=codeGenerate;
        this.sysUserEntity=sysUserEntity;
        this.selProductService=selProductService;
    }

    @Override
    public boolean save(List<SelProductEntity> list, ExcelResult excelResult) {
        list.forEach(x->{
            if (selProductService.count(new LambdaQueryWrapper<SelProductEntity>()
                    .and(e->e.eq(SelProductEntity::getProductName,x.getProductName()))
                    .eq(SelProductEntity::getIsDelete, Opposite.ZERO)
                    .eq(SelProductEntity::getCompanyId,x.getCompanyId())) >0) {
                log.warn("商品名称重复：{}",x.getProductName());
                excelResult.setError(excelResult.getError()+1);
                excelResult.getErrorMsg().add("第"+x.getRow()+"行商品名称重复");
                return;
            }
            excelResult.setSuccess(excelResult.getSuccess()+1);
            selProductService.save(x);
        });
        return true;
    }

    @Override
    public boolean saveBefore(List<SelProductEntity> list) {
        list.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setProductCode(codeGenerate.getCode(BusPrefix.PRODUCT));
            x.setCreateBy(sysUserEntity.getUserId());
            x.setUpdateBy(sysUserEntity.getUserId());
            x.setCompanyId(sysUserEntity.getCompanyId());
        });
        return true;
    }
}
