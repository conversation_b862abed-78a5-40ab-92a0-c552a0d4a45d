package com.junl.crm_work.excel;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.ExcelResult;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_common.tools.excel.ExcelService;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.crm_work.status.BusPrefix;
import com.junl.crm_work.util.CodeGenerate;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.util.List;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/1515:20
 */
@AllArgsConstructor
@Log4j2
public class CustomerService implements ExcelService<SelCustomerEntity> {

    private CodeGenerate codeGenerate;
    private SysUserEntity sysUserEntity;
    private SelCustomerService selCustomerService;

    @Override
    public boolean save(List<SelCustomerEntity> list, ExcelResult result) {
        list.forEach(x->{
            if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
                    .and(e->e.eq(SelCustomerEntity::getCustomerName,x.getCustomerName())
                            .eq(SelCustomerEntity::getIsDelete, Opposite.ZERO)
                            .eq(SelCustomerEntity::getCompanyId,x.getCompanyId())))>0) {

                result.setError(result.getError()+1);
                result.getErrorMsg().add("第"+x.getRow()+"行企业名称重复.");
                return;
            }


//            if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
//                    .and(e->e.eq(SelCustomerEntity::getDeputy,x.getDeputy())
//                            .eq(SelCustomerEntity::getIsDelete, Opposite.ZERO)))>0) {
//
//                result.setError(result.getError()+1);
//                result.getErrorMsg().add("第"+x.getRow()+"行管理人账号已存在.");
//                return;
//            }


            if (selCustomerService.count(new LambdaQueryWrapper<SelCustomerEntity>()
                    .and(e->e.eq(SelCustomerEntity::getDeputyPhone,x.getDeputyPhone())
                            .eq(SelCustomerEntity::getIsDelete,Opposite.ZERO)))>0) {

                result.setError(result.getError()+1);
                result.getErrorMsg().add("第"+x.getRow()+"行手机号码已存在.");
                return;
            }
            result.setSuccess(result.getSuccess()+1);
            selCustomerService.save(x);
        });
        return true;
    }

    @Override
    public boolean saveBefore(List<SelCustomerEntity> list) {
        list.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setCompanyId(sysUserEntity.getCompanyId());
            x.setCreateBy(sysUserEntity.getUserId());
            x.setUpdateBy(sysUserEntity.getUserId());
            x.setCustomerCode(codeGenerate.getCode(BusPrefix.CUSTOMER));
        });
        return true;
    }
}
