package com.junl.crm_work.expose;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_work.dao.SelCustomerDao;
import com.junl.crm_work.service.SelCustomerService;
import com.junl.system.CustomerService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR> Dai
 * @date 2024/3/20 17:18
 */

@Component
@Service
public class CustomerServiceImpl implements CustomerService {

    @Autowired
    private SelCustomerDao selCustomerDao;

    @Override
    public SelCustomerEntity getSelCustomer(String phone) {

        SelCustomerEntity selCustomerEntity = selCustomerDao.selectOne(
                new LambdaQueryWrapper<SelCustomerEntity>()
                        .eq(SelCustomerEntity::getDeputyPhone, phone)
                        .eq(SelCustomerEntity::getIsDelete, Opposite.ZERO)
        );
        return selCustomerEntity;
    }
}
