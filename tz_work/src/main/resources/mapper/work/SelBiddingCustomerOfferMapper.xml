<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelBiddingCustomerOfferDao">
    <resultMap id="SelBiddingCustomerOfferMap" type="com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity">
        <result property="id" column="id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="price" column="price"/>
        <result property="count" column="count"/>
        <result property="offerer" column="offerer"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="SelBiddingCustomerOffer">
            a.id,
            a.relevance_id,
            a.product_id,
            a.product_name,
            a.price,
            a.count,
            a.offerer,
            a.create_time    </sql>

    <select id="queryList" resultMap="SelBiddingCustomerOfferMap">
        SELECT a.* from sel_bidding_customer_offer a
        LEFT JOIN sel_bidding_customer b ON b.id = a.relevance_id
        LEFT JOIN sel_bidding_sequence c ON c.id = b.sequence
        <where>
            <if test="sequenceId!=null and sequenceId!=''">
                AND b.sequence=#{sequenceId}
            </if>
            <if test="biddingId!=null and biddingId!=''">
                AND c.bidding_id=#{biddingId}
            </if>
            <if test="sequence!=null">
                AND c.sequence =#{sequence}
            </if>
            <if test="sequences!=null">
                AND c.sequence in (${sequences})
            </if>
            <if test="productId!=null and productId!=''">
                AND a.product_id=#{productId}
            </if>
            <if test="relevancyId!=null and relevancyId!=''">
                AND a.relevancy_id=#{relevancyId}
            </if>
            <if test="customerId!=null and customerId!=''">
                AND a.offerer=#{customerId}
            </if>
        </where>
    </select>


    <select id="selectOfferOne" resultMap="SelBiddingCustomerOfferMap">
        SELECT a.* from sel_bidding_customer_offer a
        LEFT JOIN sel_bidding_customer b ON b.id = a.relevance_id
        <where>
            <if test="sequenceId!=null and sequenceId!=''">
                AND b.sequence=#{sequenceId}
            </if>
            <if test="productId!=null and productId!=''">
                AND a.product_id=#{productId}
            </if>
            <if test="customerId!=null and customerId!=''">
                AND a.offerer=#{customerId}
            </if>
        </where>
    </select>

</mapper>