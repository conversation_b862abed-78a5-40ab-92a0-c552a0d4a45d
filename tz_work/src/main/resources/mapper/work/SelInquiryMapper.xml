<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelInquiryDao">
    <resultMap id="SelInquiryMap" type="com.junl.crm_common.pojo.work.SelInquiryEntity">
                <result property="id" column="id"/>
                <result property="companyId" column="company_id"/>
                <result property="planId" column="plan_id"/>
                <result property="modeType" column="mode_type"/>
                <result property="inquiryStatus" column="inquiry_status"/>
                <result property="inquiryName" column="inquiry_name"/>
                <result property="inquiryStartDate" column="inquiry_start_date"/>
                <result property="inquiryEndTime" column="inquiry_end_time"/>
                <result property="remark" column="remark"/>
                <result property="isDelete" column="is_delete"/>
                <result property="createBy" column="create_by"/>
                <result property="createTime" column="create_time"/>
                <result property="updateBy" column="update_by"/>
                <result property="updateTime" column="update_time"/>
                <result property="isInquiry" column="is_inquiry"/>
                <result property="planCode" column="plan_code"/>
                <result property="planName" column="plan_name"/>
                <result property="stime" column="stime"/>
                <result property="createName" column="real_name"/>
                <result property="periodNum" column="period_num"/>
    </resultMap>

    <sql id="SelInquiry">
            a.id,
            a.company_id,
            a.plan_id,
            a.mode_type,
            a.inquiry_status,
            a.inquiry_name,
            a.inquiry_start_date,
            a.inquiry_end_time,
            a.remark,
            a.is_delete,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.period_num</sql>

    <select id="queryList" resultMap="SelInquiryMap">
        select <include refid="SelInquiry"/>,b.real_name from sel_inquiry a
        left join sys_user b on a.create_by=b.user_id
        <where>
            a.is_delete=0
            <if test="companyId!=null and companyId!=''">
               and a.company_id=#{companyId}
            </if>
            <if test="inquiryName!=null and inquiryName!=''">
               and  a.inquiry_name like concat('%',#{inquiryName},'%')
            </if>
            <if test="flag!=null">
                <choose>
                    <when test="flag == 0">
                        AND a.inquiry_status!=5
                    </when>
                    <otherwise>
                        AND a.inquiry_status=5
                    </otherwise>
                </choose>
            </if>
            <if test="userIds!=null">
               and a.create_by in (
                <foreach collection="userIds" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
        </where>
        order by a.create_time DESC
    </select>



    <select id="queryAppList" resultMap="SelInquiryMap">
        select a.*,
        if(TIMESTAMPDIFF(SECOND,now(),a.inquiry_end_time)>0,TIMESTAMPDIFF(SECOND,now(),a.inquiry_end_time),0)  as stime,
        b.is_inquiry,d.plan_code,d.plan_name from sel_inquiry a
        LEFT JOIN sel_inquiry_customer b ON b.inquiry_id = a.id
        LEFT JOIN sel_plan d ON d.id = a.plan_id
        <where>
            a.is_delete=0 AND a.inquiry_status in (2,3,4,5)
            <if test="customerId!=null and customerId!=''">
                AND b.customer_id=#{customerId}
            </if>
            <if test="inquiryName!=null and inquiryName!=''">
                AND a.inquiry_name like CONCAT('%',#{inquiryName},'%')
            </if>
            <if test="inquiryStartDate!=null">
                and date_format(a.inquiry_start_date,'%Y-%m-%d') &gt;= date_format(#{inquiryStartDate},'%Y-%m-%d')
            </if>
            <if test="inquiryEndTime!=null">
                and date_format(a.inquiry_end_time,'%Y-%m-%d') &lt;= date_format(#{inquiryEndTime},'%Y-%m-%d')
            </if>
            <if test="inquiryStatus!=null">
                <if test="inquiryStatus==2 or inquiryStatus==3" >
                AND a.inquiry_status = #{inquiryStatus}
                </if>
                <if test="inquiryStatus==4" >
                AND a.inquiry_status in (4,5)
                </if>
            </if>
            ORDER BY a.create_time desc
        </where>
    </select>


    <resultMap id="infoDetailsMap" type="com.junl.crm_common.pojo.work.SelInquiryEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="planId" column="plan_id"/>
        <result property="modeType" column="mode_type"/>
        <result property="inquiryStatus" column="inquiry_status"/>
        <result property="inquiryName" column="inquiry_name"/>
        <result property="inquiryStartDate" column="inquiry_start_date"/>
        <result property="inquiryEndTime" column="inquiry_end_time"/>
        <result property="remark" column="remark"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="customerList" ofType="com.junl.crm_common.pojo.work.SelInquiryCustomerEntity" javaType="java.util.List" resultMap="customerList"/>
    </resultMap>
    <resultMap id="customerList" type="com.junl.crm_common.pojo.work.SelInquiryCustomerEntity">
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <collection property="offerDetails" javaType="java.util.List" resultMap="offerDetails" ofType="com.junl.crm_common.pojo.work.SelInquiryOfferEntity"/>
    </resultMap>
    <resultMap id="offerDetails" type="com.junl.crm_common.pojo.work.SelInquiryOfferEntity">
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="count" column="count"/>
        <result property="price" column="price"/>
        <result property="offerer" column="offerer"/>
        <result property="createTime" column="time"/>
    </resultMap>
    
    <select id="infoInquiry" resultMap="infoDetailsMap">
        select <include refid="SelInquiry"/>,b.customer_id,c.customer_name,d.price,d.product_id,d.product_name,d.count,d.offerer,d.create_time as `time` from sel_inquiry a
        left join sel_inquiry_customer b on a.id=b.inquiry_id
        left join sel_customer c on b.customer_id=c.id
        left join sel_inquiry_offer d on b.id=d.relevancy_id
        where a.id=#{id}
    </select>
    <select id="queryCustomerOffer" resultMap="customerList">
        select b.customer_id,c.customer_name,d.price,d.product_id,d.product_name,d.count,d.offerer,d.create_time as `time`  from sel_inquiry_customer b
        left join sel_customer c on b.customer_id=c.id
        left join sel_inquiry_offer d on b.id=d.relevancy_id
        where b.inquiry_id=#{id}
    </select>
    <select id="getOfferList" resultType="com.junl.crm_common.pojo.work.SelInquiryOfferEntity">
        select a.product_name,a.price,a.count,a.create_time,b.customer_name as offerName  from sel_inquiry_offer a
        left join sel_customer b on a.offerer=b.id
        where a.product_id=#{productId}
        and a.relevancy_id in (
        <foreach collection="relevancy" separator="," item="id">
            #{id}
        </foreach>
        )
    </select>
    <select id="getInquiryCustomer" resultType="com.junl.crm_common.pojo.work.SelInquiryCustomerEntity">
        select a.customer_id,a.id, b.customer_name from sel_inquiry_customer a
        left join sel_customer b on a.customer_id=b.id
        where a.inquiry_id = #{id}
    </select>
    <select id="queryInquiryCount" resultType="java.lang.Integer">
        select count(*) from sel_inquiry a
        left join sel_inquiry_customer b
        on a.id =b.inquiry_id
        where a.plan_id in (
            <foreach collection="ids" separator="," item="id">
                #{id}
            </foreach>
            )
        and b.customer_id=#{customerId}
    </select>
    <select id="queryInquiryCustomerIds" resultType="java.lang.String">
        select b.id from sel_inquiry a
        left join sel_inquiry_customer b
        on a.id =b.inquiry_id
        where a.plan_id in (
        <foreach collection="ids" separator="," item="id">
            #{id}
        </foreach>
        )
        and b.customer_id=#{customerId}
    </select>
    <select id="queryInquiryCustomerOfferNum" resultType="java.lang.Integer">
        select count(*) from sel_inquiry_offer a
        where a.relevancy_id in (
            <foreach collection="ids" separator="," item="id">
                #{id}
            </foreach>
            )
            and a.product_id=#{productId}
    </select>
</mapper>