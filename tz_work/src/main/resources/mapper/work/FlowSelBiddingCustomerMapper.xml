<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.FlowSelBiddingCustomerDao">
    <resultMap id="FlowSelBiddingCustomerMap" type="com.junl.crm_common.pojo.work.FlowSelBiddingCustomerEntity">
                <result property="id" column="id"/>
                <result property="sequence" column="sequence"/>
                <result property="customerId" column="customer_id"/>
                <result property="detailAddress" column="detail_address"/>
                <result property="remark" column="remark"/>
                <result property="isBidding" column="is_bidding"/>
                <result property="isSuccess" column="is_success"/>
    </resultMap>

    <sql id="FlowSelBiddingCustomer">
            a.id,
            a.sequence,
            a.customer_id,
            a.detail_address,
            a.remark,
            a.is_bidding,
            a.is_success    </sql>

    <select id="queryList" resultMap="FlowSelBiddingCustomerMap">
        select <include refid="FlowSelBiddingCustomer"/> from flow_sel_bidding_customer a
        <where>
        </where>
    </select>
</mapper>