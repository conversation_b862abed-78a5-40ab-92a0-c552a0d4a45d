<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SmsErrorDao">

    <select id="getList" resultType="com.junl.crm_common.pojo.work.SmsRecordEntity">
        select id,phone,des,stage,create_time from sms_record
        order by create_time desc
    </select>
</mapper>