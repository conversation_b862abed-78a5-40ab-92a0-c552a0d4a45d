<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.work.dao.WxCustomerDao">
    <resultMap id="WxCustomerMap" type="com.junl.crm_common.pojo.work.WxCustomerEntity">
                <result property="id" column="id"/>
                <result property="unionId" column="union_id"/>
                <result property="openId" column="open_id"/>
                <result property="nickName" column="nick_name"/>
                <result property="headImg" column="head_img"/>
                <result property="sex" column="sex"/>
                <result property="country" column="country"/>
                <result property="province" column="province"/>
                <result property="city" column="city"/>
                <result property="realName" column="real_name"/>
                <result property="phone" column="phone"/>
                <result property="registerMode" column="register_mode"/>
                <result property="createBy" column="create_by"/>
                <result property="createTime" column="create_time"/>
                <result property="updateBy" column="update_by"/>
                <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="WxCustomer">
            a.id,
            a.union_id,
            a.open_id,
            a.nick_name,
            a.head_img,
            a.sex,
            a.country,
            a.province,
            a.city,
            a.real_name,
            a.phone,
            a.register_mode,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time    </sql>

    <select id="queryList" resultMap="WxCustomerMap">
        select <include refid="WxCustomer"/> from wx_customer a
        <where>
                        <if test="province!=null and province!=''">
                            AND a.province=#{province}
                        </if>
                        <if test="city!=null and city!=''">
                            AND a.city=#{city}
                        </if>
                        <if test="realName!=null and realName!=''">
                            AND a.real_name=#{realName}
                        </if>
                        <if test="phone!=null and phone!=''">
                            AND a.phone=#{phone}
                        </if>
        </where>
    </select>
</mapper>