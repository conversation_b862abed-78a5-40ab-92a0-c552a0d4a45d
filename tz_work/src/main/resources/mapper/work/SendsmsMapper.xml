<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SendsmsDao">
    <resultMap id="SendsmsMap" type="com.junl.crm_common.pojo.work.SendsmsEntity">
                <result property="smsindex" column="smsIndex"/>
                <result property="phonenumber" column="phoneNumber"/>
                <result property="smscontent" column="smsContent"/>
                <result property="smstime" column="smsTime"/>
                <result property="smsuser" column="smsUser"/>
                <result property="statime" column="staTime"/>
                <result property="endtime" column="endTime"/>
                <result property="status" column="status"/>
                <result property="extno" column="extno"/>
                <result property="resultcode" column="resultCode"/>
                <result property="resultdesc" column="resultDesc"/>
                <result property="faillist" column="failList"/>
    </resultMap>

    <sql id="Sendsms">
            a.smsIndex,
            a.phoneNumber,
            a.smsContent,
            a.smsTime,
            a.smsUser,
            a.staTime,
            a.endTime,
            a.status,
            a.extno,
            a.resultCode,
            a.resultDesc,
            a.failList    </sql>

    <select id="queryList" resultMap="SendsmsMap">
        select <include refid="Sendsms"/> from SendSms a
        <where>
                        <if test="phonenumber!=null and phonenumber!=''">
                            AND a.phoneNumber=#{phonenumber}
                        </if>
        </where>
    </select>
</mapper>