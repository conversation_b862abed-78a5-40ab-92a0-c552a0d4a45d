<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelBiddingSuccessDao">
    <resultMap id="SelBiddingSuccessMap" type="com.junl.crm_common.pojo.work.SelBiddingSuccessEntity">
        <result property="id" column="id"/>
        <result property="offerId" column="offer_id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="sequenceId" column="sequence_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="biddingCode" column="bidding_code"/>
        <result property="modeType" column="mode_type"/>
        <result property="periodNum" column="period_num"/>
        <result property="roundNum" column="round_num"/>
        <result property="planName" column="plan_name"/>
        <result property="sequence" column="sequence"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndTime" column="plan_end_time"/>
    </resultMap>

    <sql id="SelBiddingSuccess">
            a.id,
            a.bidding_id,
            a.product_id,
            a.offer_id,
            a.bidding_id,
            a.sequence_id,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by    </sql>

    <select id="queryList" resultMap="SelBiddingSuccessMap">
        select <include refid="SelBiddingSuccess"/> from sel_bidding_success a
        <where>
        </where>
    </select>



    <select id="queryAppList" resultMap="SelBiddingSuccessMap">
        SELECT  a.id, a.offer_id,a.bidding_id,a.sequence_id, a.create_time,a.update_time,a.create_by,a.update_by,
        b.sequence,b.plan_start_date,b.plan_end_time,
        c.bidding_code,c.mode_type,c.period_num,c.round_num,c.plan_name
        from sel_bidding_success a
        LEFT JOIN sel_bidding_sequence b ON b.id= a.sequence_id
        LEFT JOIN sel_bidding c ON c.id= a.bidding_id
        <where>
            c.is_delete=0 AND c.bidding_status=8
        </where>
        <if test="sequenceId!=null and sequenceId!=''">
            AND a.sequence_id=#{sequenceId}
        </if>
        <if test="offerId!=null and offerId!=''">
            AND a.offer_id=#{offerId}
        </if>
        ORDER BY a.create_time desc
    </select>

    <resultMap id="SelBiddingSuccessProductMap" type="com.junl.crm_common.pojo.work.SelBiddingSuccessProductEntity">
        <result property="id" column="id"/>
        <result property="offerId" column="offer_id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="sequenceId" column="sequence_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="biddingCode" column="bidding_code"/>
        <result property="modeType" column="mode_type"/>
        <result property="periodNum" column="period_num"/>
        <result property="roundNum" column="round_num"/>
        <result property="planName" column="plan_name"/>
        <result property="sequence" column="sequence"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndTime" column="plan_end_time"/>
    </resultMap>

    <select id="getAppList" resultMap="SelBiddingSuccessProductMap">
        SELECT a.*,
        b.offer_id,b.bidding_id,b.sequence_id,
        c.sequence,c.plan_start_date,c.plan_end_time,
        d.bidding_code,d.mode_type,d.period_num,d.round_num,d.plan_name
        from sel_bidding_success_product a
        LEFT JOIN  sel_bidding_success b ON b.id = a.success_id
        LEFT JOIN sel_bidding_sequence c ON c.id= b.sequence_id
        LEFT JOIN sel_bidding d ON d.id= c.bidding_id
        <where>
            d.is_delete=0
        </where>
        <if test="sequenceId!=null and sequenceId!=''">
            AND b.sequence_id=#{sequenceId}
        </if>
        <if test="offerId!=null and offerId!=''">
            AND b.offer_id=#{offerId}
        </if>
        ORDER BY a.create_time desc
    </select>
    <select id="querySuccessCount" resultType="com.junl.crm_common.pojo.work.SelBiddingSuccessProductEntity">
        select b.count from sel_bidding_success a
        left join  sel_bidding_success_product  b
        on a.id= b.success_id
        where a. bidding_id in (
             <foreach collection="ids" separator="," item="id">
                 #{id}
             </foreach>
            )
        <if test="customerId!=null and customerId!=''">
            and a.offer_id=#{customerId}
        </if>
        and b.product_id=#{productId}

    </select>

</mapper>