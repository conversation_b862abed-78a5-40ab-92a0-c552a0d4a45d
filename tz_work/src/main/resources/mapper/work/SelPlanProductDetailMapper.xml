<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelPlanProductDetailDao">
    <resultMap id="SelPlanProductDetailMap" type="com.junl.crm_common.pojo.work.SelPlanProductDetailEntity">
                <result property="id" column="id"/>
                <result property="companyId" column="company_id"/>
                <result property="planId" column="plan_id"/>
                <result property="modeType" column="mode_type"/>
                <result property="baseFee" column="base_fee"/>
                <result property="contractQuantity" column="contract_quantity"/>
                <result property="stockQuantity" column="stock_quantity"/>
                <result property="dailyQuantity" column="daily_quantity"/>
                <result property="saleQuantity" column="sale_quantity"/>
                <result property="remark" column="remark"/>
                <result property="price" column="price"/>
                <result property="count" column="count"/>
    </resultMap>

    <sql id="SelPlanProductDetail">
            a.id,
            a.company_id,
            a.plan_id,
            a.mode_type,
            a.base_fee,
            a.contract_quantity,
            a.stock_quantity,
            a.daily_quantity,
            a.sale_quantity,
            a.remark   </sql>

    <select id="queryList" resultMap="SelPlanProductDetailMap">
        select <include refid="SelPlanProductDetail"/> from sel_plan_product_detail a
        <where>
        </where>
    </select>


    <select id="queryAppList" resultMap="SelPlanProductDetailMap">
        SELECT a.*,
        IFNULL((SELECT f.price from sel_inquiry_offer f WHERE f.relevancy_id =d.id
        AND f.product_id = a.product_id AND f.offerer = d.customer_id),0) as price,
        IFNULL((SELECT IFNULL(f.count,0) from sel_inquiry_offer f WHERE f.relevancy_id =d.id
        AND f.product_id = a.product_id AND f.offerer = d.customer_id),0) as count
        from sel_plan_product_detail a
        LEFT JOIN sel_plan b ON b.id= a.plan_id
        LEFT JOIN sel_inquiry c ON c.plan_id = b.id
        LEFT JOIN sel_inquiry_customer d ON d.inquiry_id = c.id
        <where>
            b.is_delete=0
            <if test="planId!=null and planId!=''">
                AND b.id=#{planId}
            </if>
            <if test="customerId!=null and customerId!=''">
                AND d.customer_id=#{customerId}
            </if>
        </where>
    </select>
</mapper>