<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelModeDao">
    <resultMap id="SelModeMap" type="com.junl.crm_common.pojo.work.SelModeEntity">
                <result property="id" column="id"/>
                <result property="companyId" column="company_id"/>
                <result property="modeCode" column="mode_code"/>
                <result property="modeType" column="mode_type"/>
                <result property="modeName" column="mode_name"/>
                <result property="isEnable" column="is_enable"/>
                <result property="remark" column="remark"/>
                <result property="createTime" column="created_time"/>
                <result property="createBy" column="created_by"/>
    </resultMap>

    <sql id="SelMode">
            a.id,
            a.company_id,
            a.mode_code,
            a.mode_type,
            a.mode_name,
            a.is_enable,
            a.remark,
            a.create_time,
            a.create_by    </sql>

    <select id="queryList" resultMap="SelModeMap">
        select <include refid="SelMode"/> from sel_mode a
        <where>
        </where>
    </select>
</mapper>