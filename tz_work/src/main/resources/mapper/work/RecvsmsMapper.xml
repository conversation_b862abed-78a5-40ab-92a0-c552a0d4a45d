<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.RecvsmsDao">
    <resultMap id="RecvsmsMap" type="com.junl.crm_common.pojo.work.RecvsmsEntity">
                <result property="smsindex" column="smsIndex"/>
                <result property="sendnumber" column="sendNumber"/>
                <result property="smscontent" column="smsContent"/>
                <result property="smstime" column="smsTime"/>
                <result property="callmdn" column="callMdn"/>
    </resultMap>

    <sql id="Recvsms">
            a.smsIndex,
            a.sendNumber,
            a.smsContent,
            a.smsTime,
            a.callMdn    </sql>

    <select id="queryList" resultMap="RecvsmsMap">
        select <include refid="Recvsms"/> from RecvSms a
        <where>
                        <if test="sendnumber!=null and sendnumber!=''">
                            AND a.sendNumber=#{sendnumber}
                        </if>
                        <if test="callmdn!=null and callmdn!=''">
                            AND a.callMdn=#{callmdn}
                        </if>
        </where>
    </select>
</mapper>