<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.FlowSelPlanProductDetailDao">
    <resultMap id="FlowSelPlanProductDetailMap" type="com.junl.crm_common.pojo.work.FlowSelPlanProductDetailEntity">
                <result property="id" column="id"/>
                <result property="companyId" column="company_id"/>
                <result property="productName" column="product_name"/>
                <result property="productId" column="product_id"/>
                <result property="planId" column="plan_id"/>
                <result property="modeType" column="mode_type"/>
                <result property="baseFee" column="base_fee"/>
                <result property="contractQuantity" column="contract_quantity"/>
                <result property="stockQuantity" column="stock_quantity"/>
                <result property="dailyQuantity" column="daily_quantity"/>
                <result property="saleQuantity" column="sale_quantity"/>
                <result property="remark" column="remark"/>
    </resultMap>

    <sql id="FlowSelPlanProductDetail">
            a.id,
            a.company_id,
            a.product_name,
            a.product_id,
            a.plan_id,
            a.mode_type,
            a.base_fee,
            a.contract_quantity,
            a.stock_quantity,
            a.daily_quantity,
            a.sale_quantity,
            a.remark    </sql>

    <select id="queryList" resultMap="FlowSelPlanProductDetailMap">
        select <include refid="FlowSelPlanProductDetail"/> from flow_sel_plan_product_detail a
        <where>
        </where>
    </select>
</mapper>