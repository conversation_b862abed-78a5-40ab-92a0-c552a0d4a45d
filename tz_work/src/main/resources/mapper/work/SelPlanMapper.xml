<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelPlanDao">
    <resultMap id="SelPlanMap" type="com.junl.crm_common.pojo.work.SelPlanEntity">
                <result property="id" column="id"/>
                <result property="companyId" column="company_id"/>
                <result property="planCode" column="plan_code"/>
                <result property="modeType" column="mode_type"/>
                <result property="planStatus" column="plan_status"/>
                <result property="planName" column="plan_name"/>
                <result property="planStartDate" column="plan_start_date"/>
                <result property="planEndTime" column="plan_end_time"/>
                <result property="remark" column="remark"/>
                <result property="isDelete" column="is_delete"/>
                <result property="createBy" column="create_by"/>
                <result property="createTime" column="create_time"/>
                <result property="updateBy" column="update_by"/>
                <result property="updateTime" column="update_time"/>
                <result property="createName" column="real_name"/>
                <result property="periodNum" column="period_num"/>
    </resultMap>

    <sql id="SelPlan">
            a.id,
            a.company_id,
            a.plan_code,
            a.mode_type,
            a.plan_status,
            a.plan_name,
            a.plan_start_date,
            a.plan_end_time,
            a.remark,
            a.period_num,
            a.is_delete,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time    </sql>

    <select id="queryList" resultMap="SelPlanMap">
        select <include refid="SelPlan"/>,b.real_name from sel_plan a
        left join sys_user b on a.create_by=b.user_id
        <where>
            a.is_delete=0
            <if test="companyId!=null and companyId!=''">
                And a.company_id=#{companyId}
            </if>
            <if test="planName!=null and planName!=''">
                AND a.plan_name like concat('%',#{planName},'%')
            </if>
            <if test="flag!=null">
                <choose>
                    <when test="flag == 0">
                        AND ((a.mode_type=1 and a.plan_status!=3) OR (a.mode_type !=1 and a.plan_status!=5))
                    </when>
                    <otherwise>
                        AND ((a.mode_type=1 and a.plan_status=3) OR (a.mode_type !=1 and a.plan_status=5))
                    </otherwise>
                </choose>
            </if>
            <if test="userIds!=null">
                and a.create_by in (
                <foreach collection="userIds" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
        </where>
        order by a.create_time DESC
    </select>


    <resultMap id="flowPlan" type="com.junl.crm_common.pojo.work.FlowSelPlanEntity">
        <result property="planCode" column="plan_code"/>
        <result property="planId" column="plan_id"/>
        <result property="modeType" column="mode_type"/>
        <result property="planName" column="plan_name"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="remark" column="remark"/>
        <result property="periodNum" column="period_num"/>
        <collection property="productList" ofType="com.junl.crm_common.pojo.work.FlowSelPlanProductDetailEntity">
            <result property="productId" column="product_id"/>
            <result property="productName" column="product_name"/>
            <result property="contractQuantity" column="contract_quantity"/>
            <result property="stockQuantity" column="stock_quantity"/>
            <result property="dailyQuantity" column="daily_quantity"/>
            <result property="saleQuantity" column="sale_quantity"/>
            <result property="remark" column="remarkb"/>
        </collection>
    </resultMap>

    <select id="getPlanInfo" resultMap="flowPlan">
        select a.plan_id, a.plan_code,a.mode_type,a.plan_name,a.plan_start_date,a.plan_end_time,a.remark,a.period_num,b.product_id,
        b.product_name,b.contract_quantity,b.stock_quantity,b.daily_quantity,b.sale_quantity,
        b.remark as remarkb from flow_sel_plan a left join flow_sel_plan_product_detail b
        on a.id=b.plan_id
        where a.underway_id=#{id}
    </select>
    <select id="getIds" resultType="java.lang.String">
        select a.id from sel_plan a
        left join sel_plan_product_detail b
        on a.id = b.plan_id
        where a.mode_type=#{modeType}
        and a.company_id=#{companyId}
        and DATE_FORMAT(a.create_time,'%Y%m%d') >= DATE_FORMAT(#{startDate},'%Y%m%d')
        and DATE_FORMAT(a.create_time,'%Y%m%d') &lt;= DATE_FORMAT(#{endDate},'%Y%m%d')
        and a.is_delete=0 and b.product_id=#{productId}
    </select>
</mapper>