<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.FlowSelBiddingProductDao">
    <resultMap id="FlowSelBiddingProductMap" type="com.junl.crm_common.pojo.work.FlowSelBiddingProductEntity">
                <result property="id" column="id"/>
                <result property="sequenceId" column="sequence_id"/>
                <result property="productName" column="product_name"/>
                <result property="productId" column="product_id"/>
                <result property="modeType" column="mode_type"/>
                <result property="baseFee" column="base_fee"/>
                <result property="contractQuantity" column="contract_quantity"/>
                <result property="stockQuantity" column="stock_quantity"/>
                <result property="dailyQuantity" column="daily_quantity"/>
                <result property="saleQuantity" column="sale_quantity"/>
                <result property="remark" column="remark"/>
    </resultMap>

    <sql id="FlowSelBiddingProduct">
            a.id,
            a.sequence_id,
            a.product_name,
            a.product_id,
            a.mode_type,
            a.base_fee,
            a.contract_quantity,
            a.stock_quantity,
            a.daily_quantity,
            a.sale_quantity,
            a.remark    </sql>

    <select id="queryList" resultMap="FlowSelBiddingProductMap">
        select <include refid="FlowSelBiddingProduct"/> from flow_sel_bidding_product a
        <where>
        </where>
    </select>
</mapper>