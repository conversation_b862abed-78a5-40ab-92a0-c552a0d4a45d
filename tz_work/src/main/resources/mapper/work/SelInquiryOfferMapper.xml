<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelInquiryOfferDao">
    <resultMap id="SelInquiryOfferMap" type="com.junl.crm_common.pojo.work.SelInquiryOfferEntity">
        <result property="id" column="id"/>
        <result property="relevancyId" column="relevancy_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="price" column="price"/>
        <result property="count" column="count"/>
        <result property="createTime" column="create_time"/>
        <result property="offerer" column="offerer"/>
    </resultMap>

    <sql id="SelInquiryOffer">
            a.id,
            a.relevancy_id,
            a.product_id,
            a.product_name,
            a.price,
            a.count,
            a.create_time,
            a.offerer    </sql>

    <select id="queryList" resultMap="SelInquiryOfferMap">
        SELECT a.* from sel_inquiry_offer a
        LEFT JOIN sel_inquiry_customer b ON b.id = a.relevancy_id
        <where>
            1=1
            <if test="inquiryId!=null and inquiryId!=''">
                AND a.inquiry_id=#{inquiryId}
            </if>
            <if test="relevancyId!=null and relevancyId!=''">
                AND a.relevancy_id=#{relevancyId}
            </if>
            <if test="customerId!=null and customerId!=''">
                AND a.offerer=#{customerId}
            </if>
        </where>
    </select>
</mapper>