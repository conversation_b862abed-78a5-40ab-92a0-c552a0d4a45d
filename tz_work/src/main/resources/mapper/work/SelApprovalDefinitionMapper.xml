<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelApprovalDefinitionDao">

    <sql id="approvalDefinition">
        a.id,
        a.company_id as companyId,
        a.bus_code as busCode,
        a.create_time as createTime,
        a.update_by as updateBy
    </sql>

    <select id="getList" resultType="com.junl.crm_common.pojo.work.SelApprovalDefinitionEntity">
        select <include refid="approvalDefinition"/>,b.real_name as updateName from sel_approval_definition a
        left join sys_user b on a.update_by=b.user_id
        where a.company_id=#{companyId}
    </select>


    <resultMap id="auditRecord" type="com.junl.crm_common.pojo.work.SelApprovalUnderwayEntity">
        <id property="id" column="id"/>
        <result property="sort" column="sort"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="busId" column="bus_id"/>
        <result property="createBy" column="create_by"/>
        <result property="busType" column="bus_type"/>
        <result property="createName" column="create_name"/>
    </resultMap>

    <select id="getAuditRecord" resultType="com.junl.crm_common.pojo.work.SelApprovalUnderwayEntity">
        select a.id,a.sort,a.is_delete,a.create_time,a.bus_id,a.create_by,a.bus_type,b.real_name as  create_name
        from sel_approval_underway  a left join sys_user b on a.create_by =b.user_id
        where a.bus_id=#{id} and a.is_repeal=0 and a.bus_type in (
            <foreach collection="busType" separator="," item="bus">
                #{bus}
            </foreach>
            )
        order by a.sort DESC;
    </select>
</mapper>