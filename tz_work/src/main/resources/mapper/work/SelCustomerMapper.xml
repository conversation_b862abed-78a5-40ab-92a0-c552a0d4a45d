<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelCustomerDao">
    <resultMap id="SelCustomerMap" type="com.junl.crm_common.pojo.work.SelCustomerEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="customerCode" column="customer_code"/>
        <result property="customerName" column="customer_name"/>
        <result property="businessLicense" column="business_license"/>
        <result property="manageProduct" column="manage_product"/>
        <result property="productFile" column="product_file"/>
        <result property="deputy" column="deputy"/>
        <result property="deputyCardNo" column="deputy_card_no"/>
        <result property="deputyPhone" column="deputy_phone"/>
        <result property="registerFee" column="register_fee"/>
        <result property="telephone" column="telephone"/>
        <result property="headImg" column="head_img"/>
        <result property="authorizationFile" column="authorization_file"/>
        <result property="isAuth" column="is_auth"/>
        <result property="customerStatus" column="customer_status"/>
        <result property="levelType" column="level_type"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="region" column="region"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="postalCode" column="postal_code"/>
        <result property="remark" column="remark"/>
        <result property="isEnable" column="is_enable"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isWxAuth" column="is_wx_auth"/>
    </resultMap>

    <sql id="SelCustomer">
        a.id,
            a.company_id,
            a.customer_code,
            a.customer_name,
            a.business_license,
            a.manage_product,
            a.product_file,
            a.deputy,
            a.deputy_card_no,
            a.deputy_phone,
            a.register_fee,
            a.telephone,
            a.head_img,
            a.authorization_file,
            a.is_auth,
            a.customer_status,
            a.level_type,
            a.province,
            a.city,
            a.region,
            a.detail_address,
            a.postal_code,
            a.remark,
            a.is_enable,
            a.is_delete,
            a.create_by,
            a.is_wx_auth,
            a.create_time,
            a.update_by,
            a.update_time    </sql>

    <select id="queryList" resultMap="SelCustomerMap">
        select <include refid="SelCustomer"/> from sel_customer a
        <where>
            a.is_delete=0
            <if test="customerName!=null and customerName!=''">
                AND a.customer_name like CONCAT('%',#{customerName},'%')
            </if>
            <if test="manageProduct!=null and manageProduct!=''">
                AND a.manage_product=#{manageProduct}
            </if>
            <if test="deputyCardNo!=null and deputyCardNo!=''">
                AND a.deputy_card_no like CONCAT(#{deputyCardNo},'%')
            </if>
            <if test="deputyPhone!=null and deputyPhone!=''">
                AND a.deputy_phone like CONCAT(#{deputyPhone},'%')
            </if>
            <if test="isAuth!=null and isAuth!=''">
                AND a.is_auth=#{isAuth}
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
        </where>

    order by a.create_time desc
    </select>


    <update id="updateAppCustomerById">
        update sel_customer set
        <if test="customerName!=null and customerName!=''">
            customer_name=#{customerName},
        </if>
        <if test="businessLicense!=null and businessLicense!=''">
            business_license=#{businessLicense},
        </if>
        <if test="manageProduct!=null">
            manage_product=#{manageProduct},
        </if>
        <if test="productFile!=null and productFile!=''">
            product_file=#{productFile},
        </if>

        <if test="deputy!=null and deputy!=''">
            deputy=#{deputy},
        </if>
        <if test="deputyCardNo!=null and deputyCardNo!=''">
            deputy_card_no=#{deputyCardNo},
        </if>
        <if test="deputyPhone!=null and deputyPhone!=''">
            deputy_phone=#{deputyPhone},
        </if>

        <if test="registerFee!=null">
            register_fee=#{registerFee},
        </if>

        <if test="telephone!=null and telephone!=''">
            telephone=#{telephone},
        </if>
        <if test="headImg!=null and headImg!=''">
            head_img=#{headImg},
        </if>
        <if test="authorizationFile!=null and authorizationFile!=''">
            authorization_file=#{authorizationFile},
        </if>
        <if test="levelType!=null">
            level_type=#{levelType},
        </if>
        <if test="province!=null and province!=''">
            province=#{province},
        </if>
        <if test="city!=null and city!=''">
            city=#{city},
        </if>
        <if test="region!=null and region!=''">
            region=#{region},
        </if>
        <if test="detailAddress!=null and detailAddress!=''">
            detail_address=#{detailAddress},
        </if>
        <if test="postalCode!=null and postalCode!=''">
            postal_code=#{postalCode},
        </if>
        <if test="remark!=null and remark!=''">
            remark=#{remark},
        </if>
        is_auth=1
        where id = #{id}
    </update>
    <update id="updateWxAuth">
        update sel_customer set open_id =null,is_wx_auth=0
        where id=#{id}
    </update>

</mapper>