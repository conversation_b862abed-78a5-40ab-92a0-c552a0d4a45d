<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelBiddingSequenceDao">
    <resultMap id="SelBiddingSequenceMap" type="com.junl.crm_common.pojo.work.SelBiddingSequenceEntity">
        <result property="id" column="id"/>
        <result property="sequence" column="sequence"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="biddingCode" column="bidding_code"/>
        <result property="modeType" column="mode_type"/>
        <result property="periodNum" column="period_num"/>
        <result property="roundNum" column="round_num"/>
        <result property="biddingStatus" column="bidding_status"/>
        <result property="planName" column="plan_name"/>
        <result property="isBidding" column="is_bidding"/>
        <result property="isSuccess" column="is_success"/>
        <result property="stime" column="stime"/>
        <result property="relevanceId" column="relevance_id"/>
    </resultMap>

    <sql id="SelBiddingSequence">
            a.id,
            a.sequence,
            a.bidding_id,
            a.plan_start_date,
            a.plan_end_time    </sql>

    <select id="queryList" resultMap="SelBiddingSequenceMap">
        SELECT a.*,b.id as relevance_id,
        if(TIMESTAMPDIFF(SECOND,now(),a.plan_end_time)>0,TIMESTAMPDIFF(SECOND,now(),a.plan_end_time),0)  as stime,
        c.bidding_code,c.mode_type,c.period_num,c.round_num,c.plan_name,b.is_bidding,b.is_success from sel_bidding_sequence a
        LEFT JOIN sel_bidding_customer b ON b.sequence = a.id
        LEFT JOIN sel_bidding  c ON c.id = a.bidding_id
        <where>
            c.is_delete=0 AND a.bidding_status &gt;=3
            <if test="customerId!=null and customerId!=''">
                AND b.customer_id=#{customerId}
            </if>
            <if test="biddingId!=null and biddingId!=''">
                AND a.bidding_id=#{biddingId}
            </if>
            <if test="planName!=null and planName!=''">
                AND c.plan_name like CONCAT('%',#{planName},'%')
            </if>
            <if test="planStartDate!=null">
                and date_format(a.plan_start_date,'%Y-%m-%d') &gt;= date_format(#{planStartDate},'%Y-%m-%d')
            </if>
            <if test="planEndTime!=null">
                and date_format(a.plan_end_time,'%Y-%m-%d') &lt;= date_format(#{planEndTime},'%Y-%m-%d')
            </if>
            <if test="biddingStatus!=null">
                <if test="biddingStatus==3 or biddingStatus==4" >
                    AND a.bidding_status=#{biddingStatus}
                </if>
                <if test="biddingStatus==5">
                    AND a.bidding_status &gt;= 5
                </if>
            </if>
            ORDER BY a.plan_start_date desc
        </where>
    </select>

    <select id="queryAppList" resultMap="SelBiddingSequenceMap">
        SELECT a.id,a.bidding_code,a.mode_type,a.period_num,a.round_num,a.plan_name
        from sel_bidding a
        LEFT JOIN sel_bidding_sequence b ON b.bidding_id =a.id
        LEFT JOIN sel_bidding_customer c ON c.sequence = b.id
        <where>
            a.is_delete=0 AND a.bidding_status &gt;=3
            <if test="customerId!=null and customerId!=''">
                AND c.customer_id=#{customerId}
            </if>
            <if test="biddingId!=null and biddingId!=''">
                AND a.id=#{biddingId}
            </if>
            <if test="planName!=null and planName!=''">
                AND a.plan_name like CONCAT('%',#{planName},'%')
            </if>
            <if test="planStartDate!=null">
                and date_format(a.plan_start_date,'%Y-%m-%d') &gt;= date_format(#{planStartDate},'%Y-%m-%d')
            </if>
            <if test="planEndTime!=null">
                and date_format(a.plan_end_time,'%Y-%m-%d') &lt;= date_format(#{planEndTime},'%Y-%m-%d')
            </if>
            <if test="biddingStatus!=null">
                <if test="biddingStatus==3 or biddingStatus==4" >
                    AND a.bidding_status=#{biddingStatus}
                </if>
                <if test="biddingStatus==5">
                    AND a.bidding_status &gt;= 5
                </if>
            </if>
            GROUP BY a.id,a.plan_name
            ORDER BY a.create_time desc
        </where>
    </select>


    <select id="queryAppOne" resultMap="SelBiddingSequenceMap">
        SELECT a.*, if(TIMESTAMPDIFF(SECOND,now(),a.plan_end_time)>0,TIMESTAMPDIFF(SECOND,now(),a.plan_end_time),0) as stime, c.bidding_code,c.mode_type,c.period_num,c.round_num,c.plan_name,b.is_bidding,b.is_success from sel_bidding_sequence a
        LEFT JOIN sel_bidding_customer b ON b.sequence = a.id
        LEFT JOIN sel_bidding c ON c.id = a.bidding_id
        <where>
            c.is_delete=0 AND a.bidding_status &gt;= 3
            <if test="customerId!=null and customerId!=''">
                AND b.customer_id=#{customerId}
            </if>
            <if test="biddingId!=null and biddingId!=''">
                AND a.bidding_id=#{biddingId}
            </if>
            <if test="biddingStatus!=null">
                <if test="biddingStatus==3 or biddingStatus==4" >
                    AND a.bidding_status=#{biddingStatus}
                </if>
                <if test="biddingStatus==5">
                    AND a.bidding_status &gt;= 5
                </if>
            </if>
            ORDER BY a.sequence desc LIMIT 1
        </where>
    </select>



</mapper>