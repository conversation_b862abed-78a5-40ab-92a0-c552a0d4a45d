<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.FlowSelBiddingDao">
    <resultMap id="FlowSelBiddingMap" type="com.junl.crm_common.pojo.work.FlowSelBiddingEntity">
                <result property="id" column="id"/>
                <result property="roundId" column="round_id"/>
                <result property="underwayId" column="underway_id"/>
                <result property="companyId" column="company_id"/>
                <result property="planId" column="plan_id"/>
                <result property="biddingCode" column="bidding_code"/>
                <result property="modeType" column="mode_type"/>
                <result property="periodNum" column="period_num"/>
                <result property="roundNum" column="round_num"/>
                <result property="biddingStatus" column="bidding_status"/>
                <result property="planName" column="plan_name"/>
                <result property="planStartDate" column="plan_start_date"/>
                <result property="planEndTime" column="plan_end_time"/>
                <result property="trafficMode" column="traffic_mode"/>
                <result property="deliveryMode" column="delivery_mode"/>
                <result property="describe" column="describe"/>
                <result property="quality" column="quality"/>
                <result property="deliveryStartDate" column="delivery_start_date"/>
                <result property="deliveryEndTime" column="delivery_end_time"/>
                <result property="remark" column="remark"/>
                <result property="isDelete" column="is_delete"/>
                <result property="createBy" column="create_by"/>
                <result property="createTime" column="create_time"/>
                <result property="updateBy" column="update_by"/>
                <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="FlowSelBidding">
            a.id,
            a.round_id,
            a.underway_id,
            a.company_id,
            a.plan_id,
            a.bidding_code,
            a.mode_type,
            a.period_num,
            a.round_num,
            a.bidding_status,
            a.plan_name,
            a.plan_start_date,
            a.plan_end_time,
            a.traffic_mode,
            a.delivery_mode,
            a.describe,
            a.quality,
            a.delivery_start_date,
            a.delivery_end_time,
            a.remark,
            a.is_delete,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time    </sql>

    <select id="queryList" resultMap="FlowSelBiddingMap">
        select <include refid="FlowSelBidding"/> from flow_sel_bidding a
        <where>
        </where>
    </select>
</mapper>