<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.FlowSelPlanDao">
    <resultMap id="FlowSelPlanMap" type="com.junl.crm_common.pojo.work.FlowSelPlanEntity">
                <result property="id" column="id"/>
                <result property="underwayId" column="underway_id"/>
                <result property="companyId" column="company_id"/>
                <result property="planCode" column="plan_code"/>
                <result property="modeType" column="mode_type"/>
                <result property="planStatus" column="plan_status"/>
                <result property="planName" column="plan_name"/>
                <result property="planStartDate" column="plan_start_date"/>
                <result property="planEndTime" column="plan_end_time"/>
                <result property="remark" column="remark"/>
                <result property="isDelete" column="is_delete"/>
                <result property="createBy" column="create_by"/>
                <result property="createTime" column="create_time"/>
                <result property="updateBy" column="update_by"/>
                <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="FlowSelPlan">
            a.id,
            a.underway_id,
            a.company_id,
            a.plan_code,
            a.mode_type,
            a.plan_status,
            a.plan_name,
            a.plan_start_date,
            a.plan_end_time,
            a.remark,
            a.is_delete,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time    </sql>

    <select id="queryList" resultMap="FlowSelPlanMap">
        select <include refid="FlowSelPlan"/> from flow_sel_plan a
        <where>
        </where>
    </select>
</mapper>