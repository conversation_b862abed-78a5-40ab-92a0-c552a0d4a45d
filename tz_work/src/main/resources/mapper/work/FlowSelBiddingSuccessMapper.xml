<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.FlowSelBiddingSuccessDao">
    <resultMap id="FlowSelBiddingSuccessMap" type="com.junl.crm_common.pojo.work.FlowSelBiddingSuccessEntity">
                <result property="id" column="id"/>
                <result property="underwayId" column="underway_id"/>
                <result property="biddingId" column="bidding_id"/>
                <result property="sequenceId" column="sequence_id"/>
                <result property="productId" column="product_id"/>
                <result property="productName" column="product_name"/>
                <result property="price" column="price"/>
                <result property="count" column="count"/>
                <result property="addPrice" column="add_price"/>
                <result property="addCount" column="add_count"/>
                <result property="offerId" column="offer_id"/>
                <result property="createTime" column="create_time"/>
                <result property="updateTime" column="update_time"/>
                <result property="createBy" column="create_by"/>
                <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="FlowSelBiddingSuccess">
            a.id,
            a.underway_id,
            a.bidding_id,
            a.sequence_id,
            a.product_id,
            a.product_name,
            a.price,
            a.count,
            a.add_price,
            a.add_count,
            a.offer_id,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by    </sql>

    <select id="queryList" resultMap="FlowSelBiddingSuccessMap">
        select <include refid="FlowSelBiddingSuccess"/> from flow_sel_bidding_success a
        <where>
        </where>
    </select>
</mapper>