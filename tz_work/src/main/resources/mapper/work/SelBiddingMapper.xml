<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelBiddingDao">
    <resultMap id="SelBiddingMap" type="com.junl.crm_common.pojo.work.SelBiddingEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="planId" column="plan_id"/>
        <result property="biddingCode" column="bidding_code"/>
        <result property="modeType" column="mode_type"/>
        <result property="periodNum" column="period_num"/>
        <result property="roundNum" column="round_num"/>
        <result property="biddingStatus" column="bidding_status"/>
        <result property="planName" column="plan_name"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="remark" column="remark"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="companyName" column="company_name"/>
        <result property="createName" column="real_name"/>
    </resultMap>

    <sql id="SelBidding">
        a.id,
            a.company_id,
            a.plan_id,
            a.bidding_code,
            a.mode_type,
            a.period_num,
            a.round_num,
            a.bidding_status,
            a.plan_name,
            a.plan_start_date,
            a.plan_end_time,
            a.remark,
            a.is_delete,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time</sql>

    <select id="queryList" resultMap="SelBiddingMap">
        select <include refid="SelBidding"/>,b.real_name  from sel_bidding a
        left join sys_user b on a.create_by=b.user_id
        <where>
            a.is_delete=0
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
            <if test="modeType!=null and modeType!=''">
                AND a.mode_type=#{modeType}
            </if>
            <if test="biddingStatus!=null and biddingStatus!=''">
                AND a.bidding_status=#{biddingStatus}
            </if>
            <if test="planName!=null and planName!=''">
                AND a.plan_name like  CONCAT('%',#{planName},'%')
            </if>
            <if test="flag!=null">
                <choose>
                    <when test="flag == 0">
                        AND a.bidding_status!=8
                    </when>
                    <otherwise>
                        AND a.bidding_status=8
                    </otherwise>
                </choose>
            </if>
            <if test="userIds!=null">
                and a.create_by in (
                <foreach collection="userIds" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
        </where>
        order by a.create_time DESC
    </select>


    <select id="getAppInfo" resultMap="SelBiddingMap">
        select  a.*,b.company_name from sel_bidding a
                                            LEFT JOIN sys_company b ON b.company_id =a.company_id
        WHERE a.id=#{id}
    </select>


    <resultMap id="biddingMap" type="com.junl.crm_common.pojo.work.SelBiddingSequenceEntity">
        <id property="id" column="id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="sequence" column="sequence"/>
        <collection property="customerList" ofType="com.junl.crm_common.pojo.work.SelBiddingCustomerEntity"
                    javaType="java.util.List"
                    resultMap="customerMap"/>
    </resultMap>

    <resultMap id="customerMap" type="com.junl.crm_common.pojo.work.SelBiddingCustomerEntity">
        <id property="id" column="id_b"/>
        <result property="sequence" column="sequence_b"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <collection property="offerList" ofType="com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity"
                    javaType="java.util.List"
                    resultMap="offerMap"/>
    </resultMap>

    <resultMap id="offerMap" type="com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity">
        <id property="id" column="id_d"/>
        <result property="createTime" column="create_time"/>
        <result property="offerer" column="offerer"/>
        <result property="count" column="count"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="price" column="price"/>
    </resultMap>


    <select id="querySequence" resultMap="biddingMap">
        select a.id,a.`sequence`,a.bidding_id,b.customer_id,b.id as id_b,b.sequence as sequence_b,c.customer_name,
               d.id as id_d,d.relevance_id,d.product_id,d.product_name,d.price,d.count,d.offerer,d.create_time
        from sel_bidding_sequence  a
                 left join sel_bidding_customer b on a.id=b.sequence
                 left join sel_customer c on b.customer_id=c.id
                 left join sel_bidding_customer_offer d on b.id=d.relevance_id
        where a.bidding_id=#{id}
        order by a.`sequence` DESC
    </select>


    <resultMap id="customerPage" type="com.junl.crm_common.pojo.work.SelBiddingCustomerEntity">
        <id property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <collection property="offerList" ofType="com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity">
            <id property="id" column="ids"/>
            <result property="productName" column="product_name"/>
            <result property="price" column="price"/>
            <result property="count" column="count"/>
            <result property="createTime" column="create_time"/>
        </collection>
    </resultMap>


    <select id="queryBiddingPage" resultMap="customerPage">
        select a.id,a.customer_id,b.customer_name,c.id as ids,c.product_name,c.price,c.count,c.create_time
        from sel_bidding_customer a left join sel_customer b on a.customer_id=b.id
                                    left join sel_bidding_customer_offer c on a.id= c.relevance_id
        where a.sequence=#{id}
    </select>

    <resultMap id="flowSuccessMap" type="com.junl.crm_common.pojo.work.FlowSelBiddingEntity">
        <id property="id" column="id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="biddingCode" column="bidding_code"/>
        <result property="modeType" column="mode_type"/>
        <result property="roundNum" column="round_num"/>
        <result property="planName" column="plan_name"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="trafficMode" column="traffic_mode"/>
        <result property="deliveryMode" column="delivery_mode"/>
        <result property="quality" column="quality"/>
        <result property="describe" column="describe"/>
        <result property="deliveryStartDate" column="delivery_start_date"/>
        <result property="deliveryEndTime" column="delivery_end_time"/>
        <result property="remark" column="remark"/>
        <result property="periodNum" column="period_num"/>
        <collection property="customerList" ofType="com.junl.crm_common.pojo.work.FlowSelBiddingCustomerEntity">
            <result property="customerId" column="customer_id"/>
            <result property="customerName" column="customer_name"/>
        </collection>
        <collection property="productList" ofType="com.junl.crm_common.pojo.work.FlowSelBiddingProductEntity">
            <result property="productId" column="product_id"/>
            <result property="productName" column="product_name"/>
            <result property="baseFee" column="base_fee"/>
            <result property="contractQuantity" column="contract_quantity"/>
            <result property="stockQuantity" column="stock_quantity"/>
            <result property="dailyQuantity" column="daily_quantity"/>
            <result property="saleQuantity" column="sale_quantity"/>
        </collection>
    </resultMap>

    <select id="getFlowBiddingInfo" resultMap="flowSuccessMap">
        select a.id,a.bidding_code,a.bidding_id,a.mode_type,a.round_num,a.plan_name,a.plan_start_date,
               a.plan_end_time,a.traffic_mode,a.describe,a.delivery_mode,a.quality,a.delivery_start_date,
               a.delivery_end_time,a.remark,a.period_num,
               b.customer_id,d.customer_name,c.product_id,c.product_name,
               c.contract_quantity,c.daily_quantity,c.stock_quantity,c.sale_quantity,c.base_fee
        from flow_sel_bidding a
                 left join flow_sel_bidding_customer b on a.round_id=b.sequence
                 left join sel_customer d  on b.customer_id=d.id
                 left join flow_sel_bidding_product c on a.round_id=c.sequence_id
        where a.underway_id=#{id}
    </select>


    <resultMap id="successMap" type="com.junl.crm_common.pojo.work.SelBiddingEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="planId" column="plan_id"/>
        <result property="biddingCode" column="bidding_code"/>
        <result property="modeType" column="mode_type"/>
        <result property="periodNum" column="period_num"/>
        <result property="roundNum" column="round_num"/>
        <result property="biddingStatus" column="bidding_status"/>
        <result property="planName" column="plan_name"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="remark" column="remark"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="companyName" column="company_name"/>
        <collection property="successList" ofType="com.junl.crm_common.pojo.work.SelBiddingSuccessEntity" javaType="java.util.List" resultMap="successProduct"/>
    </resultMap>

    <resultMap id="successProduct" type="com.junl.crm_common.pojo.work.SelBiddingSuccessEntity">
        <result property="offerName" column="customer_name"/>
        <collection property="successList" ofType="com.junl.crm_common.pojo.work.SelBiddingSuccessProductEntity">
            <result property="productName" column="product_name"/>
            <result property="price" column="price"/>
            <result property="count" column="count"/>
            <result property="addPrice" column="add_price"/>
            <result property="addCount" column="add_count"/>
        </collection>
    </resultMap>


    <select id="getSuccessInfo" resultMap="successMap">
        select <include refid="SelBidding"/>,e.customer_name,d.product_name,d.price,d.count,d.add_price,d.add_count from sel_bidding a
        left join sel_bidding_sequence b on a.id=b.bidding_id and a.round_num =b.sequence
        left join sel_bidding_success c on b.id=c.sequence_id
        left join sel_customer e on c.offer_id=e.id
        left join sel_bidding_success_product d on c.id=d.success_id
        where a.id=#{id}
    </select>
    <select id="getOfferList" resultType="com.junl.crm_common.pojo.work.SelBiddingCustomerOfferEntity">
        select a.product_name,a.price,a.count,a.create_time,a.offerer,b.customer_name as offererName from sel_bidding_customer_offer a
        left join sel_customer b on a.offerer=b.id
        where a.relevance_id in (
        <foreach collection="relevanceList" separator="," item="id">
            #{id}
        </foreach>
        )
        and a.product_id=#{productId}
    </select>
    <select id="getSuccessCustomer" resultType="com.junl.crm_common.pojo.work.SelBiddingSuccessEntity">
        select a.id,b.customer_name as offerName  from sel_bidding_success a
        left join sel_customer b  on a.offer_id=b.id
        where a.bidding_id=#{biddingId}
    </select>
    <select id="queryInquiry" resultType="com.junl.crm_common.pojo.work.SelInquiryOfferEntity">
        select a.product_name,a.price,a.count,a.create_time,b.customer_name as offerName from sel_inquiry_offer a
        left join sel_customer b on a.offerer=b.id
        where  a.relevancy_id=#{relevancyId} and a.offerer=#{customerId}
    </select>
    <select id="getSelectList" resultType="com.junl.crm_common.pojo.work.SelBiddingEntity">
        select <include refid="SelBidding"/> from sel_bidding a
        where a.period_num=#{time}
        and year(a.create_time)=year(#{date})
        and a.mode_type=#{modeType}
    </select>
    <select id="getListTime" resultType="java.lang.Integer">
        select a.period_num from sel_bidding a
        where year(a.create_time)=year(#{date})
        and a.company_id=#{companyId}
        group by a.period_num
        order by a.period_num DESC
    </select>
    <select id="getBiddingCustomer" resultType="com.junl.crm_common.pojo.work.SelBiddingCustomerEntity">
        select a.id,a.customer_id,b.customer_name from sel_bidding_customer a
        left join sel_customer b on a.customer_id=b.id
        where `sequence`=#{sequenceId}
    </select>
    <select id="queryMutualNum" resultType="java.lang.Integer">
        select count(*) from sel_bidding_sequence a
        left join sel_bidding_product b on a.id = b.sequence_id
        left join sel_bidding_customer c on a.id=c.sequence
        where a.bidding_id in (
            <foreach collection="ids" separator="," item="id">
                #{id}
            </foreach>
            )
            and b.product_id=#{productId}
            and c.customer_id=#{customerId}
    </select>

    <select id="queryCustomerIds" resultType="java.lang.String">
        select c.id from sel_bidding_sequence a
        left join sel_bidding_product b on a.id = b.sequence_id
        left join sel_bidding_customer c on a.id=c.sequence
        where a.bidding_id in (
        <foreach collection="ids" separator="," item="id">
            #{id}
        </foreach>
        )
        and b.product_id=#{productId}
        and c.customer_id=#{customerId}
    </select>
    <select id="queryCustomerOfferCount" resultType="java.lang.Integer">
        select count(*) from sel_bidding_customer_offer a
        where a.relevance_id in (
            <foreach collection="ids" separator="," item="id">
                #{id}
            </foreach>
            )
            and a.product_id=#{productId}
    </select>
</mapper>