<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelProductDao">
    <resultMap id="SelProductMap" type="com.junl.crm_common.pojo.work.SelProductEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="productType" column="product_type"/>
        <result property="remark" column="remark"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="created_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="updated_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="SelProduct">
        a.id,
            a.company_id,
            a.product_code,
            a.product_name,
            a.product_type,
            a.remark,
            a.is_delete,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time    </sql>

    <select id="queryList" resultMap="SelProductMap">
        select <include refid="SelProduct"/> from sel_product a
        <where>
            a.is_delete=0
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
            <if test="productName!=null and productName!=''">
                AND a.product_name like concat('%',#{productName},'%')
            </if>
        </where>
    </select>
</mapper>