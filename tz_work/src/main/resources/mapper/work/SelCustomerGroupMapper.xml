<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelCustomerGroupDao">
    <resultMap id="SelCustomerGroupMap" type="com.junl.crm_common.pojo.work.SelCustomerGroupEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="groupName" column="group_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="relevanceEntities" ofType="com.junl.crm_common.pojo.work.SelGroupRelevanceEntity">
            <result property="customerId" column="customer_id"/>
            <result property="customerName" column="customer_name"/>
        </collection>
    </resultMap>

    <sql id="SelCustomerGroup">
        a.id,
            a.company_id,
            a.group_name,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time    </sql>

    <select id="queryList" resultMap="SelCustomerGroupMap">
        select <include refid="SelCustomerGroup"/> from sel_customer_group a
        <where>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
            <if test="groupName!=null and groupName!=''">
                AND a.group_name like CONCAT('%',#{groupName},'%')
            </if>
            <if test="userId!=null and userId!=''">
                AND a.create_by=#{userId}
            </if>
        </where>
    </select>
    <select id="getInfo" resultMap="SelCustomerGroupMap">
        select <include refid="SelCustomerGroup"/>,b.customer_id,c.customer_name from sel_customer_group a
        left join sel_group_relevance b  on a.id=b.group_id
        left join sel_customer c on b.customer_id=c.id
        where a.id=#{id}
    </select>
    <select id="queryGroupCustomer" resultType="com.junl.crm_common.pojo.work.SelGroupRelevanceEntity">
        select a.customer_id,b.customer_name from sel_group_relevance a  left join
        sel_customer b on a.customer_id=b.id
        where a.group_id=#{id}
    </select>
</mapper>