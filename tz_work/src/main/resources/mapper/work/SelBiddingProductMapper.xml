<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_work.dao.SelBiddingProductDao">
    <resultMap id="SelBiddingProductMap" type="com.junl.crm_common.pojo.work.SelBiddingProductEntity">
                <result property="id" column="id"/>
                <result property="sequenceId" column="sequence_id"/>
                <result property="productName" column="product_name"/>
                <result property="productId" column="product_id"/>
                <result property="modeType" column="mode_type"/>
                <result property="baseFee" column="base_fee"/>
                <result property="contractQuantity" column="contract_quantity"/>
                <result property="stockQuantity" column="stock_quantity"/>
                <result property="dailyQuantity" column="daily_quantity"/>
                <result property="saleQuantity" column="sale_quantity"/>
                <result property="remark" column="remark"/>
                <result property="price" column="price"/>
                <result property="count" column="count"/>
    </resultMap>

    <sql id="SelBiddingProduct">
            a.id,
            a.sequence_id,
            a.product_name,
            a.product_id,
            a.mode_type,
            a.base_fee,
            a.contract_quantity,
            a.stock_quantity,
            a.daily_quantity,
            a.sale_quantity,
            a.remark    </sql>

    <select id="queryList" resultMap="SelBiddingProductMap">
        select <include refid="SelBiddingProduct"/> from sel_bidding_product a
        <where>
        </where>
    </select>


    <select id="queryAppList" resultMap="SelBiddingProductMap">
        SELECT a.*,
        IFNULL((SELECT IFNULL(f.price,0) from sel_bidding_customer_offer f WHERE f.relevance_id =d.id
        AND f.product_id = a.product_id AND f.offerer = d.customer_id),0) as price,
        IFNULL((SELECT IFNULL(f.count,0) from sel_bidding_customer_offer f WHERE f.relevance_id =d.id
        AND f.product_id = a.product_id AND f.offerer = d.customer_id),0) as count
        from sel_bidding_product a
        LEFT JOIN sel_bidding_sequence b ON b.id = a.sequence_id
        LEFT JOIN sel_bidding c ON c.id = b.bidding_id
        LEFT JOIN sel_bidding_customer d ON d.sequence = b.id
        <where>
            c.is_delete=0
            <if test="sequenceId!=null and sequenceId!=''">
                AND b.id=#{sequenceId}
            </if>
            <if test="customerId!=null and customerId!=''">
                AND d.customer_id=#{customerId}
            </if>
        </where>
    </select>
</mapper>