<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.junl</groupId>
		<artifactId>tz_bidding</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<version>0.0.1-SNAPSHOT</version>

	<groupId>com.junl</groupId>
	<artifactId>tz_work</artifactId>
	<description>业务模块</description>
	<name>tz_work</name>

	<dependencies>
		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_common</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_rpc</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_quartz</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_msg</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
			<version>2.8.8</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.48</version>
		</dependency>
	</dependencies>

</project>
