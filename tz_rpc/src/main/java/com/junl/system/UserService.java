package com.junl.system;

import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;

import java.util.List;

/**
 * @description: 用户相关的接口
 * @author: daiqimeng
 * @date: 2021/6/911:03
 */
public interface UserService {

    SysUserEntity getUserInfoByTel(String phone);

    //根据用户id 查询用户信息
    SysUserEntity getUserInfo(String userId);
    //根据部门id 查询该部门下所有员工id
    List<String> getDeptUsers(String deptId);
    //根据数据权限标识id 查询数据权限关联的所有用户id
    List<String> getUsers(String dataId);

    String getName(String userId);

    boolean updateOpenId(String openId,String soleId);

    SysUserSoleEntity getSoleId(String phone);

    SysUserSoleEntity getSole(String userid);






}
