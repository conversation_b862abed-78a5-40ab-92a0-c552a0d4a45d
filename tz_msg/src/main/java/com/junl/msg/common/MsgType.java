package com.junl.msg.common;

import java.util.Arrays;
import java.util.Optional;

/**
 * @description: 消息类型
 * @author: daiqimeng
 * @date: 2021/6/416:05
 */
public enum MsgType {
    //跳链
    LINK(5,"sys_link"),
    //个人消息
    USER(4,"sys_user"),
    //审批
    EXAMINE(3,"sys_check"),
    //邮箱
    EMAIL(2,"sys_email"),
    //短信
    SMS(1,"sys_sms");




    private Integer code;
    private String key;

    MsgType(Integer code,String key){
        this.code=code;
        this.key=key;
    }

    public Integer getCode(){
        return this.code;
    }
    public String getKey(){
        return this.key;
    }

    /**
     * 根据给定的code 返回key  没有返回null
     * @param coke
     * @return
     */
    private static String getKey(Integer coke){
        Optional<MsgType> first = Arrays.stream(MsgType.values()).filter(x -> x.getCode().equals(coke)).findFirst();
        return first.isPresent()?first.get().getKey():null;
    }

    /**
     * 根据给定的key 返回code  没有返回null
     * @param key
     * @return
     */
    private static Integer getCode(String key){
        Optional<MsgType> first = Arrays.stream(MsgType.values()).filter(x -> x.getKey().equals(key)).findFirst();
        return first.isPresent()?first.get().getCode():null;
    }
}
