package com.junl.msg;

import com.junl.crm_common.tools.Assert;
import com.junl.msg.socket.ChannelSocketEntity;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/1411:41
 */
public class SocketUtils {

    /**
     * @describe:  查询是否包含某个用户链接 没有返回null  存在返回对应socket链接对象
     * <AUTHOR>
     * @date 2021/7/14 11:43
     * @param map
     * @param soleId
     * @return: {@link ChannelSocketEntity}
     */
    public static List<ChannelSocketEntity> query(Map<String,ChannelSocketEntity> map,String soleId){
        List<ChannelSocketEntity> resultList = map.entrySet()
                .stream()
//                .filter(x -> !ObjectUtils.isEmpty(x.getValue()))
                .map(x->x.getValue())
                .filter(x->!ObjectUtils.isEmpty(x))
                .filter(x -> soleId.equals(x.getName()))
                .collect(Collectors.toList());
        return resultList;
    }
}
