package com.junl.msg.listener;

import com.junl.crm_common.tools.Assert;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * @description: 监听容器启动完成
 * @author: daiqimeng
 * @date: 2021/7/1518:22
 */
@Component
@Log4j2
public class MsgApplicationListener implements ApplicationListener<ContextRefreshedEvent> {

    public static ApplicationContext applicationContexts;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        ApplicationContext applicationContext = event.getApplicationContext();
        if (!Assert.notNull(applicationContexts)) {
            applicationContexts=applicationContext;
        }
    }
}
