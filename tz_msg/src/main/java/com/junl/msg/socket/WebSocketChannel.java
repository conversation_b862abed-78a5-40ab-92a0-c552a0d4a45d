package com.junl.msg.socket;

import com.alibaba.fastjson.JSONObject;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.JWTUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.log4j.Log4j2;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description:
 * @author: Mir Dai
 * @date: 2021/4/29 9:35
 */
@Log4j2
public class WebSocketChannel extends SimpleChannelInboundHandler<TextWebSocketFrame>{

    //连接的客户端容器  这样写就不支持集群部署了 不然会功能异常 单机服务可以这么写
    public static Map<String,ChannelSocketEntity> handlerList=new ConcurrentHashMap<>();

    //收到推送消息触发
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, TextWebSocketFrame msg) throws Exception {
        String text = msg.text();
        if (text.equals("ping")) {
            Result success = Result.success();
            success.setCode(SocketStatus.HEARTBEAT.getCode());
            success.setData("pong");
            ctx.writeAndFlush(new TextWebSocketFrame(JSONObject.toJSONString(success)));
            //认证身份
        }else if(text.startsWith("token-")){
            SysUserEntity user = JWTUtil.getUsername(text.split("-")[1]);
            if(Assert.notNull(user)){
                log.info("新的用户链接：{}",user.getUserId());


                String s = ctx.channel().id().asLongText();

                handlerList.get(s).setName(user.getSoleId());

            }
        }else{
            ctx.close();
            handlerList.remove(ctx.channel().id().asLongText());
        }
    }

    //有客户端连接触发
    @Override
    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        log.info("新的客户端链接");
        ChannelSocketEntity channelSocketEntity = new ChannelSocketEntity();
        channelSocketEntity.setContext(ctx);
        handlerList.put(ctx.channel().id().asLongText(),channelSocketEntity);
    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) throws Exception {
        ctx.close();
        log.info("连接关闭");
        String id = ctx.channel().id().asLongText();
        handlerList.remove(id);
    }


    //客户端异常触发
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause){
        ctx.close();
        log.warn("客户端连接异常 :{}",cause.getMessage());
        String id = ctx.channel().id().asLongText();
        handlerList.remove(id);
    }

    @Override
    //心跳检测 防止断线
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        super.userEventTriggered(ctx, evt);
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (IdleState.WRITER_IDLE.equals((event.state()))) {
                handlerList.remove(ctx.channel().id().asLongText());
                ctx.close();
            }
        }
    }
}