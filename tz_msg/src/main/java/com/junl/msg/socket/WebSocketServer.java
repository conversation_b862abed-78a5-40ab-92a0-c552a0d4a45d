package com.junl.msg.socket;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.extern.log4j.Log4j2;

/**
 * @description:
 * @author: Mir Dai
 * @date: 2021/4/29 9:23
 */
@Log4j2
public class WebSocketServer {

    private NioEventLoopGroup boos=null;
    private NioEventLoopGroup worker=null;
    private ServerBootstrap serverBootstrap=null;
    private int port;


    //构造工作线程组
    public WebSocketServer(int port){
        boos=new NioEventLoopGroup();
        worker=new NioEventLoopGroup();
        serverBootstrap=new ServerBootstrap();
        this.port=port;
    }

    //启动监听
    public void start(){

        try {
            serverBootstrap.group(boos,worker)
                    .channel(NioServerSocketChannel.class)
                    .childOption(ChannelOption.SO_KEEPALIVE,true)
                    .childHandler(new WebSocketChannelInitializer());
            ChannelFuture sync = serverBootstrap.bind(port).sync();
            log.info("socket 启动成功-------------------------------------------------------------------");
            sync.addListener((f)->{
               if(f.isSuccess()){
                   log.info("监听{}端口成功",port);
               }else{
                   log.info("监听{}端口失败：{}",port,f.cause().getMessage());
               }
            });
            sync.channel().closeFuture().sync();
        }catch (InterruptedException e){
            log.error("socket异常：{}",e.getMessage());
            boos.shutdownGracefully();
            worker.shutdownGracefully();
        }
    }

} 