package com.junl.crm_common.filter;

import com.alibaba.fastjson.JSONObject;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.HttpUtils;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_common.tools.Regular;
import com.junl.crm_common.tools.sms.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.List;
import java.util.regex.Matcher;

/**
 * @description:
 * @author: Mr Dai
 * @date: 2021/1/7 21:52
 */
@Component
@Slf4j
@WebFilter(filterName = "channelFilter", urlPatterns = {"/*"})
public class ChannelFilter  implements Filter {

    @Value("${spring.logger}")
    private Boolean logger;

    @Override
    /**
     * @author: Mir Dai
     * @description:  用自己封装request 和response入参
     * @date: 2021/5/20 15:39
     * @param request
     * @param response
     * @param chain
     * @return {@link Void}
     */
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain){
        String requestURI = ((HttpServletRequest) request).getRequestURI();
        try {
            if(logger){
                ResponseWrapper responseWrapper = new ResponseWrapper((HttpServletResponse) response);
                if(requestURI.endsWith("/upload")){
                    chain.doFilter(request,responseWrapper);
                }else{
                    ServletRequest requestWrapper = new MyRequestWrapper((HttpServletRequest) request);
                    chain.doFilter(requestWrapper,responseWrapper);
                }
                String s = new String(responseWrapper.getDataStream());
                log.info("访问路径{} 方法出参: {}",requestURI,s);
                ServletOutputStream outputStream = response.getOutputStream();
                outputStream.write(s.getBytes());
                outputStream.flush();
                outputStream.close();

            }else{
                chain.doFilter(request,response);
            }
        }catch (Exception e){
            e.getMessage();
            log.error(e.getMessage());
        }
    }
}