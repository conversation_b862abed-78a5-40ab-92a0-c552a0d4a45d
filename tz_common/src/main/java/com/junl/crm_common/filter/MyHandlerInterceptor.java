package com.junl.crm_common.filter;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.JWTUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
/**
 * @description:   请求拦截器
 * @author: Mr <PERSON>
 * @date: 2021/1/7 21:55
 */
@Slf4j
public class MyHandlerInterceptor  implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
//        String principal = (String) SecurityUtils.getSubject().getPrincipal();
//        if(Assert.notNull(principal)){
//            try {
//                SysUserEntity username = JWTUtil.getUsername(principal);
//                response.setHeader("userId",username.getUserId());
//            }catch (Exception e){
//                log.error("统一返回解析token出错 ：{}",principal);
//            }
//
//        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {


    }
}