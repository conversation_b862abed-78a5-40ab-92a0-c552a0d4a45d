package com.junl.crm_common.filter;

import com.junl.crm_common.tools.HttpUtils;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.nio.charset.Charset;


/**
 * @description:
 * @author: Mr Dai
 * @date: 2021/1/7 21:38
 */
@Log4j2
public class MyRequestWrapper  extends HttpServletRequestWrapper {

    private byte[] requestBody=null;

    @SneakyThrows
    public MyRequestWrapper(HttpServletRequest request) {
        super(request);
        String body = HttpUtils.getBody(request);

        String url = request.getRequestURI();
        log.info("访问路径{} 方法入参: {}",url,body);

        requestBody=body.getBytes(Charset.defaultCharset());

    }


    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream inputStream=new ByteArrayInputStream(requestBody);
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return inputStream.read();
            }
        };
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    public byte[] getRequestBody(){
        return this.requestBody;
    }
}