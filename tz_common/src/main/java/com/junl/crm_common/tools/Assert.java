package com.junl.crm_common.tools;



import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

/**
 * 断言工具类
 *
 * <AUTHOR>
 */
public class Assert {


    /**
     * @describe:  如果是null  我要做什么
     * <AUTHOR>
     * @date 2021/5/31 15:11
     * @param s
     * @param msg
     */
    public static void  notNull(String s,String msg){
        if (!StringUtils.isNotBlank(s)) {
            throw new NullPointerException(msg);
        }
    }

    /**
     * @describe:  如果是null  我要做什么
     * <AUTHOR>
     * @date 2021/5/31 15:11
     * @param s
     * @param msg
     */
    public static void notNull(Object s,String msg){
        if(null==s){
            throw new NullPointerException(msg);
        }
    }

    /**
     * @describe:  如果不是null  我要做什么
     * <AUTHOR>
     * @date 2021/5/31 15:11
     * @param s
     * @param msg
     */
    public static void  isNull(String s,String msg){
        if (StringUtils.isNotBlank(s)) {
            throw new NullPointerException(msg);
        }
    }

    /**
     * @describe:  如果不是null  我要做什么
     * <AUTHOR>
     * @date 2021/5/31 15:11
     * @param s
     * @param msg
     */
    public static void isNull(Object s,String msg){
        if(null==s){
            throw new NullPointerException(msg);
        }
    }

    /**
     * @describe: 判断是否是null 不是null 返回true
     * <AUTHOR>
     * @date 2021/5/31 15:11
     * @param s
     * @return: {@link boolean}
     */
    public static boolean notNull(String s){
        return StringUtils.isNotBlank(s);
    }
    /**
     * @describe: 判断是否是null 不是null 返回true
     * <AUTHOR>
     * @date 2021/5/31 15:11
     * @param s
     * @return: {@link boolean}
     */
    public static boolean notNull(Object s){
        return null!=s;
    }


    /**
     * @describe: 集合判断 不禁要判空 还要判断长度
     * <AUTHOR>
     * @date 2021/6/1 15:56
     * @param collection
     * @return: {@link boolean}
     */
    public static boolean notNullCollect(Collection collection){
        return  collection!=null&&collection.size()>0;
    }


}
