package com.junl.crm_common.tools.sms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "短信平台发送参数")
@Data
public class SmsSendParam {


    @ApiModelProperty(notes = "手机号码(多个号码用”,”分隔)，最多1000个号码", required = false)
    private  String UserNumber;

    @ApiModelProperty(notes = "短信内容, 最大402个字或字符（短信内容要求的编码为gb2312或gbk）", required = false)
    private  String MessageContent;





}