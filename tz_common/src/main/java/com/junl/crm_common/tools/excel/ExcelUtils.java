package com.junl.crm_common.tools.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.junl.crm_common.annotation.ExcelValidate;
import com.junl.crm_common.pojo.ExcelResult;
import com.junl.crm_common.tools.Assert;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.*;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: excel工具类
 * @author: Mr Dai
 * @date: 2021/6/21 21:07
 */
@Log4j2
@Component
public class ExcelUtils {
    /**
     * @author: Mir Dai
     * @description:  写excel 返回自定义输出流   不帮助关闭流 使用完注意流的关闭
     * @date: 2021/6/21 22:28
     * @param list
     * @param tClass
     * @return {@link ByteArrayOutputStream}
     */
    public <T>void write(List<T> list, Class<T> tClass,OutputStream outputStream){
        write(list,tClass,outputStream,null);
    }

    public <T>void write(List<T> list, Class<T> tClass,OutputStream outputStream,List<List<String>> head){
        write(list,tClass,outputStream,head,null);
    }

    /**
     * @describe:  写入带有合并的单元格
     * <AUTHOR>
     * @date 2021/8/5 11:09
     * @param list 数据
     * @param tClass calss
     * @param outputStream 输出流
     * @param mergeRowIndex 合并的行从第几行开始
     * @param mergeColumnIndex 需要合并的列
     */
    public <T>void write(List<T> list,Class<T> tClass,OutputStream outputStream,List<List<String>> head,int mergeRowIndex,int[] mergeColumnIndex){
        write(list,tClass,outputStream,head,new MergeWriterHandler(mergeRowIndex,mergeColumnIndex));
    }

    /**
     * @author: Mir Dai
     * @description: excel 写
     * @date: 2021/6/21 22:33
     * @param list
     * @param tClass
     * @param outputStream
     * @param writeHandlers   写时需要处理的例如 下拉 批注等处理器
     * @return {@link OutputStream}
     */
    public <T>void write(List<T> list, Class<T> tClass,OutputStream outputStream,List<List<String>> head,WriteHandler... writeHandlers){
        ExcelWriterBuilder write = EasyExcel.write(outputStream, tClass);

        write=write.excludeColumnFiledNames(getField(tClass));

        ExcelWriterSheetBuilder sheet = write.sheet(getSheetName(tClass));
        //判断是否有自定义表头
        if(null!=head && head.size()>0){
            sheet=sheet.head(head);
        }

        //判断是否有写入处理器
        if(null!=writeHandlers&&writeHandlers.length>0){
            for (WriteHandler writeHandler : writeHandlers) {
                sheet=sheet.registerWriteHandler(writeHandler);
            }
        }
        //自适应列宽和居中显示
        sheet=sheet.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .registerWriteHandler(new StyleExcelHandler());

        sheet.doWrite(list);
        try {
            outputStream.close();
            outputStream.flush();
        }catch (Exception e){
            log.error("流关闭异常 ：{}",e.getMessage());
        }
    }



    public <T>void sheetsWrite(List<ExcelSheetsExport<T>> lists,OutputStream outputStream,WriteHandler... writeHandlers){
        ExcelWriter build = EasyExcel.write(outputStream).build();
        WriteSheet sheet=null;

        for(int i=0;i<lists.size();i++){
            ExcelSheetsExport excelSheetsExport = lists.get(i);
            ExcelWriterSheetBuilder excelWriterSheetBuilder = EasyExcel.writerSheet(i, excelSheetsExport.getSheetName())
                    .head(excelSheetsExport.getHead())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new StyleExcelHandler());
            //判断是否有写入处理器
            if(null!=writeHandlers&&writeHandlers.length>0){
                for (WriteHandler writeHandler : writeHandlers) {
                    excelWriterSheetBuilder=excelWriterSheetBuilder.registerWriteHandler(writeHandler);
                }
            }
            sheet=excelWriterSheetBuilder.build();
            build.write(excelSheetsExport.getData(),sheet);
        }
        build.finish();
    }



    /**
     * @author: Mir Dai
     * @description:  excel 读取
     * @date: 2021/6/21 22:59
     * @param url 路径 远程路径
     * @param tClass 读取的excel 对应实体类型
     * @param excelService  实现如何插入数据库的service
     * @return {@link Void}
     */
    public  <T>void read(String url, Class<T> tClass, ExcelService<T> excelService, ExcelResult result){
        try {
            URL url1=new URL(url);
            InputStream inputStream = url1.openStream();
            read(inputStream,tClass,excelService,result);
        }catch (Exception e){
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }


    }

    public  <T>void read(InputStream inputStream, Class<T> tClass,ExcelService<T> excelService,ExcelResult result){
        try {
            ExcelReaderSheetBuilder sheet = EasyExcel.read(inputStream, tClass, new ReadDataListener<T>(excelService, result)).sheet(getSheetName(tClass));
            sheet.doRead();
        }catch (Exception e){
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }


    private <T>String getSheetName(Class<T> tClass){
        if (!tClass.isAnnotationPresent(SheetName.class)) {
            throw new IllegalArgumentException("实体类必须标注 SheetName");
        }
        String value = tClass.getDeclaredAnnotation(SheetName.class).value();
        return value;
    }

    private <T>Collection<String> getField(Class<T> tClass){
        Set<String> s=new HashSet<>();
        Field[] declaredFields = tClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            if (!declaredField.isAnnotationPresent(ExcelProperty.class)) {
                s.add(declaredField.getName());
            }
        }
        return s;
    }




    //@Log4j2
    class ReadDataListener<T> extends AnalysisEventListener<T>{

        private ExcelService<T> excelService;

        public int BATCH_COUNT=20;

        List<T> list=new ArrayList<>();

        private ExcelResult result;


        public ReadDataListener(ExcelService<T> excelService,ExcelResult result){
            this.excelService=excelService;
            this.result=result;
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            Integer rowIndex = context.readRowHolder().getRowIndex();
            if (validate(data,result,rowIndex+1)) {
                try {
                    Field size = data.getClass().getDeclaredField("row");
                    size.setAccessible(true);
                    size.set(data,rowIndex+1);
                }catch (Exception e){
                    log.error("导入的实体类必须携带row字段");
                }
                list.add(data);
                if(list.size()>=BATCH_COUNT){
                    if (excelService.saveBefore(list)) {
                        saveDate();
                        list.clear();
                    }
                }
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (excelService.saveBefore(list)) {
                saveDate();
                list.clear();
            }
            log.info("excel 解析结束");
        }

        private void saveDate(){
            excelService.save(list,result);
        }

        /**
         * 自定义校验
         * @param data
         * @param excelResult
         * @param row
         */
        boolean validate(T data,ExcelResult excelResult,int row){
            Field[] declaredFields = data.getClass().getDeclaredFields();
            for (Field declaredField : declaredFields) {
                if (declaredField.isAnnotationPresent(ExcelValidate.class)) {
                    ExcelValidate declaredAnnotation = declaredField.getDeclaredAnnotation(ExcelValidate.class);
                    boolean b = declaredAnnotation.notNull();
                    String patter = declaredAnnotation.patter();
                    int size = declaredAnnotation.size();
                    String value = declaredAnnotation.value();
                    declaredField.setAccessible(true);
                    try {
                        Object o = declaredField.get(data);
                        //校验不能为Null
                        if(!b){
                            if (!Assert.notNull(o)) {
                                excelResult.setError(excelResult.getError()+1);
                                excelResult.getErrorMsg().add("第"+row+"行"+value+"不能为空.");
                                return false;
                            }
                        }

                        if (Assert.notNull(o)) {
                            if(Assert.notNull(patter)){
                                String s = String.valueOf(o);
                                if (!s.matches(patter)) {
                                    excelResult.setError(excelResult.getError()+1);
                                    excelResult.getErrorMsg().add("第"+row+"行"+value+"数据格式错误.");
                                    return false;
                                }
                            }

                            if(size>0){
                                String s = String.valueOf(o);
                                if(s.length()>size){
                                    excelResult.setError(excelResult.getError()+1);
                                    excelResult.getErrorMsg().add("第"+row+"行"+value+"长度不能超过"+size);
                                    return false;
                                }
                            }

                        }

                    }catch (Exception e){
                        log.error("excel 校验错误：{}",e.getMessage());
                        excelResult.setError(excelResult.getError()+1);
                        excelResult.getErrorMsg().add("第"+row+"行发生未知错误,请检查excel");
                        return false;
                    }

                }
            }
            return true;
        }

    }

} 