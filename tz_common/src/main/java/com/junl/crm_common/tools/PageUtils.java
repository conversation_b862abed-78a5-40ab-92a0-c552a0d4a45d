package com.junl.crm_common.tools;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.junl.crm_common.annotation.Limit;
import com.junl.crm_common.annotation.Page;
import com.junl.crm_common.common.PageEntity;

import java.lang.reflect.Field;
import java.util.List;

/**
 * @description: 分页工具类
 * @author: Mr <PERSON>
 * @date: 2021/5/20 17:46
 */
public class PageUtils<T> {

    /**
     * @author: Mir Dai
     * @description: 执行分页
     * @date: 2021/5/21 11:48
     * @param t
     * @return {@link Void}
     */
    public static <T>void execute(T t){
            Integer page=null;
            Integer limit=null;
            Field[] declaredFields = t.getClass().getSuperclass().getDeclaredFields();
            try {
                for (Field declaredField : declaredFields) {
                    if (declaredField.isAnnotationPresent(Page.class)) {
                        declaredField.setAccessible(Boolean.TRUE);
                        page = (Integer) declaredField.get(t);
                    }else if(declaredField.isAnnotationPresent(Limit.class)){
                        declaredField.setAccessible(Boolean.TRUE);
                        limit= (Integer) declaredField.get(t);
                    }
                }
            }catch (Exception e){
                e.getMessage();
            }

            if(page!=null&&limit!=null){
                PageHelper.startPage(page,limit);
            }else{
                throw new RuntimeException("缺少分页参数");
            }

    }

    /**
     * @author: Mir Dai
     * @description:  获取分页数据对象
     * @date: 2021/5/21 11:48
     * @param list
     * @return {@link PageEntity <T>}
     */
    public static <T>PageEntity<T> getData(List<T> list){
        return new PageEntity(new PageInfo(list));
    }

} 