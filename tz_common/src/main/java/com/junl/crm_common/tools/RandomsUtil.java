package com.junl.crm_common.tools;

import java.security.SecureRandom;
import java.util.Random;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/1915:46
 */
public class RandomsUtil {

    private static String[] numbers={"0","1","2","3","4","5","6","7","8","9"};
    private static Random random=new SecureRandom();

    public static String getRandomNumber(int number){
        if(number<=0){
            throw  new RuntimeException("number 必须是一个大于0的整数");
        }
        StringBuilder stringBuilder=new StringBuilder();

        for(int i=0;i<number;i++){
            String temp = numbers[random.nextInt(numbers.length)];
            stringBuilder.append(temp);
        }

        return stringBuilder.toString();
    }

}
