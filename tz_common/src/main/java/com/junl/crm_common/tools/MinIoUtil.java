package com.junl.crm_common.tools;

import io.minio.*;
import io.minio.http.Method;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 *不完善
 * */
@Component
@Log4j2
public class MinIoUtil {

    private static MinioClient minioClient;

    @Value("${minio.endpoint}")
    private String endpoint;
    @Value("${minio.accessKey}")
    private String accessKey;
    @Value("${minio.secretKey}")
    private String secretKey;
    @Value("${minio.port}")
    private Integer port;

    private  void  init(){
        if(minioClient==null){
            minioClient=MinioClient.builder()
                    .endpoint(endpoint,port,false)
                    .credentials(accessKey,secretKey)
                    .build();
        }
    }

    /** 判断存在库是否存在 */
    public  Boolean exist(String bucketName){
        init();
        BucketExistsArgs build = BucketExistsArgs.builder().bucket(bucketName).build();
        Boolean flag=null;
        try {
            flag=minioClient.bucketExists(build);
        }catch (Exception e){
            log.error(e.getCause().getMessage());
        }
        return flag;
    }
    /** 删除存储库 */
    public void remove(String bucketName){
        init();
        try {
            minioClient.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
        }catch (Exception e){
            log.error(e.getCause().getMessage());
        }
    }
    /** 获取对象 */
    public  InputStream getObject(String objectName,String bucket){
        init();
        try {
            GetObjectResponse object = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucket)
                            .object(objectName)
                            .build()
            );
            return object;
        } catch (Exception e) {
            log.error(e.getCause().getMessage());
        }
        return null;
    }
    /** 获取请求的url */
    private String getUrl(String object,String bucket){
        try {
            String url = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .bucket(bucket)
                            .object(object)
                            .expiry(1, TimeUnit.MINUTES)
                            .method(Method.GET)
                            .build()
            );
            return url.split("\\?")[0];
        }catch (Exception e){
            log.error(e.getCause().getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /** 上传对象 */
    public String putObject(String object,String bucket,InputStream inputStream){
        init();
        try {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucket)
                            .object(object)
                            .stream(inputStream,inputStream.available(),-1)
                            .build()
            );
            String url = getUrl(object, bucket);
            return url;
        } catch (Exception e) {
            log.error(e.getCause().getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }
    /** 删除对象 */
    public void removeObject(String object,String bucket){
        init();
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucket)
                    .object(object)
                    .build());
        }catch (Exception e){
            log.error(e.getCause().getMessage());
        }
    }

}
