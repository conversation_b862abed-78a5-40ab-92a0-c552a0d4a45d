package com.junl.crm_common.tools;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.swing.text.NumberFormatter;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * @program: mms-car-wechat
 * @description: 阿里云上传文件工具类
 * @author: daiqimeng
 * @create: 2020-04-26 14:53
 **/
@Component
@Log4j2
public class OSSFileUtils {
    private OSS ossClient=null;
    // Endpoint以杭州为例，其它Region请按实际情况填写。
    @Value("${aliYunImag.endpoint}")
    String endpoint = null;
    // 云账号AccessKey有所有API访问权限，建议遵循阿里云安全最佳实践，创建并使用RAM子账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建。
    @Value("${aliYunImag.accessKeyId}")
    String accessKeyId = null;
    @Value("${aliYunImag.accessKeySecret}")
    String accessKeySecret=null;
    @Value("${aliYunImag.aliyunBucketName}")
    String aliyunBucketName =null;

    private String projectName="system/";

    private SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");


    //文件流上传  提供文件后缀
    public  String uploadFile(InputStream inputStream,String fileSuffixName){
        try {
            int available = inputStream.available();
            if(available>1500000){
                ByteArrayOutputStream byteArrayOutputStream = imageCompress(inputStream, 800, 1100, fileSuffixName);
                byte[] bytes = byteArrayOutputStream.toByteArray();
                log.info("压缩后的文件大小为：{}",bytes.length);
                inputStream.read(bytes);
            }
        }catch (IOException e){
            log.error("文件压缩报错.{}",e.getMessage());
        }

        String fileName=new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date())
                + (new Random().nextInt(9000) % (9000 - 1000 + 1) + 1000) +"."+ fileSuffixName;
        return privateUploadFile(inputStream,fileName);
    }
    private String  privateUploadFile(InputStream inputStream,String fileName){
        if(ossClient==null){
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        }
        // 上传文件流。
        String temp=projectName+simpleDateFormat.format(new Date())+"/"+fileName;
        ossClient.putObject(aliyunBucketName, temp, inputStream);
        // 关闭OSSClient。
        String url="https://"+aliyunBucketName+"."+endpoint+"/"+temp;
        return url;
    }
    //不提供后缀的默认全部png
    public String uploadFile(InputStream inputStream){
        String fileName=new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date())
                + (new Random().nextInt(9000) % (9000 - 1000 + 1) + 1000)+".png";
        return privateUploadFile(inputStream,fileName);
    }
    //spring 自带文件类型上传
    public String uploadFile(MultipartFile file){
        String suffix = getSuffix(file.getOriginalFilename());
        String url=null;
        try {
            url=uploadFile(file.getInputStream(),suffix);
        }catch (Exception e){
            return "上传文件失败";
        }
        return url;
    }

    /**
     * 多文件上传
     * @param files
     * @return
     */
    public List<String> uploadFileAll(MultipartFile[] files){
        List<String>  list=new ArrayList<>();
        for (MultipartFile file : files) {
            String s = uploadFile(file);
            list.add(s);
        }
        return list;
    }

    /**
     * 方法描述: 获取文件后缀  不是标准 一般用于windows平台  Linux 平台获取错误
     * @date 2020/4/26 16:42
     * @param fileName
     * @return  {@link String}
     */
    private static String getSuffix(String fileName){
        int i = fileName.lastIndexOf(".");
        String suffix=fileName.substring(i+1);
        return suffix;
    }

    /**
     * 方法描述: 压缩文件 返回字节流
     * @date 2020/12/3 10:12
     * @param inputStream 源文件
     * @param destWidth 宽
     * @param destHeight 高
     * @param imageTpye 文件类型
     * <AUTHOR>
     * @return  {@link ByteArrayOutputStream}
     */
    public static ByteArrayOutputStream imageCompress(InputStream inputStream, int destWidth, int destHeight, String imageTpye){

        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        //保存目标文件图像
        BufferedImage tagImage = null;
        //判断图片文件是否存在以及是否为文件类型
        try {
            BufferedImage read = ImageIO.read(inputStream);
            //生成目标缩略图
            tagImage = new BufferedImage(destWidth,destHeight,BufferedImage.TYPE_INT_RGB);
            //根据目标图片的大小绘制目标图片
            tagImage.getGraphics().drawImage(read,0,0,destWidth,destHeight,null);
            ImageIO.write(tagImage, imageTpye, byteArrayOutputStream);
        }catch (Exception e){
            log.error("文件压缩失败: {}",e.getMessage());
            return null;
        }
        return byteArrayOutputStream;
    }

    public  boolean  deleteFile(String filePath){
        try {
            if(ossClient==null){
                ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            }
            //替换掉统一前缀
            String replace = filePath.replace("https://crmmms.oss-cn-shanghai.aliyuncs.com/", "");
            String[] split = replace.split("/");
            ossClient.deleteObject(aliyunBucketName,split[2]);
        }catch (Exception e){
            log.error("删除文件错误：{}",e.getMessage());
            return false;
        }
        return true;
    }




}