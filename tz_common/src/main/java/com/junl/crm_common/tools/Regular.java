package com.junl.crm_common.tools;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 正则工具类
 * @author: daiqimeng
 * @date: 2021/8/1918:41
 */
public class Regular {
    public static Pattern compile = Pattern.compile("(?<=(http|https):\\/\\/)(([a-z]*.[a-z]*(.[a-z]*){0,2})|(\\d{1,3}.\\d{1,3}.\\d{1,3}.\\d{1,3}))(:\\d{2,5})?(?=\\/)");


    /**
     * 替换url中的域名\#端口 | ip\#端口
     * @param url  要替换的url
     * @param name 替换的值
     * @return
     */
    public static String replace(String url,String name){
        StringBuffer buffer=new StringBuffer();
        Matcher matcher = compile.matcher(url);
        if (matcher.find()) {
            matcher.appendReplacement(buffer,name);
        }
        matcher.appendTail(buffer);
        return buffer.toString();
    }


}
