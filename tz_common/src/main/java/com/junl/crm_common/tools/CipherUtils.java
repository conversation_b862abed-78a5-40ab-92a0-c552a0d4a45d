package com.junl.crm_common.tools;

import lombok.Data;
import lombok.SneakyThrows;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.*;

import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;


/**
 * @description: 加密/解密工具类
 * @author: Mr <PERSON>
 * @date: 2021/4/30 11:24
 */


public class CipherUtils {

    private static String DES_KEY="a1b2c35e";
    private static Integer HASH_SIZE=2048;


//    /**
//     * @author: Mr <PERSON>
//     * @description:  生成一套数字证书压缩包 （本地方法全部用私钥加密 公钥验证的方式 ）
//     * @date: 2021/7/1 23:08
//     * @param filePath 生成文件路径 示例：E:\\ (未对路径做校验，给路径就行)   包含 ftx证书文件 公钥 私钥 keyStore密钥库文件
//     * @param issuer 颁发者
//     * @param claim 领取者
//     * @param day 证书有效日期 (天)
//     * @param password (生成密钥库的密码  勿忘记使用本地密钥库存储多证书会用到)
//     * @return {@link Void}
//     */
//    @SneakyThrows
//    public static void generate(String filePath, CertificateDto issuer, CertificateDto claim, int day, String password){
//        //获取空参密钥库
//        KeyStore keyStore =loadStore();
//        //设置生成器
//        CertAndKeyGen keypair = new CertAndKeyGen(Algorithm.RSA.name, Algorithm.SHA256_WITH_RSA.name, "SUN");
//        keypair.generate(HASH_SIZE);
//        //构建证书信息
//        X509Certificate build = build(keypair, issuer, claim, day);
//        //操作密钥库
//        keyStore.setKeyEntry(password,keypair.getPrivateKey(),password.toCharArray(),new Certificate[]{build});
//        //字节流接受密钥文件
//        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
//        keyStore.store(byteArrayOutputStream,password.toCharArray());
//        //压缩到指定路径
//        FileUtils.MyZip myZip=new FileUtils.MyZip(new FileOutputStream(filePath+"/safety.zip"));
//        myZip.add("safety.keyStore",byteArrayOutputStream.toByteArray())
//                .add("safetyPub.key",base64Encoder(keypair.getPublicKey().getEncoded()).getBytes())
//                .add("safetyPri.key",base64Encoder(keypair.getPrivateKey().getEncoded()).getBytes())
//                .add("safety.pfx",byteArrayOutputStream.toByteArray());
//        myZip.closeEntry();
//        myZip.close();
//        byteArrayOutputStream.close();
//    }
//
//    /**
//     * @describe:  本地生成一个密钥库 密钥库含密码别名的一个原始一个证书 勿删除 否则后面操作该密钥库会报错
//     * <AUTHOR> Dai
//     * @date 2021/7/3 9:46
//     * @param filePath store密钥库生成位置
//     * @param password 密码
//     * @param issuer 颁发者 密钥库拥有者
//     */
//    @SneakyThrows
//    public static void generate(String filePath, String password, CertificateDto issuer){
//        KeyStore keyStore =loadStore();
//        CertAndKeyGen keypair = new CertAndKeyGen(Algorithm.RSA.name, Algorithm.SHA256_WITH_RSA.name, "SUN");
//        keypair.generate(HASH_SIZE);
//        X509Certificate selfCertificate = keypair.getSelfCertificate(issuer.getX500Name(), 1);
//        keyStore.setKeyEntry(password,keypair.getPrivateKey(),password.toCharArray(),new Certificate[]{selfCertificate});
//        String cn = issuer.getCN();
//        FileOutputStream fileOutputStream=new FileOutputStream(filePath+"/"+cn+".keystore");
//        keyStore.store(fileOutputStream,password.toCharArray());
//    }
//
//    /**
//     * @describe:  生成一个证书 并且加入到本地库  返回证书流 （本地方法全部用私钥加密 公钥验证的方式 可更换）
//     * <AUTHOR>
//     * @date 2021/7/3 10:05
//     * @param storePath 本地密钥库地址
//     * @param storePassword 本地密钥库密码
//     * @param issuer 证书颁发者
//     * @param claim 证书领取方
//     * @param day  生成证书天数
//     * @param unique 生成证书在本地密钥库的唯一标识
//     * @return: {@link ByteArrayOutputStream} 生成的证书流
//     */
//    @SneakyThrows
//    public static ByteArrayOutputStream  generate(String storePath, String storePassword, CertificateDto issuer, CertificateDto claim, int day, String unique){
//        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
//        //生成证书
//        KeyStore keyStore =loadStore();
//        CertAndKeyGen keypair = new CertAndKeyGen(Algorithm.RSA.name, Algorithm.SHA256_WITH_RSA.name, "SUN");
//        keypair.generate(HASH_SIZE);
//        X509Certificate build = build(keypair, issuer, claim, day);
//        keyStore.setKeyEntry(unique,keypair.getPrivateKey(),unique.toCharArray(),new Certificate[]{build});
//        keyStore.store(byteArrayOutputStream,unique.toCharArray());
//
//        //取出该证书
//        Certificate certificate = keyStore.getCertificate(unique);
//        //加入到本地库
//        KeyStore store = loadStore(storePath, storePassword);
//        store.setKeyEntry(unique,keypair.getPrivateKey(),storePassword.toCharArray(),new Certificate[]{certificate});
//        FileOutputStream fileOutputStream=new FileOutputStream(storePath);
//        store.store(fileOutputStream,storePassword.toCharArray());
//        fileOutputStream.close();
//
//        return byteArrayOutputStream;
//    }
//
//
//    /**
//     * @describe:  根据生成器构建新的证书
//     * <AUTHOR> Dai
//     * @date 2021/7/3 9:22
//     * @param keypair 构建器
//     * @param issuer 颁发者
//     * @param claim 领取方
//     * @param day 证书日期  (新的证书日期)
//     * @return: {@link X509Certificate}
//     */
//    @SneakyThrows
//    private static X509Certificate build(CertAndKeyGen keypair, CertificateDto issuer, CertificateDto claim, int day){
//        //获取证书类
//        X509Certificate selfCertificate = keypair.getSelfCertificate(issuer.getX500Name(), day);
//        byte[] encoded = selfCertificate.getEncoded();
//
//        //构建证书信息
//        X509CertImpl privateCert=new X509CertImpl(encoded);
//        X509CertInfo info=(X509CertInfo)privateCert.get(X509CertImpl.NAME+"."+X509CertImpl.INFO);
//
//        //设置过期时间
//        Date beginDate = new Date();
//        long end=beginDate.getTime()+day*1000*60*60*24;
//        CertificateValidity cv = new CertificateValidity(beginDate,new Date(end));
//        info.set(X509CertInfo.VALIDITY,cv);
//
//        //设置序列号
//        CertificateSerialNumber csn = new CertificateSerialNumber((int) (new Date().getTime()/1000));
//        info.set(X509CertInfo.SERIAL_NUMBER,csn);
//
//        //设置新证书的签发者
//        info.set(X509CertInfo.ISSUER+"."+CertificateIssuerName.DN_NAME,issuer.getX500Name());
//        //更新新证书的领用方
//        info.set(X509CertInfo.SUBJECT+"."+CertificateSubjectName.DN_NAME,claim.getX500Name());
//
//        //设置使用算法
//        AlgorithmId algorithm = new AlgorithmId(AlgorithmId.sha256WithRSAEncryption_oid);
//        info.set(CertificateAlgorithmId.NAME+"."+
//                CertificateAlgorithmId.ALGORITHM,algorithm);
//
//        //获取私钥
//        PrivateKey privateKey = keypair.getPrivateKey();
//
//        //构建新的证书实体类
//        X509CertImpl x509Cert=new X509CertImpl(info);
//        x509Cert.sign(privateKey, Algorithm.SHA256_WITH_RSA.name);
//
//        return x509Cert;
//    }
//
//    /**
//     * 根据路径加载密钥库
//     * @param storePath 密钥路径
//     * @param storePass 密钥密码
//     * @return
//     */
//    @SneakyThrows
//    private static KeyStore loadStore(String storePath, String storePass){
//        KeyStore store=KeyStore.getInstance(Algorithm.PKCS12.name);
//        FileInputStream fileInputStream=new FileInputStream(storePath);
//        store.load(fileInputStream,storePass.toCharArray());
//        fileInputStream.close();
//        return store;
//    }
//
//    /**
//     * 构建一个空的密钥库
//     * @return
//     */
//    @SneakyThrows
//    private static KeyStore loadStore(){
//        KeyStore instance = KeyStore.getInstance(Algorithm.PKCS12.getName());
//        instance.load(null,null);
//        return instance;
//    }
//
//
//    /**
//     * @describe:  删除本地密钥库中的某个证书(该证书未到期也验证不通过 表示废除该证书了)
//     * <AUTHOR> Dai
//     * @date 2021/7/1 18:49
//     * @param storePath 密钥库地址
//     * @param storePass 密钥库密码
//     * @param unique  需要删除的证书唯一别名标识
//     * @return: {@link boolean}
//     */
//    @SneakyThrows
//    public static boolean deleteCertificate(String storePath,String storePass,String unique){
//        KeyStore store=loadStore(storePath,storePass);
//        store.deleteEntry(unique);
//        FileOutputStream fileOutputStream=new FileOutputStream(storePath);
//        store.store(fileOutputStream,storePass.toCharArray());
//        fileOutputStream.close();
//        return true;
//    }
//
//    /**
//     * 遍历本地密钥库
//     * @param storePath
//     * @param storePass
//     */
//    @SneakyThrows
//    public static void iteration(String storePath,String storePass){
//        KeyStore store = loadStore(storePath, storePass);
//        Enumeration<String> aliases = store.aliases();
//        while (aliases.hasMoreElements()) {
//            String s = aliases.nextElement();
//            X509Certificate certificate = (X509Certificate) store.getCertificate(s);
//            if (null!=certificate) {
//                System.out.println("证书生效日期 ："+certificate.getNotAfter());
//                System.out.println("证书结束日期 :"+certificate.getNotBefore());
//                System.out.println("证书颁发者 ："+certificate.getIssuerDN().getName());
//                System.out.println("证书领用方 ："+certificate.getSubjectDN().getName());
//                System.out.println("证书签名算法： "+certificate.getSigAlgName());
//                System.out.println("证书序列号： "+certificate.getSerialNumber().toString(16));
//                System.out.println("证书公钥： "+base64Encoder(certificate.getPublicKey().getEncoded()));
//                System.out.println("证书别名："+s);
//            }
//
//        }
//    }
//
//    /**
//     * @describe:  根据本地密钥库验证证书是否有效 验证自己生成的证书
//     * <AUTHOR> Dai
//     * @date 2021/7/1 18:52
//     * @param storePath 本地密钥库
//     * @param storePass 本地密钥库密码
//     * @param unique 证书唯一标识
//     * @param inputStream 证书
//     */
//    @SneakyThrows
//    public static void verify(String storePath,String storePass,String unique, InputStream inputStream){
//        //加载本地密钥库
//        KeyStore store1 = loadStore(storePath, storePass);
//
//        if (!store1.containsAlias(unique)) {
//            throw new RuntimeException("未知的证书来源");
//        }
//
//        //加载需要验证的证书
//        KeyStore store=KeyStore.getInstance(Algorithm.PKCS12.getName());
//        store.load(inputStream,unique.toCharArray());
//
//
//
//        Certificate certificate1 = store1.getCertificate(unique);
//
//        Certificate certificate = store.getCertificate(unique);
//
//        if(null==certificate1||null==certificate){
//            throw new RuntimeException("未知的证书来源 ");
//        }
//        //通过本地密钥库验证外部证书的key
//        certificate1.verify(certificate.getPublicKey());
//
//        //验证证书是否过期
//        X509Certificate x509Certificate= (X509Certificate) certificate;
//        Date notAfter = x509Certificate.getNotAfter();
//        if(notAfter.compareTo(new Date())<1){
//            throw new RuntimeException("证书已过期");
//        }
//    }



    public static void main(String[] args)throws Exception {
        System.out.println(enCoderMd5("admin123"));
    }


    /**
     * @author: Mr Dai
     * @description:  DES算法加密解密
     * @date: 2021/6/30 21:30
     */
    public static String encoderDES(String content){
        return des(content,Cipher.ENCRYPT_MODE);
    }

    public static String decoderDES(String content){
        return des(content,Cipher.DECRYPT_MODE);
    }

    @SneakyThrows
    private static String des(String secretContent,int cipher){
        SecretKeyFactory keyFactory=SecretKeyFactory.getInstance(Algorithm.DES.name);
        Cipher des = Cipher.getInstance(Algorithm.DES.name/*+"/CBC/NoPadding"*/);
        //des spec
        DESKeySpec desKeySpec=new DESKeySpec(DES_KEY.getBytes());
        //IV向量
        //IvParameterSpec ivParameterSpec=new IvParameterSpec(DES_KEY.getBytes());
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        des.init(cipher,secretKey/*,ivParameterSpec*/);
        if(cipher==1){
            //加密
            byte[] bytes = des.doFinal(secretContent.getBytes());
            return base64Encoder(bytes);
            //解密
        }else{
            byte[] bytes = des.doFinal(base64Decoder(secretContent));
            return new String(bytes);
        }
    }
    /*************************************************************************/



    /**
     * @author: Mr Dai
     * @description:  消息摘要加密 不可逆
     * @date: 2021/6/30 21:30
     */

    public static String enCoderMd5(String content){
        return digest(Algorithm.MD5.name, content);
    }

    public static String enCoderSHA(String content){
        return digest(Algorithm.SHA.name, content);
    }
    public static String enCoderSHA1(String content){
        return digest(Algorithm.SHA1.name, content);
    }
    public static String enCoderSHA256(String content){
        return digest(Algorithm.SHA256.name, content);
    }
    public static String enCoderSHA512(String content){
        return digest(Algorithm.SHA512.name, content);
    }


    private static String digest(String name,String content){
        MessageDigest messageDigest = getMessageDigest(name);
        byte[] digest = messageDigest.digest(content.getBytes());
        return toHex(digest);
    }

    //不足长度高位补0
    private static String toHex(byte[] bytes){
        StringBuilder stringBuilder=new StringBuilder();
        for (byte aByte : bytes) {
            String s = Integer.toHexString(aByte & 0xff);
            if(s.length()==1){
                s="0"+s;
            }
            stringBuilder.append(s);
        }
        return stringBuilder.toString();
    }

    private static MessageDigest getMessageDigest(String algorithm){
        try {
            MessageDigest instance = MessageDigest.getInstance(algorithm);
            return instance;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage());
        }
    }
    /*******************************分割线***************************************/


    /**
     * @author: Mr Dai
     * @description:  非对称加密  安全性最高
     * @date: 2021/6/30 21:30
     */

    @Data
    static class Key{
        private String publicKey;
        private String privateKey;
    }

    /**
     * 系统自动生成公钥和私钥 返回自定义Key对象
     * @return
     */
    @SneakyThrows
    public static Key keyGen(){
        Key key=new Key();
        KeyPairGenerator instance = KeyPairGenerator.getInstance(Algorithm.RSA.name);
        instance.initialize(HASH_SIZE);
        KeyPair keyPair = instance.generateKeyPair();
        key.setPublicKey(base64Encoder(keyPair.getPublic().getEncoded()));
        key.setPrivateKey(base64Encoder(keyPair.getPrivate().getEncoded()));
        return key;
    }

    /**
     * 使用公钥加密
     * @param content
     * @param publicKey
     * @return
     */
    @SneakyThrows
    public static String RSAEncoder(String content,String publicKey){
        Cipher instance = Cipher.getInstance(Algorithm.RSA.name);
        instance.init(Cipher.ENCRYPT_MODE,getPubKey(publicKey));
        byte[] bytes = instance.doFinal(content.getBytes());
        return base64Encoder(bytes);
    }

    /**
     * 使用私钥解密
     * @param content
     * @param privateKey
     * @return
     */
    @SneakyThrows
    public static String RSADecoder(String content,String privateKey){
        Cipher instance = Cipher.getInstance(Algorithm.RSA.name);
        instance.init(Cipher.DECRYPT_MODE,getPrvKey(privateKey));
        return new String(instance.doFinal(base64Decoder(content)));
    }

    @SneakyThrows
    private static PublicKey getPubKey(String publicKey){
        KeyFactory factory = KeyFactory.getInstance(Algorithm.RSA.name);
        PublicKey pub_key = factory.generatePublic(new X509EncodedKeySpec(base64Decoder(publicKey)));
        return pub_key;
    }

    @SneakyThrows
    private static PrivateKey getPrvKey(String privateKey){
        KeyFactory factory = KeyFactory.getInstance(Algorithm.RSA.name);
        PrivateKey prv_key = factory.generatePrivate(new PKCS8EncodedKeySpec(base64Decoder(privateKey)));
        return prv_key;
    }
    /*******************************分割线***************************************/


    /**
     * @author: Mr Dai
     * @description:  非对称加密  生成数字签名  采用sha256rsa
     * @date: 2021/6/30 21:30
     */
    @SneakyThrows
    public static String sign(String content,String privateKey){
        PrivateKey prvKey = getPrvKey(privateKey);
        Signature instance = Signature.getInstance(Algorithm.SHA256_WITH_RSA.name);
        instance.initSign(prvKey);
        instance.update(content.getBytes());
        byte[] sign = instance.sign();
        return base64Encoder(sign);
    }

    @SneakyThrows
    public static boolean verify(String content,String publicKey,String secret){
        PublicKey pubKey = getPubKey(publicKey);
        Signature instance = Signature.getInstance(Algorithm.SHA256_WITH_RSA.name);
        instance.initVerify(pubKey);
        instance.update(content.getBytes());
        return instance.verify(base64Decoder(secret));
    }

    /*******************************分割线***************************************/

    /**
     * base64转码成String
     * @param bytes
     * @return
     */
    private static String base64Encoder(byte[] bytes){
        return new String(Base64.getEncoder().encode(bytes));
    }

    private static byte[] base64Decoder(String content){
        return Base64.getDecoder().decode(content.getBytes());
    }

    static enum Algorithm {

        PKCS12("PKCS12"),
        X509("X.509"),
        SHA256_WITH_RSA("SHA256WithRSA"),
        RSA("RSA"),
        MD5("MD5"),
        SHA512("SHA-512"),
        SHA256("SHA-256"),
        SHA1("SHA-1"),
        SHA("SHA-512"),
        DES("DES");

        private String name;

        Algorithm(String name) {
            this.name = name;
        }

        public String getName() {
            return this.name;
        }
    }
}