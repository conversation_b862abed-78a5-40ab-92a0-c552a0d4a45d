package com.junl.crm_common.tools;

import com.junl.crm_common.pojo.admin.SysUserEntity;
import lombok.extern.log4j.Log4j2;

import java.lang.reflect.Field;

/**
 * @description: 项目必须处理的一些事情
 * @author: daiqimeng
 * @date: 2021/5/3111:24
 */
@Log4j2
public class RuleTools {

    /**
     * @describe:  给项目实体类创建人 和更新人赋值 和公司Id
     * <AUTHOR>
     * @date 2021/5/31 11:26
     * @param o
     * @param sysUserEntity
     * @param flag true  新增  false 更新
     */
    public static void setField(Object o, SysUserEntity sysUserEntity,boolean flag){
        Class<?> aClass = o.getClass();
        try {
            Field updateBy = aClass.getDeclaredField("updateBy");
            if(flag){
                Field createBy = aClass.getDeclaredField("createBy");
                Field companyId = aClass.getDeclaredField("companyId");
                ClassUtils.openField(createBy);
                createBy.set(o,sysUserEntity.getUserId());
                ClassUtils.openField(companyId);
                companyId.set(o,sysUserEntity.getCompanyId());
            }
            ClassUtils.openField(updateBy);
            updateBy.set(o,sysUserEntity.getUserId());

        }catch (Exception e){
            log.error("赋值创建人、更新人、公司id 出错：{}",e.getMessage());
            throw new RuntimeException(e.getMessage());
        }

    }


}
