package com.junl.crm_common.tools;


import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import lombok.extern.log4j.Log4j2;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @program: mms-car-wechat
 * @description: jwt工具
 * @author: daiqimeng
 * @create: 2020-07-03 14:15
 **/
@Log4j2
public class JWTUtil {

    /**
     * 密钥，注意这里如果真实用到，应当设置到复杂点，相当于私钥的存在。如果被人拿到，想到于它可以自己制造token了。
     */
    private static final String SECRET = "FREAKING_AWESOME";

    /**
     * 暂定2个小时token 过期时间
     */
    private static final int OVER_TIME=1000*60*60*2;


    /**
     * 创建带有过期时间的token
     * @param sysUserEntity
     * @return
     */
    public static String createToken(SysUserEntity sysUserEntity) {
        Algorithm algorithm = Algorithm.HMAC256(SECRET);
        // 附带username信息
        return JWT.create()
                .withClaim("username", sysUserEntity.getUserName())
                .withClaim("companyId",sysUserEntity.getCompanyId())
                .withClaim("dept_id",sysUserEntity.getDeptId())
                .withClaim("userId",sysUserEntity.getUserId())
                .withClaim("soleId",sysUserEntity.getSoleId())
                .withExpiresAt(new Date(System.currentTimeMillis()+OVER_TIME))
                //创建一个新的JWT，并使用给定的算法进行标记
                .sign(algorithm);
    }

    /**
     * 创建无时间限制的token
     * @param sysUserEntity
     * @return
     */
    public static String createAlwaysToken(SysUserEntity sysUserEntity) {
        Algorithm algorithm = Algorithm.HMAC256(SECRET);
        // 附带username信息
        return JWT.create()
                .withClaim("username", sysUserEntity.getUserName())
                .withClaim("companyId",sysUserEntity.getCompanyId())
                .withClaim("dept_id",sysUserEntity.getDeptId())
                .withClaim("userId",sysUserEntity.getUserId())
                .withClaim("soleId",sysUserEntity.getSoleId())
                //创建一个新的JWT，并使用给定的算法进行标记
                .sign(algorithm);
    }

    /**
     * 校验 token 是否正确
     *
     * @param token    密钥
     * @return 是否正确 boolean
     */
    public static boolean verify(String token, SysUserEntity sysUserEntity) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            //在token中附带了role的信息
            JWTVerifier verifier = JWT.require(algorithm)
                    .withClaim("username", sysUserEntity.getUserName())
                    .withClaim("companyId",sysUserEntity.getCompanyId())
                    .withClaim("dept_id",sysUserEntity.getDeptId())
                    .withClaim("userId",sysUserEntity.getUserId())
                    .withClaim("soleId",sysUserEntity.getSoleId())
                    .build();
            //验证 token
            verifier.verify(token);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
    }

    /**
     * 获得token中的信息，无需secret解密也能获得
     *
     * @param token the token
     * @return token中包含的用户名 username
     */
    public static SysUserEntity getUsername(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            SysUserEntity sysUserEntity=new SysUserEntity();
            sysUserEntity.setUserId(jwt.getClaim("userId").asString());
            sysUserEntity.setCompanyId(jwt.getClaim("companyId").asString());
            sysUserEntity.setDeptId(jwt.getClaim("dept_id").asString());
            sysUserEntity.setUserName(jwt.getClaim("username").asString());
            sysUserEntity.setSoleId(jwt.getClaim("soleId").asString());
            return sysUserEntity;
        } catch (JWTDecodeException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public static void main(String[] args) {
    }
}
