package com.junl.crm_common.tools;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * @description: 线程池工具类
 * @author: daiqimeng
 * @date: 2021/6/813:36
 */
@Log4j2
public class ThreadPoolUtil {

    /**
     * 初始化线程池
     */
    private static ThreadPoolExecutor executor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors()*2,
            100,
            2,
            TimeUnit.MINUTES,new LinkedBlockingDeque<>(10000),
            Executors.defaultThreadFactory(),new ThreadPoolExecutor.AbortPolicy());


    /**
     * 执行runnable 返回一个future  布尔值为传输进来的值 传进来什么布尔值 执行成功后就返回什么值
     * @param runnable
     */
    public static void runTask(Runnable runnable) {
        executor.submit(runnable);
    }

    /**
     * 执行callable
     * @param callable
     * @param <T>
     * @return
     */
    public static  <T>Future<T> runTask(Callable<T> callable){
        Future<T> future = executor.submit(callable);
        return future;
    }
    

    /**
     * @describe:  执行多任务 等待所有执行完 结果一起返回
     * <AUTHOR>
     * @date 2021/8/24 16:12
     * @param callableList
     * @param countDownLatch
     * @return: {@link List<T>}
     */
    public static <T> List<T> runTask(List<Callable<T>> callableList,CountDownLatch countDownLatch){
        List<T> list=new ArrayList<>();
        List<Future<T>> futures=new ArrayList<>();
        for (Callable<T> tCallable : callableList) {
            Future<T> submit = executor.submit(tCallable);
            futures.add(submit);
        }
        try {
            countDownLatch.await();
            for (Future<T> future : futures) {
                list.add(future.get());
            }
        }catch (Exception e){
            log.error("多线程执行callable错误 等待异常  :{}",e.getMessage());
        }
        return list;
    }

}
