package com.junl.crm_common.tools;


import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Date;

/**
 * 时间工具类
 */
@Log4j2
public class DateUtils {

    private static SimpleDateFormat simpleFormatter=new SimpleDateFormat("yyyy-MM-dd");



    /**
     * 获取当前时间
     * @return
     */
    public static Date getNow(){
        return new Date();
    }

    /**
     * @describe:  比较第一个日期 和第二个日期相差的天数
     * <AUTHOR>
     * @date 2021/6/1 18:44
     * @param minDay
     * @param maxDay
     * @return: {@link long}
     */
    public static long compareDay(Date minDay,Date maxDay){
        return compareDay(minDay,transition(maxDay));
    }


    /**
     * @describe:  比较第一个日期 和第二个日期相差的天数
     * <AUTHOR>
     * @date 2021/6/1 18:44
     * @param date
     * @param date1
     * @return: {@link long}
     */
    public static long compareDay(Date date,LocalDateTime date1){
        return compareDay(transition(date),date1);
    }

    /**
     * @describe:  比较第一个日期 和第二个日期相差的天数
     * <AUTHOR>
     * @date 2021/6/1 18:44
     * @param date
     * @param date1
     * @return: {@link long}
     */
    public static long compareDay(LocalDateTime date,LocalDateTime date1){
        return compareDay(date.toLocalDate(),date1.toLocalDate());
    }

    /**
     * 计算2个localDate 相差时间
     * @param date
     * @param date1
     * @return
     */
    private static long compareDay(LocalDate date,LocalDate date1){
        return date1.toEpochDay()-date.toEpochDay();
    }

    /**
     * @describe:  计算两个相差秒数
     * <AUTHOR>
     * @date 2021/7/29 18:57
     * @param date 较小的时间
     * @param date1 较大的时间
     * @return: {@link long}
     */
    public static long compareSecond(Date date,Date date1){
         return compareSecond(transition(date),transition(date1));
    }
    public static long compareSecond(LocalDateTime date,LocalDateTime date1){
        Duration between = Duration.between(date, date1);
        return between.getSeconds();
    }




    /**
     * 按照指定的格式格式化时间
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern){
        SimpleDateFormat temp=new SimpleDateFormat(pattern);
        String format = temp.format(date);
        return format;
    }
    /**
     * 按照指定的格式转换时间
     * @param date
     * @param pattern
     * @return
     */
    public static Date parse(String date, String pattern){
        SimpleDateFormat temp=new SimpleDateFormat(pattern);
        Date parse=null;
        try {
            parse= temp.parse(date);
        }catch (ParseException e){
            log.error("时间转换错误： {}",e.getMessage());
            throw new RuntimeException("时间转换错误： "+e.getMessage());
        }
        return parse;
    }


    /**
     * @describe: 默认格式化时间
     * <AUTHOR>
     * @date 2021/6/1 18:28
     * @param date
     * @return: {@link String}
     */
    public static String defaultFormat(Date date){
        return simpleFormatter.format(date);
    }

    /**
     * @describe: 默认时间转换
     * <AUTHOR>
     * @date 2021/6/1 18:27
     * @param date
     * @return: {@link Date}
     */
    public static Date defaultParse(String date){
        Date parse=null;
        try {
            parse= simpleFormatter.parse(date);
        }catch (ParseException e){
            log.error("转换时间错误：{}",e.getMessage());
            throw new RuntimeException("转换时间错误："+e.getMessage());
        }
        return parse;
    }

    /**
     * @describe: 根据给定date 获取最小时间
     * <AUTHOR>
     * @date 2021/6/1 18:18
     * @param date
     * @return: {@link Date}
     */
    public static Date getMin(Date date){
        return transition(getMin(transition(date)));
    }



    /**
     * @describe: 根据给定date 获取最大时间
     * <AUTHOR>
     * @date 2021/6/1 18:22
     * @param date
     * @return: {@link Date}
     */
    public static Date getMax(Date date){
        return transition(getMax(transition(date)));
    }

    /**
     * 获取当天最小时间
     * @return
     */
    public static LocalDateTime getMin(){
        return getMin(LocalDateTime.now());
    }

    /**
     * 获取当天最大时间
     * @return
     */
    public static LocalDateTime getMax(){
        return getMax(LocalDateTime.now());
    }

    /**
     * @describe: 获取一天的最小时间
     * <AUTHOR>
     * @date 2021/6/1 18:11
     * @param localDateTime
     * @return: {@link LocalDateTime}
     */
    public static LocalDateTime getMin(LocalDateTime localDateTime){
        return LocalDateTime.of(localDateTime.getYear(),localDateTime.getMonth(),localDateTime.getDayOfMonth(),0,0,0);
    }
    /**
     * @describe: 获取一天的最大时间
     * <AUTHOR>
     * @date 2021/6/1 18:21
     * @param localDateTime
     * @return: {@link LocalDateTime}
     */
    public static LocalDateTime getMax(LocalDateTime localDateTime){
        return LocalDateTime.of(localDateTime.getYear(),localDateTime.getMonth(),localDateTime.getDayOfMonth(),23,59,59);
    }

    /**
     * @describe: date 转换成LocalDateTime
     * <AUTHOR>
     * @date 2021/6/1 18:16
     * @param date
     * @return: {@link LocalDateTime}
     */
    public static LocalDateTime transition(Date date){
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),ZoneId.systemDefault());
    }
    /**
     * @describe: localDateTime 转换成date
     * <AUTHOR>
     * @date 2021/6/1 18:16
     * @param localDateTime
     * @return: {@link Date}
     */
    public static Date transition(LocalDateTime localDateTime){
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static void main(String[] args) {
        LocalDateTime transition = transition(new Date());
        transition.minusMinutes(0);
        transition.minusSeconds(0);
        Date transition1 = transition(transition);
        System.out.println(format(transition1,"yyyy-MM-dd HH:mm:ss"));
    }


}
