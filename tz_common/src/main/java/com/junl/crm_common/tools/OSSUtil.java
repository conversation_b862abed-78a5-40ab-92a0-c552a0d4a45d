//package com.junl.crm_common.tools;
//
//import com.alibaba.fastjson.JSONObject;
//import com.aliyuncs.DefaultAcsClient;
//import com.aliyuncs.IAcsClient;
//import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
//import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
//import com.aliyuncs.exceptions.ClientException;
//import com.aliyuncs.exceptions.ServerException;
//import com.aliyuncs.http.HttpClientConfig;
//import com.aliyuncs.ocr.model.v20191230.*;
//import com.aliyuncs.profile.DefaultProfile;
//import com.aliyuncs.profile.IClientProfile;
//import com.google.gson.Gson;
//import com.junl.modules.bc.entity.BcLogisticsEntity;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.apache.http.HttpResponse;
//import org.apache.http.util.EntityUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.HashMap;
//import java.util.Map;
//
//
//
///**
// * @program: mms-car-wechat
// * @description: 阿里云短信发送
// * @author: daiqimeng
// * @create: 2020-04-30 16:02
// **/
//@Slf4j
//@Component
//public class OSSUtil {
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    //产品名称:云通信短信API产品,开发者无需替换
//    static final String PRODUCT = "Dysmsapi";
//    //产品域名,开发者无需替换
//    static final String DOMAIN = "dysmsapi.aliyuncs.com";
//
//    // TODO 此处需要替换成开发者自己的AK(在阿里云访问控制台寻找)
//    static final String ACCESSKEY_ID = "LTAI4G5pUZ7KaehaRpzb6v8A";
//    static final String ACCESSKEY_SECRET = "******************************";
//    static final String SIGN_NAME="慧行SaaS";
//
//    /**
//     * 方法描述: 阿里云短信发送
//     * <AUTHOR>
//     * @date 2020/6/5 16:49
//     * @param phone
//     * @param code
//     * @param templateCode
//     * @return  {@link boolean}
//     */
//    public static boolean sendSms(String phone, String code,TemplateCode templateCode){
//        try {
//
//            //可自助调整超时时间
//            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
//            System.setProperty("sun.net.client.defaultReadTimeout", "10000");
//
//            //初始化acsClient,暂不支持region化
//            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", ACCESSKEY_ID, ACCESSKEY_SECRET);
//            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", PRODUCT,DOMAIN);
//
//            HttpClientConfig clientConfig = HttpClientConfig.getDefault();
//
//            IAcsClient acsClient = new DefaultAcsClient(profile);
//
//            //组装请求对象-具体描述见控制台-文档部分内容
//            SendSmsRequest request = new SendSmsRequest();
//            //必填:待发送手机号
//            request.setPhoneNumbers(phone);
//            //必填:短信签名-可在短信控制台中找到
//            request.setSignName(SIGN_NAME);
//            //必填:短信模板-可在短信控制台中找到
//            request.setTemplateCode(templateCode.toString());
//            //可选:模板中的变量替换JSON串
//            request.setTemplateParam("{\"code\":\""+code+"\"}");
//
//            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            //request.setOutId("yourOutId");
//
//            //hint 此处可能会抛出异常，注意catch
//            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
//
//            if (sendSmsResponse.getMessage().equals("OK")) {
//                log.info(sendSmsResponse.getCode());
//                return true;
//            }else{
//                return false;
//            }
//        }catch (Exception e){
//            log.error("*************************短信发送异常:",e);
//            return false;
//        }
//    }
//    /**
//     * 定义枚举模板类
//     * @date 2020/4/30 16:39
//     * <AUTHOR>
//     * @return  {@link null}
//     */
//    public enum TemplateCode{
//        ONE{
//            @Override
//            public String toString() {
//                return "SMS_192150133";
//            }
//        },
//        TWO{
//            @Override
//            public String toString() {
//                return "模板二号";
//            }
//        },
//        THREE{
//            @Override
//            public String toString() {
//                return "模板三号";
//            }
//        }
//
//    }
//
//
//
//}
