package com.junl.crm_common.tools;
import java.awt.BasicStroke;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.Shape;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Hashtable;


import javax.imageio.ImageIO;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.Result;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

/**
 * 二维码生成工具类
 * <AUTHOR>
 */

public class QRCodeUtils {
    public static final String FORMAT_NAME = "PNG";
    // 二维码尺寸
    private static final int QRCODE_SIZE = 300;
    // LOGO宽度
    private static final int WIDTH = 60;
    // LOGO高度
    private static final int HEIGHT = 60;

    private static BufferedImage createImage(String content, String imgPath) throws Exception {
        if (imgPath!=null) {
            FileInputStream fileInputStream=new FileInputStream(imgPath);
            return createImage(content,fileInputStream,Boolean.TRUE);
        }else{
            return createImage(content,null,Boolean.TRUE);
        }

    }


    private static BufferedImage createImage(String content, InputStream imgPath,
                                             boolean needCompress) throws Exception {
        Hashtable<EncodeHintType, Object> hints = new Hashtable<EncodeHintType, Object>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, Charset.defaultCharset().name());
        hints.put(EncodeHintType.MARGIN, 1);

        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
        BitMatrix bitMatrix = multiFormatWriter.encode(content,
                BarcodeFormat.QR_CODE, QRCODE_SIZE, QRCODE_SIZE, hints);


        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(width, height,
                BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000
                        : 0xFFFFFFFF);
            }
        }
        if (imgPath == null) {
            return image;
        }
        // 插入图片
        QRCodeUtils.insertImage(image, imgPath, needCompress);
        return image;
    }

    /**
     * 插入LOGO
     *
     * @param source
     *            二维码图片
     * @param inputStream
     *            LOGO图片流
     * @param needCompress
     *            是否压缩
     * @throws Exception
     */
    private static void insertImage(BufferedImage source, InputStream inputStream,
                                    boolean needCompress) throws Exception {
        Image src = ImageIO.read(inputStream);
        int width = src.getWidth(null);
        int height = src.getHeight(null);
        if (needCompress) { // 压缩LOGO
            if (width > WIDTH) {
                width = WIDTH;
            }
            if (height > HEIGHT) {
                height = HEIGHT;
            }
            Image image = src.getScaledInstance(width, height,
                    Image.SCALE_SMOOTH);
            BufferedImage tag = new BufferedImage(width, height,
                    BufferedImage.TYPE_INT_RGB);
            Graphics g = tag.getGraphics();
            g.drawImage(image, 0, 0, null); // 绘制缩小后的图
            g.dispose();
            src = image;
        }
        // 插入LOGO
        Graphics2D graph = source.createGraphics();
        int x = (QRCODE_SIZE - width) / 2;
        int y = (QRCODE_SIZE - height) / 2;
        graph.drawImage(src, x, y, width, height, null);
        Shape shape = new RoundRectangle2D.Float(x, y, width, width, 6, 6);
        graph.setStroke(new BasicStroke(3f));
        graph.draw(shape);
        graph.dispose();
    }


    /**
     * 获取二维码 返回字节输出流
     * <AUTHOR>
     * @param content
     * @param imgPath
     * @return
     * @throws Exception
     */
    public static ByteArrayOutputStream getEncodeStream(String content,String imgPath)throws Exception {
        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        BufferedImage image = QRCodeUtils.createImage(content, imgPath);
        ImageIO.write(image,FORMAT_NAME,byteArrayOutputStream);
        return byteArrayOutputStream;
    }

    /**
     * 解析二维码  返回String
     * <AUTHOR>
     * @param inputStream
     * @return
     * @throws Exception
     */
    public static String decode(InputStream inputStream) throws Exception{
        BufferedImage image=ImageIO.read(inputStream);
        return getContent(image);
    }

    private static String getContent(BufferedImage image)throws Exception{
        if (image == null) {
            throw new RuntimeException("解析错误，错误的流信息");
        }
        BufferedImageLuminanceSource source = new BufferedImageLuminanceSource(
                image);
        BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
        Result result;
        Hashtable<DecodeHintType, Object> hints = new Hashtable<DecodeHintType, Object>();
        hints.put(DecodeHintType.TRY_HARDER,Boolean.TRUE);
        hints.put(DecodeHintType.CHARACTER_SET, Charset.defaultCharset().name());
        hints.put(DecodeHintType.PURE_BARCODE, Boolean.TRUE);
        result = new MultiFormatReader().decode(bitmap, hints);
        String resultStr = result.getText();
        return resultStr;
    }

    /**
     * 解析二维码  返回String
     * <AUTHOR>
     * @param url
     * @return
     * @throws Exception
     */
    public static String decode(URL url) throws Exception{
        BufferedImage image=ImageIO.read(url);
        return getContent(image);
    }


    public static void main(String[] args) throws Exception {
//        String text = "I Love You ♥♥♥♥";  //这里设置自定义网站url
//        ByteArrayOutputStream encodeStream = getEncodeStream(text, null);
//        byte[] bytes = encodeStream.toByteArray();
//        FileOutputStream fileOutputStream=new FileOutputStream("D:\\123.png");
//        fileOutputStream.write(bytes);
//        fileOutputStream.close();
//        encodeStream.close();


    }
}
