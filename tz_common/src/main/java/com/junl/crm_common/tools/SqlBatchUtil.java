package com.junl.crm_common.tools;


import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * Description: sql 批处理工具
 *
 * <AUTHOR> <PERSON>
 * @date 2023/4/26 20:32
 */
public class SqlBatchUtil {

    static final Logger log = LoggerFactory.getLogger(SqlBatchUtil.class);



    public static <T,M>void execute(List<T> list, Class<M> mapper, insertBatchFace<T,M> function,int batchSize){
        SqlSession sqlSession =null;
        try {

            SqlSessionFactory sqlSessionFactory = SpringUtil.getBean(SqlSessionFactory.class);
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            M mapperDao = sqlSession.getMapper(mapper);

            for(int i=0;i<list.size();i++){
                function.insert(list.get(i),mapperDao);
                if ( i!=0 && i%batchSize == 0 ){
                    sqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
                }
            }

            if (list.size() < batchSize || list.size()%batchSize != 0) {
                sqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
            }
            sqlSession.clearCache();
        }catch (Exception e){
            if (!ObjectUtils.isEmpty(sqlSession)) {
                sqlSession.rollback();
            }
            log.error("批量插入失败: {}",e.getMessage());
        }finally {
            if (!ObjectUtils.isEmpty(sqlSession)) {
                sqlSession.close();
            }
        }
    }

    /**
     * @Description:   批量处理新增 有缺陷 数据量过大时还要考虑指定条数分批处理
     * @param list
     * @param mapper
     * @param function
     * @return {@link Void }
     * <AUTHOR> Dai
     * @date 2023/4/26 20:28
     */
    public  static <T,M> void execute(List<T> list, Class<M> mapper, insertBatchFace<T,M> function){
        execute(list,mapper,function,3000);
    }


    @FunctionalInterface
    public interface insertBatchFace<T,M>{
        public void insert(T r,M t);
    }
}
