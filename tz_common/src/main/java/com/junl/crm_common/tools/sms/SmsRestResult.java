package com.junl.crm_common.tools.sms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2021/8/02 13:04
 */
@Data
public class SmsRestResult {
    @ApiModelProperty("错误代码 0表示成功")
    private int result;

    @ApiModelProperty("错误描述")
    private String description;

    @ApiModelProperty("任务编号")
    private String taskid;

    @ApiModelProperty("失败号码列表")
    private String faillist;

    @ApiModelProperty("任务编号")
    private String task_id;


    public SmsRestResult(int result, String description) {
        this.result = result;
        this.description = description;
    }

    public SmsRestResult(int result,String description,String taskid,String faillist,String task_id) {
        this.result = result;
        this.description = description;
        this.taskid = taskid;
        this.faillist = faillist;
        this.task_id = task_id;
    }

    public SmsRestResult() {

    }


}

