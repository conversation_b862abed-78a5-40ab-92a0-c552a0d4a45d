package com.junl.crm_common.tools;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @description: 文件工具类
 * @author: Mr <PERSON>
 * @date: 2021/3/22 10:06
 */
public class FileUtils {


    /**
     * zip 压缩文件
     * @param file
     * @param urlPath
     */
    public static void zip(File file,String urlPath){
        if(!file.exists()){
            throw new RuntimeException("被压缩的文件不能为空");
        }
        try {
            byte[] bytes=new byte[1024];
            FileInputStream fileInputStream=new FileInputStream(file);
            ZipOutputStream zipOutputStream=new ZipOutputStream(new FileOutputStream(urlPath));
            zipOutputStream.putNextEntry(new ZipEntry(file.getName()));
            int len;
            while ((len=fileInputStream.read(bytes))>0) {
                zipOutputStream.write(bytes,0,len);
            }
            zipOutputStream.closeEntry();
            fileInputStream.close();
            zipOutputStream.close();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 自定义 连续压缩多个包
     */
    static class MyZip extends ZipOutputStream{

        public MyZip(OutputStream out) {
            super(out);
        }

        public MyZip add(String sourceName,byte[] bytes){
            try {
                this.putNextEntry(new ZipEntry(sourceName));
                this.write(bytes,0, bytes.length);
            }catch (Exception e){
                e.printStackTrace();
            }
           return this;
        }
    }
} 