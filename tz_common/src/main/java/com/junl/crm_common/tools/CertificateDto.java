//package com.junl.crm_common.tools;
//
//import lombok.Getter;
//import lombok.Setter;
//import sun.security.x509.X500Name;
//
//import java.io.IOException;
//
///**
// * @description: 证书颁发与领取实体定义
// * @author: Mr Dai
// * @date: 2021/7/1 20:59
// */
//@Getter
//@Setter
//public class CertificateDto {
//
//    private CertificateDto(){}
//
//    //通用  实体名称
//    private String CN;
//    //组织单位
//    private String OU;
//    //组织机构
//    private String O;
//    //城市
//    private String L;
//    //省
//    private String S;
//    //国家
//    private String C;
//
//    @Override
//    public String toString(){
//        StringBuilder builder=new StringBuilder();
//        builder.append("CN=").append(this.CN).append(",")
//                .append("OU=").append(this.OU).append(",")
//                .append("O=").append(this.O).append(",")
//                .append("L=").append(this.L).append(",")
//                .append("S=").append(this.S).append(",")
//                .append("C=").append(this.C);
//        return builder.toString();
//    }
//
//    /**
//     * 获取默认的对象
//     * @return
//     */
//    public static CertificateDto getDefault(){
//        CertificateDto certificate=new CertificateDto();
//        certificate.setCN("DQM");
//        certificate.setC("China");
//        certificate.setL("Beijing");
//        certificate.setS("Beijing");
//        certificate.setO("Mafia");
//        certificate.setOU("butcher");
//        return certificate;
//    }
//
//    /**
//     * 以CertificateDto 对象为基础 构建的x500对象
//     * @return
//     */
//    public X500Name getX500Name(){
//        try {
//            X500Name x500Name=new X500Name(this.CN,
//                    this.OU,
//                    this.O,
//                    this.L,
//                    this.S,
//                    this.C);
//            return x500Name;
//        } catch (IOException e) {
//            throw new RuntimeException(e.getMessage());
//        }
//    }
//
//}