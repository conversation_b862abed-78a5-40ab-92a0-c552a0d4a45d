package com.junl.crm_common.tools.sms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyuncs.exceptions.ClientException;
import com.junl.crm_common.constant.DySmsEnum;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_common.tools.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.junl.crm_common.status.RedisKey.PHONE_RECORD;


/**
 * Created on 21/8/2.
 * 备注:Demo工程编码采用UTF-8
 * 一信通平台能力接口规范
 */
@Component
@Slf4j
public class TzSmsHelper {


    //短信平台-企业编号
    @Value("${smsapi.spCode}")
    private String spCode = null;
    //短信平台-用户名称
    @Value("${smsapi.loginName}")
    private String loginName=null;
    //短信平台-用户密码
    @Value("${smsapi.password}")
    private String password =null;

    // 消息协议
    private final String SMS_SEAD_URL="http://sms.api.ums86.com:8899/sms/Api/Send.do";





    /**
     * <AUTHOR>
     * @Description 发短信
     * @Date 2021/8/14 21:27
     * @Param [smsSendParam:UserNumber:手机号码(多个号码用”,”分隔)，最多1000个号码，
     * MessageContent：短信内容, 最大402个字或字符（短信内容要求的编码为gb2312或gbk）,
     * dySmsEnum：LOGIN_TEMPLATE_CODE：登录获取验收码 ]
     * @return  {“result=错误代码&description=错误描述&faillist=失败号码列表”}
     * 成功示例：result=0&description=发送短信成功
     **/
    public SmsRestResult sendSms(SmsSendParam smsSendParam,DySmsEnum dySmsEnum){
        if (!SpringUtil.isProd()) {
            String content = getContent(dySmsEnum, smsSendParam.getMessageContent());
            log.info("短信内容是: {}",content);
            return new SmsRestResult(0,"发送成功");
        }
        SmsRestResult body  = new SmsRestResult(20,"系统错误");
//        RedisUtils redisUtils = SpringUtil.getBean(RedisUtils.class);
//
//        if (!redisUtils.hasKey(PHONE_RECORD+smsSendParam.getUserNumber())) {
//            //记录保存到凌晨1点左右
//            redisUtils.set(PHONE_RECORD+smsSendParam.getUserNumber(),"0",25- LocalDateTime.now().getHour(), TimeUnit.HOURS);
//        }

//        long count=redisUtils.increment(PHONE_RECORD+smsSendParam.getUserNumber());

        try{
            HttpClient httpclient = new HttpClient();
            //String strCount = "您的验证码为：123456（有效期为5分钟），请不要把验证码泄露给其他人！";
            PostMethod post = new PostMethod("http://sms.api.ums86.com:8899/sms/Api/Send.do");
            post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"gbk");
            post.addParameter("SpCode", spCode);
            post.addParameter("LoginName", loginName);
            post.addParameter("Password", password);
            post.addParameter("MessageContent", getContentMes(dySmsEnum,smsSendParam.getMessageContent()));
            post.addParameter("UserNumber", smsSendParam.getUserNumber());
            post.addParameter("SerialNumber", "");
            post.addParameter("ScheduleTime", "");
            post.addParameter("ExtendAccessNum", "");
            post.addParameter("f", "1");
            httpclient.executeMethod(post);
            String info = new String(post.getResponseBody(),"gbk");
            String[] split = info.split("&");
            Map<String,Object> map = new HashMap<String, Object>(split.length);
            for (String key : split) {
                String[] splitd =key.split("=");
                String keys = splitd[0];
                String kvalue ="";
                if(splitd.length>1) {
                    kvalue = splitd[1];
                }
                map.put(keys,kvalue);
            }
            body = JSONObject.parseObject(JSON.toJSONString(map),SmsRestResult.class);
            log.info("短信发送成功"+body);
        }catch (Exception e) {
            body.setDescription(e.getMessage());
            log.error("*************************短信发送异常:",e);
        }
        return  body;
    }


    public String getContent(DySmsEnum dySmsEnum,String param) {
        switch (dySmsEnum) {
            case LOGIN_TEMPLATE_CODE:
                return "【中海油泰州石化】【泰石竞拍】"+param+"，在5分钟内有效。如非本人操作请忽略本短信。";
            case FORGET_PASSWORD_TEMPLATE_CODE:
                return "【中海油泰州石化】【泰石竞拍】"+param+"，在5分钟内有效。如非本人操作请忽略本短信。";
        }
        return "xxxx";
    }

    public String getContentMes(DySmsEnum dySmsEnum,String param) {
        switch (dySmsEnum) {
            case LOGIN_TEMPLATE_CODE:
                return "【中海油泰州石化】MES【泰石竞拍】"+param+"，在5分钟内有效。如非本人操作请忽略本短信。";
            case FORGET_PASSWORD_TEMPLATE_CODE:
                return "【中海油泰州石化】MES【泰石竞拍】"+param+"，在5分钟内有效。如非本人操作请忽略本短信。";
        }
        return "xxxx";
    }
}
