package com.junl.crm_common.tools;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;


/**
 * Redis工具类
 *
 * <AUTHOR>
 * @email
 * @date 2021-05-20 11:09
 */
@Component
public class RedisUtils {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private ValueOperations<String, String> valueOperations;
    @Autowired
    private HashOperations<String, String, Object> hashOperations;
    @Autowired
    private ListOperations<String, Object> listOperations;
    @Autowired
    private SetOperations<String, Object> setOperations;
    @Autowired
    private ZSetOperations<String, Object> zSetOperations;

    /**           ****************************
     *                  String 操作
     *            ****************************
     */

    public void set(String key,String value){
        valueOperations.set(key,value);
    }

    /**
     * 设置过期时间 单位秒
     * @param key
     * @param value
     * @param time
     */
    public void set(String key,String value,long time){
        valueOperations.set(key,value,time,TimeUnit.SECONDS);
    }
    public void set(String key,String value,long time,TimeUnit timeUnit){
        valueOperations.set(key,value,time,timeUnit);
    }

    public String get(String key){

        return valueOperations.get(key);
    }

    /**
     * key 自增1   value  必须是数值类型 不然报错
     * @param key
     * @return
     */
    public long increment(String key){
        return valueOperations.increment(key,1);
    }

    /**
     * 获取 key 的过期剩余时间
     * @param key
     * @return
     */
    public long getExpire(String key){
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 共用的删除方法
     * @param key
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }



    /**           ****************************
     *                  hash 操作
     *            ****************************
     */

    /**
     * 方法描述: hash数据存值
     * @date 2020/5/6 10:18
     * @param key key值
     * @param field 属性key值
     * @param value 属性value值
     * @return  {@link Void}
     */
    public void hSet(String key,String field,Object value){
        hashOperations.put(key,field,value);
    }

    /**
     * @Description:  判断hash结构该值是否存在
     * @param key
     * @param field
     * @return {@link Void }
     * <AUTHOR> Dai
     * @date 2024/4/23 17:15
     */
    public boolean hashKey(String key,String field){
        return hashOperations.hasKey(key,field);
    }

    /**
     * 方法描述:hash 数据取值
     * @date 2020/5/6 10:19
     * @param key 取的key
     * @param field 取的属性key
     * @return  {@link Object}
     */
    public Object hGet(String key,String field){
        return hashOperations.get(key, field);
    }
    /**
     * 方法描述: 指定值加1
     * <AUTHOR>
     * @date 2020/7/3 17:01
     * @param key
     * @param field
     * @return  {@link Long}
     */
    public Long hIncrement(String key,String field){
        return incrementOrDecrement(key,field,1l);
    }

    private Long incrementOrDecrement(String key,String field,Long value){
        Long result = hashOperations.increment(key, field, value);
        return result;
    }

    public Long hDecrement(String key,String field){
        return incrementOrDecrement(key,field,-1l);
    }

    /**
     * 方法描述:查询关联key的全部value          field  value
     * @date 2020/5/6 10:20      例  key   field  value
     * @param key 取值的key值               field  value
     * @return  {@link Set< String>}
     */
    public Set<String> hGetAll(String key){
        Set<String> keys = hashOperations.keys(key);
        List<Object> objects = hashOperations.multiGet(key, keys);
        keys.clear();
        objects.forEach(x->keys.add((String) x));
        return keys;
    }
    /**
     * 方法描述:查询关联的key  所有的 field value
     * @date 2020/12/16 20:32               field  value
     * @param key             例：   key    field  value
     * <AUTHOR>                          field  value
     * @return  {@link Map< String, Object>}
     */
    public Map<String,Object> hGetAllMap(String key){
        Map<String,Object> map=new HashMap<>();
        Set<String> keys = hashOperations.keys(key);
        keys.forEach(x->{
            Object o = hGet(key, x);
            map.put(x, o);
        });
        return map;
    }


    /**
     * 方法描述: 如果不存在 再放入值
     * @date 2020/5/6 10:27
     * @param key key值
     * @param field 属性key值
     * @param value 属性value值
     * @return  {@link boolean}
     */
    public boolean ifSet(String key,String field,String value){
        return hashOperations.putIfAbsent(key,field,value);
    }
    /**
     * 方法描述: 删除key中指定属性的值
     * @date 2020/5/6 10:47
     * @param key
     * @param field
     * @return  {@link boolean}
     */
    public boolean hDel(String key,String... field){
        if (hashOperations.delete(key, field)>0) {
            return true;
        }
        return false;
    }



    /**
     * 方法描述: 盘点哈希表中是否已经存在该值
     * <AUTHOR>
     * @date 2020/5/9 9:46
     * @param key
     * @param field
     * @return  {@link boolean}
     */
    public boolean ifHash(String key,String field){
       return hashOperations.hasKey(key,field);
    }

    /**
     * 改变hash中的某个field中的value 值
     * 由于Redis模板没有提供这个api  采用的是删除再增加达到改变的效果
     * @param key
     * @param field
     * @param value
     */
    public void hAlter(String key,String field,String value){
        hDel(key,field);
        hSet(key,field,value);
    }

    /**  ***************************
     *                                     *
     *       list操作                      *
     */
    /**
     * 方法描述:往集合里面放入集合数据 从左边开始
     * <AUTHOR>
     * @date 2020/5/20 18:30
     * @param list
     * @param key
     * @return  {@link boolean}
     */
    public boolean lSetLeftAll(String key,List<?> list){
        Long car = listOperations.leftPushAll(key, list.toArray());
        if(car>0){
            return true;
        }
        return false;
    }

    /**
     * 方法描述:往集合里面放入集合数据 从右边开始
     * <AUTHOR>
     * @date 2020/5/20 18:30
     * @param list
     * @param key
     * @return  {@link boolean}
     */
    public boolean lSetRightAll(String key,List<?> list){
        Long car = listOperations.rightPushAll(key, list.toArray());
        if(car>0){
            return true;
        }
        return false;
    }
    /**
     * 方法描述:取出集合里面该key的所有数据
     * <AUTHOR>
     * @date 2020/5/20 18:37
     * @param key
     * @return  {@link List< Object>}
     */
    public List<Object> lGetAll(String key){
        List<Object> car =listOperations.range(key, 0, -1);
        return car;
    }

    /**
     * 方法描述:取出集合里面下标位置的所有值
     * <AUTHOR>
     * @date 2020/5/20 18:37
     * @param key
     * @return  {@link List< Object>}
     */
    public List<Object> lGetIndex(String key,Integer start,Integer end){
        List<Object> car =listOperations.range(key, start, end);
        return car;
    }

    /**
     * 方法描述: 删除集合中对应的key  左边的值
     * <AUTHOR>
     * @date 2020/5/21 15:11
     * @param key
     * @return  {@link boolean}
     */
    public boolean lRemoveLeft(String key){
        Object o = listOperations.leftPop(key);

        if(o!=null){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 方法描述: 删除集合中对应的key  右边的值
     * <AUTHOR>
     * @date 2020/5/21 15:11
     * @param key
     * @return  {@link boolean}
     */
    public boolean lRemoveRight(String key){
        Object o = listOperations.rightPop(key);
        if(o!=null){
            return true;
        }else{
            return false;
        }
    }
    /**  ***********************
     *                                   *
     *     无序集合操作Set                    *
     */


    /**
     * 方法描述:往无序集合中放入一个或者多个值
     * <AUTHOR>
     * @date 2020/5/21 15:30
     * @param key
     * @param value
     * @return  {@link boolean}
     */
    public boolean sSet(String key ,String... value){
        Long add = setOperations.add(key, value);
        if(add>0){
            return true;
        }
        return false;
    }
    /**
     * 方法描述:从无序集合中删除一个或多个值
     * <AUTHOR>
     * @date 2020/5/21 15:31
     * @param key
     * @param value
     * @return  {@link boolean}
     */
    public boolean sRemove(String key,String... value){
        Long remove = setOperations.remove(key, value);
        if(remove>0){
            return true;
        }
        return false;
    }
    /**
     * 方法描述: 集合中随机弹出指定个数元素
     * <AUTHOR>
     * @date 2020/5/21 15:32
     * @param key
     * @return  {@link Object}
     */
    public List<Object> sRandomRemove(String key,long count){
        return setOperations.pop(key,count);
    }

    /**
     * 方法描述: 获取集合长度
     * <AUTHOR>
     * @date 2020/5/21 15:34
     * @param key
     * @return  {@link long}
     */
    public long sGetSize(String key){
        return setOperations.size(key);
    }

    /**
     * 方法描述: 获取集合中的所有元素
     * <AUTHOR>
     * @date 2020/5/21 15:37
     * @param key
     * @return  {@link Set< Object>}
     */
    public Set<Object> sGetAll(String key){
        return setOperations.members(key);
    }

    /**              ****************
     *    ZSET  有序集合操作                    *
     */

    /**
     * 方法描述: 添加一个集合 并设置权重  如果存在 当前权重加1
     * <AUTHOR>
     * @date 2020/5/21 15:44
     * @param key
     * @param value
     * @param sort
     * @return  {@link boolean}
     */
    public boolean zAdd(String key,String value,Integer sort){
        Double score = zSetOperations.score(key, value);
        if(score!=null){
            sort=score.intValue()+1;
        }
        return zSetOperations.add(key, value, sort);
    }


    /**
     * 方法描述: 获取集合中的元素  指定区间
     * <AUTHOR>
     * @date 2020/5/21 15:46
     * @param key
     * @param start
     * @param end
     * @return  {@link Set< Object>}
     */
    public Set<Object> zRange(String key, long start, long end){
        Set<Object> range = zSetOperations.range(key, start, end);
        return range;
    }
    /**
     * 方法描述:删除有序集合中的一个或多个值
     * <AUTHOR>
     * @date 2020/5/21 15:49
     * @param key
     * @param value
     * @return  {@link boolean}
     */
    public boolean zRemove(String key,String... value){
        zSetOperations.remove(key, value);
        return true;
    }
    /**
     * 方法描述: 删除集合中指定位置的值
     * <AUTHOR>
     * @date 2020/6/15 15:46
     * @param key
     * @param start
     * @param end
     * @return  {@link boolean}
     */
    public boolean zRemoveRange(String key,long start,long end){
        zSetOperations.removeRange(key, start, end);
        return true;
    }

    /**
     * 方法描述: 返回该元素在集合中的下标位置
     * <AUTHOR>
     * @date 2020/5/21 15:51
     * @param key
     * @param value
     * @return  {@link long}
     */
    public long zRank(String key,String value){
        return zSetOperations.rank(key,value);
    }

    /**
     * 方法描述:获取有序集合长度
     * <AUTHOR>
     * @date 2020/6/15 15:28
     * @param key
     * @return  {@link long}
     */
    public long zSize(String key){
        return zSetOperations.size(key);
    }

}
