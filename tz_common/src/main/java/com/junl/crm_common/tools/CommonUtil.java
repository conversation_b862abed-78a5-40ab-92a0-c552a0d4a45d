package com.junl.crm_common.tools;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.function.Function;

/**
 * 常用的一些工具类
 * <AUTHOR>
 */
public class CommonUtil {

    /**
     *  给一个集合  收集集合中的某个字段
     *  例子:
     *  List<Studnet> list;
     *  Student{
     *      int age;
     *      string name;
     *  }
     *
     * collect(list,Student::getName)  返回  List<String>
     *
     * @param list
     * @param function
     * @param <T>
     * @param <R>
     * <AUTHOR>
     */
    public static  <T,R>List<R> collect(Collection<T> list, Function<T,R> function){
        List<R> r=new ArrayList<>();
        if(list!=null&&list.size()>0){
            for (T t : list) {
                R apply = function.apply(t);
                r.add(apply);
            }
        }
        return r;
    }

    /**
     * 收集集合中的T的属性  并且给予一个符号串联起来 返回String
     * 例：List<Studnet> list;
     *      *  Student{
     *      *      int age;
     *      *      string name;
     *      *  }
     *      collect(list,Student::getName,"&")  返回 name&name&name
     *
     *
     * @param list
     * @param function
     * @param symbol
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T,R>String collect(Collection<T> list,Function<T,R> function,String symbol){
        StringJoiner joiner=new StringJoiner(symbol);
        if (list!=null&list.size()>0) {
            for (T t : list) {
                R apply = function.apply(t);
                joiner.add(apply+"");
            }
        }
       return joiner.toString();
    }


    public static String getImgBase64(ByteArrayOutputStream byteArrayOutputStream){
        byte[] bytes = byteArrayOutputStream.toByteArray();
        Base64.Encoder encoder = Base64.getEncoder();
        String s = encoder.encodeToString(bytes);
        return "data:image/png;base64,"+s;
    }

    public static String getExcelBase64(ByteArrayOutputStream byteArrayOutputStream){
        byte[] bytes = byteArrayOutputStream.toByteArray();
        String s = Base64.getEncoder().encodeToString(bytes);
        return "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+s;
    }




}
