package com.junl.crm_common.tools.excel;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/6/2212:45
 */

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.*;
import lombok.Data;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.Date;

/**
 * 示例实体类
 */
@Data
//行高
@ContentRowHeight(30)
//头部行高
@HeadRowHeight(20)
//列宽  放在字段上就是单独字段列宽
@ColumnWidth(30)
// 头背景设置成红色 类上全部头信息 字段上 单独字段的头部 注意：只设置头部
/**
 * 颜色取值参考:
 * @see IndexedColors
 */
@HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND,fillForegroundColor = 10)
// 头字体设置成20  字体大小设置
@HeadFontStyle(fontHeightInPoints = 20)
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)
@SheetName("学生表")
public class Student{
    //统一表头 多个
    @ExcelProperty
    //单个表头
    private String string;
    @ExcelProperty
    @DateTimeFormat("yyyy-MM-dd")
    private Date date;
    @ExcelProperty(index = 3)
    @NumberFormat("#.##%")
    private Double doubleData;

    //指定列位置

    @ExcelProperty
    private String name;

    @ExcelProperty(converter = SexConverter.class)
    private Integer sex;
    /**
     * 忽略这个字段
     */
    private String ignore;
}
