package com.junl.crm_common.constant;

/**
 * @ClassName BiddingSequenceStatusEnum
 * @Description 竞价模式标识
 * @Auther: chenlong
 * @Date: 2021/09/01/16:25
 */
public enum BiddingModeTypeEnum {

    /**
     * 竞价竞量模式
     */
    VOLUME_COUNT(1, "竞价竞量模式"),

    /**
     * 零销模式
     */
    RETAIL_COUNT(2, "零销模式"),

    /**
     * 纯竞价模式
     */
    PURE_PRICE(3, "纯竞价模式"),

    /**
     * 定价模式
     */
    SET_PRICE(4, "定价模式");



    /**
     * 状态值
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 构造函数
     * @param code 值
     * @param desc 描述
     */
    BiddingModeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据值枚举
     * @param code 值
     */
    public static BiddingModeTypeEnum getBiddingModeTypeEnumEnum(Integer code) {
        for (BiddingModeTypeEnum biddingModeTypeEnum: BiddingModeTypeEnum.values()) {
            if (code.equals(biddingModeTypeEnum.getCode())) {
                return biddingModeTypeEnum;
            }
        }
        return null;
    }
}
