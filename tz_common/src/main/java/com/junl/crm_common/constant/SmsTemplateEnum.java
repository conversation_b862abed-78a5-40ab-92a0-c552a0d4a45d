package com.junl.crm_common.constant;

/**
 * @ClassName AchievementTypeEnum
 * @Description  短信模板方式
 * @Auther: chenlong
 * @Date: 2021/07/05/18:25
 */
public enum SmsTemplateEnum {


    /**
     * 登录模板
     */
    SMS_TPL_LOGIN(1, "登录模板"),

    /**
     * 注册模板
     */
    SMS_TPL_REGISTER(2, "注册模板"),

    /**
     * 忘记密码模板
     */
    SMS_TPL_FORGET_PASSWORD(3, "忘记密码模板"),

    /**
     * 变更手机号模板
     */
    SMS_TPL_CHANGE(4, "修改手机号模板");


    /**
     * 状态值
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 构造函数
     * @param code 值
     * @param desc 描述
     */
    SmsTemplateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据值枚举
     * @param code 值
     */
    public static SmsTemplateEnum getFileModuleTypeEnumByCode(Integer code) {
        for (SmsTemplateEnum fileModuleTypeEnum: SmsTemplateEnum.values()) {
            if (code.equals(fileModuleTypeEnum.getCode())) {
                return fileModuleTypeEnum;
            }
        }
        return null;
    }
}
