package com.junl.crm_common.constant;

/**
 * @ClassName BiddingSequenceStatusEnum
 * @Description 竞价轮次状态
 * @Auther: chenlong
 * @Date: 2021/06/05/16:25
 */
public enum BiddingSequenceAppStatusEnum {

    /**
     * 待竞价
     */
    WAITING(1, 3,"待竞价"),

    /**
     * 竞价中
     */
    UNDERWAY(2,4, "竞价中"),

    /**
     * 竞价结束
     */
    END(3, 5,"竞价结束");



    /**
     * 状态值
     */
    private Integer code;

    /**
     * 库中值状态
     */
    private int dataStatus;
    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 构造函数
     * @param code 值
     * @param desc 描述
     */
    BiddingSequenceAppStatusEnum(Integer code,Integer dataStatus, String desc) {
        this.code = code;
        this.dataStatus =dataStatus;
        this.desc = desc;
    }

    /**
     * 根据值枚举
     * @param code 值
     */
    public static BiddingSequenceAppStatusEnum getBiddingSequenceAppStatusEnum(Integer code) {
        for (BiddingSequenceAppStatusEnum flagStatusEnum: BiddingSequenceAppStatusEnum.values()) {
            if (code.equals(flagStatusEnum.getCode())) {
                return flagStatusEnum;
            }
        }
        return null;
    }
}
