package com.junl.crm_common.constant;

/**
 * @ClassName BiddingSequenceStatusEnum
 * @Description 竞价轮次状态
 * @Auther: chenlong
 * @Date: 2021/06/05/16:25
 */
public enum InquiryStatusEnum {

    /**
     * 待竞价
     */
    WAITING(2, "待竞价"),

    /**
     * 竞价中
     */
    UNDERWAY(3, "竞价中"),

    /**
     * 竞价结束
     */
    END(4, "竞价结束");



    /**
     * 状态值
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 构造函数
     * @param code 值
     * @param desc 描述
     */
    InquiryStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据值枚举
     * @param code 值
     */
    public static InquiryStatusEnum getBiddingSequenceStatusEnum(Integer code) {
        for (InquiryStatusEnum flagStatusEnum: InquiryStatusEnum.values()) {
            if (code.equals(flagStatusEnum.getCode())) {
                return flagStatusEnum;
            }
        }
        return null;
    }
}
