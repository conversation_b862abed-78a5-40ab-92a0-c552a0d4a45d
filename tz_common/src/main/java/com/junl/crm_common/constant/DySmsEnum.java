package com.junl.crm_common.constant;


import com.alibaba.druid.util.StringUtils;

public enum DySmsEnum {
	
	LOGIN_TEMPLATE_CODE("SMS_159783259","杭州数式-登录获取验收码","code"),
	CHANGE_PHONE_TEMPLATE_CODE("SMS_159783259","杭州数式-修改手机获取验收码","code"),
	FORGET_PASSWORD_TEMPLATE_CODE("SMS_159783259","杭州数式-忘记密码获取验收","code"),
	REGISTER_TEMPLATE_CODE("SMS_159783259","杭州数式-注册获取验收","code"),
	DELIVERY_LOG_TEMPLATE_CODE("SMS_186380473","杭州数式","code");

	/**
	 * 短信模板编码
	 */
	private String templateCode;
	/**
	 * 签名
	 */
	private String signName;
	/**
	 * 短信模板必需的数据名称，多个key以逗号分隔，此处配置作为校验
	 */
	private String keys;
	
	private DySmsEnum(String templateCode, String signName, String keys) {
		this.templateCode = templateCode;
		this.signName = signName;
		this.keys = keys;
	}
	
	public String getTemplateCode() {
		return templateCode;
	}
	
	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}
	
	public String getSignName() {
		return signName;
	}
	
	public void setSignName(String signName) {
		this.signName = signName;
	}
	
	public String getKeys() {
		return keys;
	}

	public void setKeys(String keys) {
		this.keys = keys;
	}

	public static DySmsEnum toEnum(String templateCode) {
		if(StringUtils.isEmpty(templateCode)){
			return null;
		}
		for(DySmsEnum item : DySmsEnum.values()) {
			if(item.getTemplateCode().equals(templateCode)) {
				return item;
			}
		}
		return null;
	}
}

