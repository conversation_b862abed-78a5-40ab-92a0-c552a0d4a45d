package com.junl.crm_common.constant;

/**
 * @ClassName FlagStatusEnum
 * @Description 标识状态枚举模块
 * @Auther: chenlong
 * @Date: 2021/06/05/16:25
 */
public enum FlagStatusEnum {

    /**
     * 正常
     */
    PRODUCT(0, "正常"),

    /**
     * 禁用
     */
    LEADS(1, "禁用");



    /**
     * 状态值
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 构造函数
     * @param code 值
     * @param desc 描述
     */
    FlagStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据值枚举
     * @param code 值
     */
    public static FlagStatusEnum getFlagStatusEnum(Integer code) {
        for (FlagStatusEnum flagStatusEnum: FlagStatusEnum.values()) {
            if (code.equals(flagStatusEnum.getCode())) {
                return flagStatusEnum;
            }
        }
        return null;
    }
}
