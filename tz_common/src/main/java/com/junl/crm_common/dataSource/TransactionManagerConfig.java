package com.junl.crm_common.dataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * @description:
 * @author: Mr <PERSON>
 * @date: 2021/6/10 22:50
 */
@Configuration
public class TransactionManagerConfig {

    @Bean(name = "transactionManager")
    public DataSourceTransactionManager dataSourceTransactionManagerOne(@Autowired DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

} 