package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 销售计划
 * @author:      daiqimeng
 **/
@ApiModel(description = "销售计划")
@TableName("sel_plan")
@Data
@Accessors(chain = true)
public class SelPlanEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    @NotNull
    private String id;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "公司名称")
    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(notes = "计划编码",allowEmptyValue = true, required = false)
    private String planCode;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式",allowEmptyValue = true, required = false)
    @NotNull
    private Integer modeType;

    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private Integer planStatus;

    @ApiModelProperty(notes = "计划名称",allowEmptyValue = true, required = false)
    @NotNull
    private String planName;

    @ApiModelProperty(notes = "周期开始时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd")
    @NotNull
    private Date planStartDate;

    @ApiModelProperty(notes = "周期结束时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd")
    @NotNull
    private Date planEndTime;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "是否删除0-否1-是",allowEmptyValue = true, required = false)
    private Integer isDelete;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "期数")
    private Integer periodNum;

    @TableField(exist = false)
    private String createName;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "编辑人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "编辑时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty("计划商品")
    @NotNull
    private List<SelPlanProductDetailEntity> productList;

}
