package com.junl.crm_common.pojo.work;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;



import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * @description: 审批记录竞价企业交易成功表
 * @author:      daiqimeng
 **/
@TableName("flow_sel_bidding_success")
@Data
public class FlowSelBiddingSuccessEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    private String underwayId;

    private String biddingId;

    private String sequenceId;

    private String offerId;

    @TableField(exist = false)
    private String offerName;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String createBy;

    private String updateBy;

    @TableField(exist = false)
    private List<FlowSelBiddingSuccessProductEntity> products;

}
