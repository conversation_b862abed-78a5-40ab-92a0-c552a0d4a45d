package com.junl.crm_common.pojo.work;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;


import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 微信客户表
 * @author: daiqimeng
 **/
@ApiModel(description = "微信客户表")
@TableName("wx_customer")
@Data
public class WxCustomerEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键", allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "公共平台唯一标识", allowEmptyValue = true, required = false)
    private String unionId;

    @ApiModelProperty(notes = "用户微信唯一标识", allowEmptyValue = true, required = false)
    private String openId;

    @ApiModelProperty(notes = "微信昵称", allowEmptyValue = true, required = false)
    private String nickName;

    @ApiModelProperty(notes = "微信头像", allowEmptyValue = true, required = false)
    private String headImg;

    @ApiModelProperty(notes = "性别 1=男性；2=女性；3=未知", allowEmptyValue = true, required = false)
    private Integer sex;

    @ApiModelProperty(notes = "国家", allowEmptyValue = true, required = false)
    private String country;

    @ApiModelProperty(notes = "省", allowEmptyValue = true, required = false)
    private String province;

    @ApiModelProperty(notes = "市", allowEmptyValue = true, required = false)
    private String city;

    @ApiModelProperty(notes = "真实姓名", allowEmptyValue = true, required = false)
    private String realName;

    @ApiModelProperty(notes = "电话号码", allowEmptyValue = true, required = false)
    private String phone;

    @ApiModelProperty(notes = "获取方式 1：微信小程序，2：公众号", allowEmptyValue = true, required = false)
    private Integer registerMode;

    @ApiModelProperty(notes = "创建人", allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "创建时间", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "编辑人", allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "编辑时间", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
