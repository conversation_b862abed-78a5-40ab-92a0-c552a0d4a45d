package com.junl.crm_common.pojo.work;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR> <PERSON>
 * @date 2024/3/25 11:07
 */
@Setter
@Getter
@Accessors(chain = true)
@TableName("sms_record")
public class SmsRecordEntity {

    @TableId(type=IdType.AUTO)
    private String id;

    private String phone;

    private Integer stage;

    private String des;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
