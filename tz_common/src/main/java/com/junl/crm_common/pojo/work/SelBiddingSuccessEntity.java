package com.junl.crm_common.pojo.work;
import java.lang.String;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;

/**
* @description: 竞价企业交易成功表
* @author:      daiqimeng
**/
@ApiModel(description = "竞价企业交易成功表")
@TableName("sel_bidding_success")
@Data
public class SelBiddingSuccessEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "竞价id",allowEmptyValue = true, required = false)
    private String biddingId;

    @ApiModelProperty(notes = "轮次id",allowEmptyValue = true, required = false)
    private String sequenceId;

    @ApiModelProperty("报价人")
    private String offerId;

    @ApiModelProperty("报价企业名称")
    @TableField(exist = false)
    private String offerName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;


    @ApiModelProperty(notes = "场次",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer sequence;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date planStartDate;
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date planEndTime;

    @ApiModelProperty(notes = "竞价编码",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String biddingCode;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer modeType;

    @ApiModelProperty(notes = "周期数",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer periodNum;

    @ApiModelProperty(notes = "场次数",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer roundNum;

    @ApiModelProperty(notes = "竞价名称",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String planName;

    @ApiModelProperty(notes = "成功商品列表")
    @TableField(exist = false)
    private List<SelBiddingSuccessProductEntity> successList;


}
