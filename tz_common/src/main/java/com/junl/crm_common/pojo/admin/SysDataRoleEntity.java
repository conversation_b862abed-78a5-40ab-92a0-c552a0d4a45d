package com.junl.crm_common.pojo.admin;
import java.lang.String;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

/**
* @description: 数据权限关联表
* @author:      daiqimeng
**/
@ApiModel(description = "数据权限关联表")
@TableName("sys_data_role")
@Data
public class SysDataRoleEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "数据权限主表Id",allowEmptyValue = true, required = false)
    private String dataId;

    @ApiModelProperty(notes = "部门id(按部门维度区分数据权限)",allowEmptyValue = true, required = false)
    private String deptId;

    @ApiModelProperty(notes = "部门名称")
    @TableField(exist = false)
    private String deptName;

}
