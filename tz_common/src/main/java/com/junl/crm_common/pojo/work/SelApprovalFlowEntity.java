package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 审批流程进度表
 * @author:      daiqimeng
 **/
@ApiModel(description = "审批流程进度表")
@TableName("sel_approval_flow")
@Data
public class SelApprovalFlowEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "进行中的审批流程主健",allowEmptyValue = true, required = false)
    private String underwayId;

    @ApiModelProperty(notes = "审批人",allowEmptyValue = true, required = false)
    private String approver;

    @ApiModelProperty(notes = "0未审核 同意1  2驳回",allowEmptyValue = true, required = false)
    private String result;

    @ApiModelProperty(notes = "父节点 *表示开始节点")
    private String parentId;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "节点名称")
    private String panelName;

    @ApiModelProperty(notes = "审批人名称")
    private String approverName;

    @ApiModelProperty(notes = "下个节点")
    @TableField(exist = false)
    private SelApprovalFlowEntity child;

    @TableField(exist = false)
    @ApiModelProperty(notes = "审批时长")
    private String time;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
