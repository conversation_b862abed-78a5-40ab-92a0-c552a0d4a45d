package com.junl.crm_common.pojo.work;
import java.lang.String;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
* @description: 竞价序号关联所选企业表
* @author:      daiqimeng
**/
@TableName("flow_sel_bidding_customer")
@Data
public class FlowSelBiddingCustomerEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    private String sequence;

    private String customerId;

    @TableField(exist = false)
    private String customerName;

    private String detailAddress;

    private String remark;

    private String isBidding;

    private String isSuccess;

}
