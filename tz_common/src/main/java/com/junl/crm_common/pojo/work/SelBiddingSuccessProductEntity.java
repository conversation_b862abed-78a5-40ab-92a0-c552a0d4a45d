package com.junl.crm_common.pojo.work;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* @description: 竞价企业交易成功表
* @author:      daiqimeng
**/
@ApiModel(description = "竞价企业交易成功商品表")
@TableName("sel_bidding_success_product")
@Data
public class SelBiddingSuccessProductEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "成功id",allowEmptyValue = true, required = false)
    private String successId;

    @ApiModelProperty("产品id")
    private String productId;

    @ApiModelProperty("产品名称")
    private String productName;
    @ApiModelProperty("报价")
    private BigDecimal price;

    @ApiModelProperty("报量")
    private BigDecimal count;

    @ApiModelProperty("追加报价")
    private BigDecimal addPrice;

    @ApiModelProperty("追加报量")
    private BigDecimal addCount;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;


    @ApiModelProperty(notes = "竞价id")
    @TableField(exist = false)
    private String biddingId;

    @ApiModelProperty(notes = "轮次id")
    @TableField(exist = false)
    private String sequenceId;

    @ApiModelProperty("报价人")
    @TableField(exist = false)
    private String offerId;


    @ApiModelProperty(notes = "场次")
    @TableField(exist = false)
    private Integer sequence;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date planStartDate;
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date planEndTime;

    @ApiModelProperty(notes = "竞价编码")
    @TableField(exist = false)
    private String biddingCode;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式")
    @TableField(exist = false)
    private Integer modeType;

    @ApiModelProperty(notes = "周期数")
    @TableField(exist = false)
    private Integer periodNum;

    @ApiModelProperty(notes = "场次数")
    @TableField(exist = false)
    private Integer roundNum;

    @ApiModelProperty(notes = "竞价名称")
    @TableField(exist = false)
    private String planName;

}
