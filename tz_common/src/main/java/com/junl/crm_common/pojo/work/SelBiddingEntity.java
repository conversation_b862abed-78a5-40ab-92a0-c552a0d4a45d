package com.junl.crm_common.pojo.work;
import java.util.ArrayList;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.pojo.ReportPlan;
import com.junl.crm_common.pojo.SuccessExcel;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Iterator;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 销售竞价
 * @author:      daiqimeng
 **/
@ApiModel(description = "销售竞价")
@TableName("sel_bidding")
@Data
@Accessors(chain = true)
public class SelBiddingEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "分组id")
    private String groupId;

    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(notes = "计划主ID",allowEmptyValue = true, required = false)
    private String planId;

    @ApiModelProperty(notes = "计划编码",allowEmptyValue = true, required = false)
    private String biddingCode;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式",allowEmptyValue = true, required = false)
    private Integer modeType;

    @ApiModelProperty(notes = "周期数",allowEmptyValue = true, required = false)
    private Integer periodNum;

    @ApiModelProperty(notes = "场次数",allowEmptyValue = true, required = false)
    private Integer roundNum;

    @ApiModelProperty(notes = "竞价状态1-竞价审核中2-竞价中3-竞价结束,4交易完成",allowEmptyValue = true, required = false)
    private Integer biddingStatus;

    @ApiModelProperty(notes = "竞价名称",allowEmptyValue = true, required = false)
    private String planName;

    @ApiModelProperty(notes = "竞价开始时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;

    @ApiModelProperty(notes = "运输方式")
    private String trafficMode;

    @ApiModelProperty(notes = "交货方式")
    private Integer deliveryMode;


    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "交货日期开始")
    private Date deliveryStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "交货日期结束")
    private Date deliveryEndTime;

    @ApiModelProperty(notes = "质量标准")
    private String quality;

    @ApiModelProperty(notes = "交易描述")
    @TableField("`describe`")
    private String describe;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "是否删除0-否1-是",allowEmptyValue = true, required = false)
    private Integer isDelete;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @TableField(exist = false)
    private String createName;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "编辑人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "编辑时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty(notes = "报表对象")
    private List<ReportPlan> reportPlans;

    @ApiModelProperty(notes = "轮次信息")
    @TableField(exist = false)
    private List<SelBiddingSequenceEntity> sequenceList;

    @ApiModelProperty(notes = "竞价商品")
    @TableField(exist = false)
    private List<SelBiddingProductEntity> productList;

    @ApiModelProperty(notes = "竞价公司")
    @TableField(exist = false)
    private List<SelBiddingCustomerEntity> customerList;

    @ApiModelProperty(notes = "成功交易信息")
    @TableField(exist = false)
    private List<SelBiddingSuccessEntity> successList;

    @ApiModelProperty(notes = "交易成功组装的信息")
    @TableField(exist = false)
    private List<SuccessExcel>  successData;

}
