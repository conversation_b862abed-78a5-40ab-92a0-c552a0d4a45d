package com.junl.crm_common.pojo.work;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.tools.DateUtils;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 销售询价主表
 * @author:      daiqimeng
 **/
@ApiModel(description = "销售询价主表")
@TableName("sel_inquiry")
@Data
@Accessors(chain = true)
public class SelInquiryEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    @NotNull
    private String id;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "分组id")
    private String groupId;

    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(notes = "计划主ID",allowEmptyValue = true, required = false)
    private String planId;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式",allowEmptyValue = true, required = false)
    private Integer modeType;

    @ApiModelProperty(notes = "询价状态1-待发布2-询价中3-询价结束",allowEmptyValue = true, required = false)
    private Integer inquiryStatus;

    @ApiModelProperty(notes = "询价名称",allowEmptyValue = true, required = false)
    private String inquiryName;

    @ApiModelProperty(notes = "询价开始时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date inquiryStartDate;

    @ApiModelProperty(notes = "询价结束时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date inquiryEndTime;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "期数")
    private Integer periodNum;

    @ApiModelProperty(notes = "是否删除0-否1-是",allowEmptyValue = true, required = false)
    private Integer isDelete;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @TableField(exist = false)
    private String createName;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "编辑人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "编辑时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @TableField(exist = false)
    @ApiModelProperty(notes = "报价企业")
    @NotNull
    private List<SelInquiryCustomerEntity> customerList;

    @ApiModelProperty(notes = "是否参与询价0-否1-是",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer isInquiry;

    @ApiModelProperty(notes = "计划编码", required = false)
    @TableField(exist = false)
    private String planCode;

    @ApiModelProperty(notes = "计划名称", required = false)
    @TableField(exist = false)
    private String planName;

    @ApiModelProperty(notes = "倒计时单位秒", required = false)
    @TableField(exist = false)
    private Long stime;


    @TableField(exist = false)
    @ApiModelProperty(notes = "询价商品")
    List<SelPlanProductDetailEntity> productDetailList;


}
