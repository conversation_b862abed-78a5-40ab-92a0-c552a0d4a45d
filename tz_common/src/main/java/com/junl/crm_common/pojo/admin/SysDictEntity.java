package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 字典主表
 * @author:      daiqimeng
 **/
@ApiModel(description = "字典主表")
@TableName("sys_dict")
@Data
public class SysDictEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    @NotNull
    private String dictId;

    @ApiModelProperty(notes = "字典名称",allowEmptyValue = true, required = false)
    @NotNull
    private String dictName;

    @ApiModelProperty(notes = "字典标识",allowEmptyValue = true, required = false)
    private String dictFlag;


    @ApiModelProperty(notes = "字典标识修改之前的值")
    @TableField(exist = false)
    private String oldDictFlag;

    @NotNull
    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "排序",allowEmptyValue = true, required = false)
    private Integer sort;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "字典value值")
    @TableField(exist = false)
    private List<SysDictValueEntity> values;

}
