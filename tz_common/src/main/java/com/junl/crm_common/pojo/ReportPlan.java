package com.junl.crm_common.pojo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.junl.crm_common.tools.excel.SheetName;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 期数报表
 * @author: daiqimeng
 * @date: 2021/8/2015:16
 */
@Data
@ContentRowHeight(18)
@SheetName("产品销售计划表")
public class ReportPlan {

    @ExcelProperty(index = 0)
    private String productName;
    @ExcelProperty(index = 1)
    @NumberFormat("#.##%")
    private Double sellCount;
    @ExcelProperty(index = 2)
    private String customerName;
    @ExcelProperty(index = 3)
    @NumberFormat("#.##%")
    private Double count;
    @ExcelProperty(index = 4)
    private String a;
    @ExcelProperty(index = 5)
    private String b;
    @ExcelProperty(index = 6)
    @NumberFormat("#.##%")
    private Double price;
    @ExcelProperty(index = 7)
    private String c;

    @ExcelIgnore
    private Integer size;
    @ExcelIgnore
    private boolean flag=false;



    public static List<List<String>> getHead(String name){
        List<List<String>> head=new ArrayList<>();

        List<String> list1=new ArrayList<>();
        list1.add(name);
        list1.add("产品名称");

        List<String> list2=new ArrayList<>();
        list2.add(name);
        list2.add("本轮可发量(吨)");

        List<String> list3=new ArrayList<>();
        list3.add(name);
        list3.add("客户名称");

        List<String> list4=new ArrayList<>();
        list4.add(name);
        list4.add("需求量(吨)");

        List<String> list5=new ArrayList<>();
        list5.add(name);
        list5.add("建议量(吨)");


        List<String> list6=new ArrayList<>();
        list6.add(name);
        list6.add("审批量(吨)");


        List<String> list7=new ArrayList<>();
        list7.add(name);
        list7.add("价格(元/吨)");


        List<String> list8=new ArrayList<>();
        list8.add(name);
        list8.add("备注");

        head.add(list1);
        head.add(list2);
        head.add(list3);
        head.add(list4);
        head.add(list5);
        head.add(list6);
        head.add(list7);
        head.add(list8);
        return head;
    }

}
