package com.junl.crm_common.pojo.work;
import java.lang.String;




import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author:      daiqimeng
 **/
@ApiModel(description = "")
@TableName("sel_group_relevance")
@Data
public class SelGroupRelevanceEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "id",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String customerId;

    @ApiModelProperty(notes = "客户名称")
    @TableField(exist = false)
    private String customerName;

    @ApiModelProperty(notes = "组id",allowEmptyValue = true, required = false)
    private String groupId;


}
