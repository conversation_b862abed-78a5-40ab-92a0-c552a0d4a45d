package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 公司表
 * @author:      daiqimeng
 **/
@ApiModel(description = "公司表")
@TableName("sys_company")
@Data
public class SysCompanyEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    @NotNull
    private String companyId;

    @ApiModelProperty(notes = "公司名称",allowEmptyValue = true, required = false)
    @NotNull
    private String companyName;

    @ApiModelProperty(notes = "省市区",allowEmptyValue = true, required = false)
    private String provinces;

    @ApiModelProperty(notes = "详细地址",allowEmptyValue = true, required = false)
    private String detailedAddress;

    @ApiModelProperty(notes = "logo",allowEmptyValue = true, required = false)
    private String logo;

    @ApiModelProperty(notes = "是否子公司 0否 1是",allowEmptyValue = true, required = false)
    private Integer subsidiary;

    @ApiModelProperty(notes = "公司唯一标识 不区分公司和子公司的标识 主要用于区分是否是一家公司",allowEmptyValue = true, required = false)
    private String flag;

    @ApiModelProperty(notes = "法人",allowEmptyValue = true, required = false)
    @NotNull
    private String legalPerson;

    @ApiModelProperty(notes = "公司电话",allowEmptyValue = true, required = false)
    private String telephone;

    @ApiModelProperty(notes = "法人联系方式",allowEmptyValue = true, required = false)
    @NotNull
    private String phone;

    @ApiModelProperty(notes = "附件 （主要用于传输公司营业执照 等证明）",allowEmptyValue = true, required = false)
    private String accessory;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "所属行业",allowEmptyValue = true, required = false)
    private String trade;

    @ApiModelProperty(notes = "简称",allowEmptyValue = true, required = false)
    private String abbreviation;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "管理员")
    private String principal;

    @ApiModelProperty(notes = "公司管理员")
    @NotNull
    @TableField(exist = false)
    private SysUserEntity boss;

    @ApiModelProperty(notes = "公司部门")
    @TableField(exist = false)
    private List<SysDeptEntity> dept;

    @ApiModelProperty(notes = "公司无部门人员信息")
    @TableField(exist = false)
    private List<SysUserEntity> users;

}
