package com.junl.crm_common.pojo.work;
import java.lang.String;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 竞价的基价调整记录
* @author:      daiqimeng
**/
@ApiModel(description = "竞价的基价调整记录")
@TableName("sel_bidding_fee_record")
@Data
public class SelBiddingFeeRecordEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "轮次",allowEmptyValue = true, required = false)
    private String sequenceId;

    @ApiModelProperty(notes = "基价",allowEmptyValue = true, required = false)
    private BigDecimal baseFee;

    @ApiModelProperty(notes = "商品id")
    private String productId;

    @ApiModelProperty(notes = "商品名称")
    private String productName;

}
