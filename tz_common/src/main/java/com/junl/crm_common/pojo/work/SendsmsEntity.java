package com.junl.crm_common.pojo.work;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;


import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 发送短信表
 * @author: daiqimeng
 **/
@ApiModel(description = "发送短信表")
@TableName("SendSms")
@Data
public class SendsmsEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键", allowEmptyValue = true, required = false)
    private String smsindex;

    @ApiModelProperty(notes = "手机号码，一次最多发送100个手机号码", allowEmptyValue = true, required = false)
    private String phonenumber;

    @ApiModelProperty(notes = "短信内容", allowEmptyValue = true, required = false)
    private String smscontent;

    @ApiModelProperty(notes = "插表时间", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date smstime;

    @ApiModelProperty(notes = "信息发送人", allowEmptyValue = true, required = false)
    private String smsuser;

    @ApiModelProperty(notes = "预约发送开始时间。无值需写入null", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statime;

    @ApiModelProperty(notes = "预约发送结束时间。无值需写入null", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endtime;

    @ApiModelProperty(notes = "发送状态（0-待发送,1-发送成功,2-发送失败）", allowEmptyValue = true, required = false)
    private Integer status;

    @ApiModelProperty(notes = "扩展接入号，最多支持3位扩展", allowEmptyValue = true, required = false)
    private String extno;

    @ApiModelProperty(notes = "发送结果编码", allowEmptyValue = true, required = false)
    private String resultcode;

    @ApiModelProperty(notes = "发送结果描述", allowEmptyValue = true, required = false)
    private String resultdesc;

    @ApiModelProperty(notes = "发送失败的手机号码", allowEmptyValue = true, required = false)
    private String faillist;

}
