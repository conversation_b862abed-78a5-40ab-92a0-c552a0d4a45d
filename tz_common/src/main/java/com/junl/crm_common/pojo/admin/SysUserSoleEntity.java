package com.junl.crm_common.pojo.admin;
import java.lang.String;




import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * @description: 用户唯一标识关联表
 * @author:      daiqimeng
 **/
@TableName("sys_user_sole")
@Data
public class SysUserSoleEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    private String userId;

    private String soleId;

    private String openId;

}
