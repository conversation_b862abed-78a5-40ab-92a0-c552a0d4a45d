package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.pojo.SuccessExcel;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * @description: 销售竞价
 * @author:      daiqimeng
 **/
@TableName("flow_sel_bidding")
@Data
public class FlowSelBiddingEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    private String biddingId;

    private String roundId;

    private String underwayId;

    private String companyId;

    private String planId;

    private String biddingCode;

    private Integer modeType;

    private Integer periodNum;

    private Integer roundNum;

    private Integer biddingStatus;

    private String planName;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;

    private Integer trafficMode;

    private Integer deliveryMode;

    @TableField("`describe`")
    private String describe;

    private String quality;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryStartDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryEndTime;

    private String remark;

    private Integer isDelete;

    private String createBy;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @TableField(exist = false)
    /** 竞价公司 */
    private List<FlowSelBiddingCustomerEntity> customerList;

    @TableField(exist = false)
    /** 竞价商品 */
    private List<FlowSelBiddingProductEntity> productList;
    @TableField(exist = false)
    private List<FlowSelBiddingSuccessEntity> successList;

    @TableField(exist = false)
    private List<SuccessExcel> successData;

}
