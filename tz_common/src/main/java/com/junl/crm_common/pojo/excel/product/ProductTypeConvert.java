package com.junl.crm_common.pojo.excel.product;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/1510:59
 */
public class ProductTypeConvert implements Converter<Integer> {
    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        Integer type=null;
        String stringValue = cellData.getStringValue();
        switch (stringValue){
            case "燃料油类":
                type=1;
                break;
            case "化工类":
                type=2;
                break;
            case "苯类":
                type=3;
                break;
            default:
                type=4;
        }
        return type;
    }

    @Override
    public CellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String type="";
        switch (value){
            case 1:
                type="燃料油类";
                break;
            case 2:
                type="燃料油类";
                break;
            case 3:
                type="燃料油类";
                break;
            default:
                type="其他";
        }
        return new CellData(type);
    }
}
