package com.junl.crm_common.pojo.work;
import java.lang.String;
import java.lang.Integer;




import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 竞价商品详情表
 * @author:      daiqimeng
 **/
@ApiModel(description = "竞价商品详情表")
@TableName("sel_bidding_product")
@Data
public class SelBiddingProductEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "序列id",allowEmptyValue = true, required = false)
    private String sequenceId;

    @ApiModelProperty(notes = "商品名称",allowEmptyValue = true, required = false)
    private String productName;

    @ApiModelProperty(notes = "商品id",allowEmptyValue = true, required = false)
    private String productId;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式",allowEmptyValue = true, required = false)
    private Integer modeType;

    @ApiModelProperty(notes = "制定基价",allowEmptyValue = true, required = false)
    private BigDecimal baseFee;

    @ApiModelProperty(notes = "合同量",allowEmptyValue = true, required = false)
    private BigDecimal contractQuantity;

    @ApiModelProperty(notes = "库存量",allowEmptyValue = true, required = false)
    private BigDecimal stockQuantity;

    @ApiModelProperty(notes = "日产量",allowEmptyValue = true, required = false)
    private BigDecimal dailyQuantity;

    @ApiModelProperty(notes = "预计可销售量",allowEmptyValue = true, required = false)
    private BigDecimal saleQuantity;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "报价",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Double price;

    @ApiModelProperty(notes = "报量(吨)",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Double count;

    @ApiModelProperty(notes = "报量ID",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String offerId;

}
