package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 系统用户
 * @author:      daiqimeng
 **/
@ApiModel(description = "系统用户")
@TableName("sys_user")
@Data
@Accessors(chain = true)
public class SysUserEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    @NotNull
    private String userId;

    @ApiModelProperty(notes = "用户名称",allowEmptyValue = true, required = false)
    @NotNull
    private String userName;

    @ApiModelProperty(notes = "用户真实姓名")
    @NotNull
    private String realName;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    @NotNull
    private String companyId;

    @ApiModelProperty(notes = "数据权限标识id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dataId;

    @ApiModelProperty(notes = "公司名称",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(notes = "邮箱")
    private String email;

    @ApiModelProperty(notes = "部门Id",allowEmptyValue = true, required = false)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deptId;

    @TableField(exist = false)
    @ApiModelProperty(notes = "部门名称",allowEmptyValue = true, required = false)
    private String deptName;


    @ApiModelProperty(notes = "职位Id",allowEmptyValue = true, required = false)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String positionId;

    @ApiModelProperty(notes = "职位名称",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String positionName;

    @ApiModelProperty(notes = "系统身份标识")
    private Integer identityCode;

    @ApiModelProperty(notes = "手机号码",allowEmptyValue = true, required = false)
    private String phone;

    @ApiModelProperty(notes = "密码",allowEmptyValue = true, required = false)
    @NotNull
    private String password;

    @ApiModelProperty(notes = "角色Id",allowEmptyValue = true, required = false)
    @NotNull
    private String roleId;

    @ApiModelProperty(notes = "性别 0女 1男",allowEmptyValue = true, required = false)
    private String sex;

    @ApiModelProperty(notes = "头像",allowEmptyValue = true, required = false)
    private String headImg;

    @ApiModelProperty(notes = "最后登陆时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finallyLoginTime;

    @ApiModelProperty(notes = "账号状态 0正常 1冻结",allowEmptyValue = true, required = false)
    private String status;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @TableField(exist = false)
    @ApiModelProperty(notes = "用户唯一标识")
    private String soleId;
    @TableField(exist = false)
    @ApiModelProperty(notes = "用户唯一openId")
    private String openId;

}
