package com.junl.crm_common.pojo.work;
import java.lang.String;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 询价关联企业id
* @author:      daiqimeng
**/
@ApiModel(description = "询价关联企业id")
@TableName("sel_inquiry_customer")
@Data
public class SelInquiryCustomerEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "id",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "企业id",allowEmptyValue = true, required = false)
    private String customerId;

    @ApiModelProperty(notes = "询价Id",allowEmptyValue = true, required = false)
    private String inquiryId;

    @ApiModelProperty(notes = "是否报过价 0否1是")
    private Integer isInquiry;

    @ApiModelProperty(notes = "客户名称")
    @TableField(exist = false)
    private String customerName;


    @ApiModelProperty(notes = "报价详情")
    @TableField(exist = false)
    private List<SelInquiryOfferEntity>  offerDetails;

}
