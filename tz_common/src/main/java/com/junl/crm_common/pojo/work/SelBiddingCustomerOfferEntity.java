package com.junl.crm_common.pojo.work;
import java.lang.Double;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;



import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 竞价企业报量表
 * @author:      daiqimeng
 **/
@ApiModel(description = "竞价企业报量表")
@TableName("sel_bidding_customer_offer")
@Data
public class SelBiddingCustomerOfferEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "关联 bidding_customer主键",allowEmptyValue = true, required = false)
    private String relevanceId;

    @ApiModelProperty(notes = "商品id",allowEmptyValue = true, required = false)
    private String productId;

    @ApiModelProperty(notes = "商品名称",allowEmptyValue = true, required = false)
    private String productName;

    @ApiModelProperty(notes = "竟量",allowEmptyValue = true, required = false)
    private Double count;

    @ApiModelProperty(notes = "竟价")
    private BigDecimal price;

    @ApiModelProperty(notes = "竟量人",allowEmptyValue = true, required = false)
    private String offerer;

    @ApiModelProperty("竟量人名称")
    @TableField(exist = false)
    private String offererName;

    @ApiModelProperty(notes = "报价时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
