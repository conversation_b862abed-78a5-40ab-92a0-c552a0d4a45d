package com.junl.crm_common.pojo.work;
import java.lang.String;
import java.lang.Integer;




import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @description: 竞价轮次关联表
 * @author:      daiqimeng
 **/
@ApiModel(description = "竞价轮次关联表")
@TableName("sel_bidding_sequence")
@Data
public class SelBiddingSequenceEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "id",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "场次",allowEmptyValue = true, required = false)
    private Integer sequence;

    @ApiModelProperty(notes = "竞价id",allowEmptyValue = true, required = false)
    private String biddingId;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartDate;


    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;


    @ApiModelProperty(notes = "轮次关联公司信息")
    @TableField(exist = false)
    private List<SelBiddingCustomerEntity> customerList;

    @ApiModelProperty(notes = "轮次基价记录")
    @TableField(exist = false)
    private List<SelBiddingFeeRecordEntity> feeRecordList;


    @ApiModelProperty(notes = "竞价编码",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String biddingCode;

    @ApiModelProperty(notes = "模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer modeType;

    @ApiModelProperty(notes = "周期数",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer periodNum;

    @ApiModelProperty(notes = "场次数",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer roundNum;

    @ApiModelProperty(notes = "竞价状态1-待竞价2-竞价中3-竞价结束",allowEmptyValue = true, required = false)
    private Integer biddingStatus;

    @ApiModelProperty(notes = "竞价名称",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String planName;

    @ApiModelProperty(notes = "是否参与竞价0-否1-是",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer isBidding;

    @ApiModelProperty(notes = "是否竞价成功0-否1-是",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private Integer isSuccess;

    @ApiModelProperty(notes = "倒计时单位秒", required = false)
    @TableField(exist = false)
    private Long stime;

    @ApiModelProperty(notes = "竞价中间id", required = false)
    @TableField(exist = false)
    private String relevanceId;

    @ApiModelProperty(notes = "轮次商品列表")
    @TableField(exist = false)
    private List<SelBiddingProductEntity> productList;




}
