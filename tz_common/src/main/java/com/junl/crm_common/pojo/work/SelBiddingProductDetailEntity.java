package com.junl.crm_common.pojo.work;
import java.lang.String;




import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 销售竞价商品明细
 * @author:      daiqimeng
 **/
@ApiModel(description = "销售竞价商品明细")
@TableName("sel_bidding_product_detail")
@Data
public class SelBiddingProductDetailEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "竞价主ID",allowEmptyValue = true, required = false)
    private String biddingId;

    @ApiModelProperty(notes = "计划商品明细ID",allowEmptyValue = true, required = false)
    private String planProductId;

    @ApiModelProperty(notes = "商品ID",allowEmptyValue = true, required = false)
    private String productId;

    @ApiModelProperty(notes = "调整基价",allowEmptyValue = true, required = false)
    private String modifyBaseFee;

    @ApiModelProperty(notes = "调整定量",allowEmptyValue = true, required = false)
    private String modifyBaseQuantity;

}
