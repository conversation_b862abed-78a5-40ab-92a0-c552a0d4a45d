package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;



import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 销售竞价企业明细
 * @author:      daiqimeng
 **/
@ApiModel(description = "销售竞价企业明细")
@TableName("sel_bidding_customer_detail")
@Data
public class SelBiddingCustomerDetailEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "竞量明细ID",allowEmptyValue = true, required = false)
    private String biddingProductId;

    @ApiModelProperty(notes = "商品ID",allowEmptyValue = true, required = false)
    private String productId;

    @ApiModelProperty(notes = "客户ID",allowEmptyValue = true, required = false)
    private String customerId;

    @ApiModelProperty(notes = "报价",allowEmptyValue = true, required = false)
    private String replyFee;

    @ApiModelProperty(notes = "报量",allowEmptyValue = true, required = false)
    private String replyQuantity;

    @ApiModelProperty(notes = "是否报价0-否1-是",allowEmptyValue = true, required = false)
    private Integer isReply;

    @ApiModelProperty(notes = "成交状态1-未成交2-已成交",allowEmptyValue = true, required = false)
    private Integer dealStatus;

    @ApiModelProperty(notes = "是否删除0-否1-是",allowEmptyValue = true, required = false)
    private Integer isDelete;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "编辑人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "编辑时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
