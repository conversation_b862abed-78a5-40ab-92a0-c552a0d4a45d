package com.junl.crm_common.pojo;

import lombok.Data;

import java.util.List;

/**
 * @description: 审核节点前后端交互实体类
 * @author: daiqimeng
 * @date: 2021/7/219:59
 */

@Data
public class AuditDto {
    private String panelName;
    //负责人
    private String leadsId;
    //负责人姓名
    private String leadsName;
    private String id;
    private Boolean ture;
    //下一个节点
    private List<AuditDto> children;
}
