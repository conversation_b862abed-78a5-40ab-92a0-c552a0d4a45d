package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
                         import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

/**
* @description: 岗位表
* @author:      daiqimeng
**/
@ApiModel(description = "岗位表")
@TableName("sys_position")
@Data
public class SysPositionEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    private String positionId;

    @ApiModelProperty(notes = "岗位名称",allowEmptyValue = true, required = false)
    private String positionName;

    @ApiModelProperty(notes = "部门id",allowEmptyValue = true, required = false)
    private String deptId;

    @ApiModelProperty(notes = "部门名称")
    @TableField(exist = false)
    private String deptName;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
     @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
     @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
     @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
     @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty(notes = "岗位人员")
    @TableField(exist = false)
    private List<SysUserEntity> users;

}
