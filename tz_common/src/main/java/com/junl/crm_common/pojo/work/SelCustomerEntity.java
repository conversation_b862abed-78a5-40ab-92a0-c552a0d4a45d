package com.junl.crm_common.pojo.work;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.ExcelValidate;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.tools.excel.SheetName;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 企业表
 * @author:      daiqimeng
 **/
@ApiModel(description = "企业表")
@TableName("sel_customer")
@Data
@HeadFontStyle(fontHeightInPoints = 20)
@ColumnWidth(40)
@Accessors(chain = true)
@SheetName("企业用户")
public class SelCustomerEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    @NotNull
    private String id;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "客户编码",allowEmptyValue = true, required = false)
    private String customerCode;
    @NotNull
    @ApiModelProperty(notes = "客户名称",allowEmptyValue = true, required = false)
    @ExcelProperty({"企业导入","企业名称"})
    @ExcelValidate(value = "企业名称")
    private String customerName;

    @ApiModelProperty(notes = "营业执照附件",allowEmptyValue = true, required = false)
    private String businessLicense;

    @ApiModelProperty(notes = "经营产品类型:1燃料油类 2化工类 3苯类 4其它",allowEmptyValue = true, required = false)
    private String manageProduct;

    @ApiModelProperty(notes = "产品证件",allowEmptyValue = true, required = false)
    private String productFile;

    @ApiModelProperty(notes = "用户姓名",allowEmptyValue = true, required = false)
    @NotNull
    @ExcelProperty({"企业导入","企业管理人账号"})
    @ExcelValidate(value = "企业管理人账号")
    private String deputy;

    @ApiModelProperty(notes = "身份证号",allowEmptyValue = true, required = false)
    private String deputyCardNo;
    @NotNull
    @ApiModelProperty(notes = "用户电话",allowEmptyValue = true, required = false)
    @ExcelProperty({"企业导入","管理人手机号"})
    @ExcelValidate(patter = "^[1]\\d{10}$",value ="管理人手机号")
    private String deputyPhone;

    @ApiModelProperty(notes = "注册资金",allowEmptyValue = true, required = false)
    private BigDecimal registerFee;

    @ApiModelProperty(notes = "公司电话",allowEmptyValue = true, required = false)
    private String telephone;

    @ApiModelProperty(notes = "头像",allowEmptyValue = true, required = false)
    private String headImg;

    @ApiModelProperty(notes = "授权书",allowEmptyValue = true, required = false)
    private String authorizationFile;

    @ApiModelProperty(notes = "是否是否企业认证0-否1-是",allowEmptyValue = true, required = false)
    private Integer isAuth;

    @ApiModelProperty(notes = "是否微信认证0-否1-是",allowEmptyValue = true, required = false)
    private Integer isWxAuth;

    @ApiModelProperty(notes = "openid",allowEmptyValue = true, required = false)
    private String openId;

    @ApiModelProperty(notes = "客户审核状态1-待审核2-已审核",allowEmptyValue = true, required = false)
    private Integer customerStatus;

    @ApiModelProperty(notes = "活跃度等级类型1-一级2-二级3-三级4-其它",allowEmptyValue = true, required = false)
    private Integer levelType;

    @ApiModelProperty(notes = "省",allowEmptyValue = true, required = false)
    private String province;

    @ApiModelProperty(notes = "市",allowEmptyValue = true, required = false)
    private String city;

    @ApiModelProperty(notes = "区",allowEmptyValue = true, required = false)
    private String region;

    @ApiModelProperty(notes = "详细地址",allowEmptyValue = true, required = false)
    private String detailAddress;

    @ApiModelProperty(notes = "邮政编码",allowEmptyValue = true, required = false)
    private String postalCode;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "是否启用0-禁用1-启用",allowEmptyValue = true, required = false)
    private Integer isEnable;

    @ApiModelProperty(notes = "是否删除0-否1-是",allowEmptyValue = true, required = false)
    private Integer isDelete;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "编辑人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "编辑时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;



    @ApiModelProperty(notes = "用户ID",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String userId;

    @ApiModelProperty(notes = "用户名",allowEmptyValue = true, required = false)
    @TableField(exist = false)
    private String reallyName;

    @TableField(exist = false)
    @ApiModelProperty("excel 导入行标记")
    private Integer row;

}
