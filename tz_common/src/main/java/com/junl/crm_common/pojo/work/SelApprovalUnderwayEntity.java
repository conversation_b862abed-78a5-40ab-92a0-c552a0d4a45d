package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;



import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 正在进行中的流程
 * @author:      daiqimeng
 **/
@ApiModel(description = "正在进行中的流程")
@TableName("sel_approval_underway")
@Data
public class SelApprovalUnderwayEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "公司id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "业务类型 0销售计划审核 1 提交竞价审核 2 交易完成审核",allowEmptyValue = true, required = false)
    private Integer busType;

    @ApiModelProperty(notes = "业务id",allowEmptyValue = true, required = false)
    private String busId;

    @ApiModelProperty(notes = "流程json串",allowEmptyValue = true, required = false)
    private String schedule;

    @ApiModelProperty(notes = "创建人姓名")
    @TableField(exist = false)
    private String createName;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @ApiModelProperty(notes = "是否有效")
    private Integer isDelete;

    @ApiModelProperty(notes = "排序")
    private Integer sort;

    @ApiModelProperty(notes = "是否开始")
    private Integer isStart;


    @ApiModelProperty(notes = "是否撤销")
    private Integer isRepeal;

    @ApiModelProperty("流程节点")
    @TableField(exist = false)
    private SelApprovalFlowEntity flowEntityList;
}
