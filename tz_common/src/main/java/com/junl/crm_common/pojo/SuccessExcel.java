package com.junl.crm_common.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.junl.crm_common.tools.excel.SheetName;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/8/1018:20
 */

@Data
@ContentRowHeight(18)
@ColumnWidth(30)
@SheetName("竞价报表")
public class SuccessExcel {

    @ExcelProperty(index = 0)
    private String productName;

    @ExcelProperty(index = 1)
    private String customerName;

    @ExcelProperty(index = 2)
    @NumberFormat("#.##%")
    private Double count;
    @ExcelProperty(index = 3)
    @NumberFormat("#.##%")
    private Double price;
    @ExcelProperty(index = 4)
    @NumberFormat("#.##%")
    private Double addCount;
    @ExcelProperty(index = 5)
    @NumberFormat("#.##%")
    private Double addPrice;

    private Integer size;


    /**
     * @describe:  根据开始时间和结束时间获取表头
     * <AUTHOR>
     * @date 2021/8/10 15:12
     * @return: {@link List < List< String>>}
     */
    public static List<List<String>> getHead(String name,Integer roundNum){
        List<List<String>> head=new ArrayList<>();

        List<String> list1=new ArrayList<>();
        list1.add(name+"交易成功报表\t第"+roundNum+"轮");
        list1.add("客户名称");

        List<String> list2=new ArrayList<>();
        list2.add(name+"交易成功报表\t第"+roundNum+"轮");
        list2.add("产品名称");

        List<String> list3=new ArrayList<>();
        list3.add(name+"交易成功报表\t第"+roundNum+"轮");
        list3.add("报量");

        List<String> list4=new ArrayList<>();
        list4.add(name+"交易成功报表\t第"+roundNum+"轮");
        list4.add("基价");

        List<String> list5=new ArrayList<>();
        list5.add(name+"交易成功报表\t第"+roundNum+"轮");
        list5.add("追加量");

        List<String> list6=new ArrayList<>();
        list6.add(name+"交易成功报表\t第"+roundNum+"轮");
        list6.add("追加价");



        head.add(list1);
        head.add(list2);
        head.add(list3);
        head.add(list4);
        head.add(list5);
        head.add(list6);

        return head;
    }
}
