package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.ExcelValidate;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.pojo.excel.product.ProductTypeConvert;
import com.junl.crm_common.tools.excel.SheetName;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 商品表
 * @author:      daiqimeng
 **/

@ApiModel(description = "商品表")
@TableName("sel_product")
@Data
@HeadFontStyle(fontHeightInPoints = 20)
@ColumnWidth(40)
@SheetName("商品")
public class SelProductEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "商品编码",allowEmptyValue = true, required = false)
    @NotNull
    private String productCode;

    @ExcelProperty({"商品导入","商品名称"})
    @ExcelValidate(size = 20,value = "商品名称")
    @ApiModelProperty(notes = "商品名称",allowEmptyValue = true, required = false)
    @NotNull
    private String productName;

    @ExcelProperty(value = {"商品导入","商品分类"},converter = ProductTypeConvert.class)
    @ExcelValidate(value = "商品分类")
    @ApiModelProperty(notes = "商品分类1燃料油类 2化工类 3苯类 4其它",allowEmptyValue = true, required = false)
    @NotNull
    private Integer productType;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "是否删除0-否1-是",allowEmptyValue = true, required = false)
    private Integer isDelete;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "编辑人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "编辑时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty("excel 导入行标记")
    private Integer row;

}
