package com.junl.crm_common.pojo.work;
import java.lang.String;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.transaction.annotation.Transactional;

/**
* @description: 竞价序号关联所选企业表
* @author:      daiqimeng
**/
@ApiModel(description = "竞价序号关联所选企业表")
@TableName("sel_bidding_customer")
@Data
@Accessors(chain = true)
public class SelBiddingCustomerEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "序列轮次id",allowEmptyValue = true, required = false)
    private String sequence;

    @ApiModelProperty(notes = "客户id",allowEmptyValue = true, required = false)
    private String customerId;

    @ApiModelProperty(notes = "详细地址", required = false)
    private String detailAddress;

    @ApiModelProperty(notes = "备注", required = false)
    private String remark;

    @ApiModelProperty(notes = "是否参与竞价0-否1-是")
    private Integer isBidding;

    @ApiModelProperty(notes = "是否竞价成功0-否1-是")
    private Integer isSuccess;

    @ApiModelProperty(notes = "客户名称")
    @TableField(exist = false)
    private String customerName;

    @ApiModelProperty("报价信息")
    @TableField(exist = false)
    private List<SelBiddingCustomerOfferEntity>  offerList;

}
