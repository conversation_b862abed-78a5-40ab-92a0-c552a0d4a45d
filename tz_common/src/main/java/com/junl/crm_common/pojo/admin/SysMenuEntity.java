package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 菜单权限表
 * @author:      daiqimeng
 **/
@ApiModel(description = "菜单权限表")
@TableName("sys_menu")
@Data
public class SysMenuEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "菜单ID",allowEmptyValue = true, required = false)
    @TableId
    @NotNull
    private String menuId;

    @ApiModelProperty(notes = "菜单名称",allowEmptyValue = true, required = false)
    @NotNull
    private String menuName;

    @ApiModelProperty(notes = "父菜单ID",allowEmptyValue = true, required = false)
    @NotNull
    private String parentId;

    @ApiModelProperty(notes = "显示顺序",allowEmptyValue = true, required = false)
    private Integer orderNum;

    @ApiModelProperty(notes = "路由地址",allowEmptyValue = true, required = false)
    private String path;

    @ApiModelProperty(notes = "组件路径",allowEmptyValue = true, required = false)
    private String component;

    @ApiModelProperty(notes = "是否为外链（0是 1否）",allowEmptyValue = true, required = false)
    @NotNull
    private Integer isFrame;

    @ApiModelProperty(notes = "是否缓存（0缓存 1不缓存）",allowEmptyValue = true, required = false)
    private Integer isCache;

    @ApiModelProperty(notes = "菜单类型（M目录 C菜单 F按钮）",allowEmptyValue = true, required = false)
    @NotNull
    private String menuType;

    @ApiModelProperty(notes = "菜单状态（0显示 1隐藏）",allowEmptyValue = true, required = false)
    private String visible;

    @ApiModelProperty(notes = "菜单状态（0正常 1停用）",allowEmptyValue = true, required = false)
    private String status;

    @ApiModelProperty(notes = "菜单图标",allowEmptyValue = true, required = false)
    private String icon;

    @ApiModelProperty(notes = "创建者",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新者",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "备注",allowEmptyValue = true, required = false)
    private String remark;

    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty(notes = "子菜单")
    @TableField(exist = false)
    private List<SysMenuEntity> childMenu;

}
