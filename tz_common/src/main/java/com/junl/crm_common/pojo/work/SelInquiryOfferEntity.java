package com.junl.crm_common.pojo.work;
import java.lang.Double;
import java.util.Date;
import java.lang.String;



import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 企业对于询价的报价报量记录表
 * @author:      daiqimeng
 **/
@ApiModel(description = "企业对于询价的报价报量记录表")
@TableName("sel_inquiry_offer")
@Data
public class SelInquiryOfferEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "id",allowEmptyValue = true, required = false)
    private String id;

    @ApiModelProperty(notes = "关联表id",allowEmptyValue = true, required = false)
    private String relevancyId;

    @ApiModelProperty(notes = "商品id",allowEmptyValue = true, required = false)
    private String productId;

    @ApiModelProperty(notes = "商品名称",allowEmptyValue = true, required = false)
    private String productName;

    @ApiModelProperty(notes = "报价",allowEmptyValue = true, required = false)
    private Double price;

    @ApiModelProperty(notes = "量(吨)",allowEmptyValue = true, required = false)
    private Double count;

    @ApiModelProperty(notes = "报价时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "报价人",allowEmptyValue = true, required = false)
    private String offerer;

    @TableField(exist = false)
    private String offerName;

}
