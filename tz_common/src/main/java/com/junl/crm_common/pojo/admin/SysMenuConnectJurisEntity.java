package com.junl.crm_common.pojo.admin;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 权限菜单关联表
* @author:      daiqimeng
**/
@ApiModel(description = "权限菜单关联表")
@TableName("sys_menu_connect_juris")
@Data
public class SysMenuConnectJurisEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    private String id;

    @ApiModelProperty(notes = "权限id",allowEmptyValue = true, required = false)
    private String jurisdictionId;

    @ApiModelProperty(notes = "菜单id",allowEmptyValue = true, required = false)
    private String menuId;

}
