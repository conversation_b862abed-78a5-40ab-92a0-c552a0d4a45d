package com.junl.crm_common.pojo.work;
import java.lang.String;
import java.lang.Integer;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
* @description: 审批记录销售计划明细
* @author:      daiqimeng
**/
@TableName("flow_sel_plan_product_detail")
@Data
public class FlowSelPlanProductDetailEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    private String companyId;

    private String productName;

    private String productId;

    private String planId;

    private Integer modeType;

    private String baseFee;

    private Double contractQuantity;

    private Double stockQuantity;

    private Double dailyQuantity;

    private Double saleQuantity;

    private String remark;

}
