package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.common.JWTToken;
import com.junl.crm_common.tools.JWTUtil;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 公告写表(读写表分离)
 * @author:      daiqimeng
 **/
@ApiModel(description = "公告写表(读写表分离)")
@TableName("sys_notice_write")
@Data
public class SysNoticeWriteEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    @NotNull
    private String noticeId;

    @ApiModelProperty(notes = "公告标题",allowEmptyValue = true, required = false)
    @NotNull
    private String noticeTitle;

    @ApiModelProperty(notes = "公告内容",allowEmptyValue = true, required = false)
    @NotNull
    private String noticeContent;

    @ApiModelProperty(notes = "公告附件",allowEmptyValue = true, required = false)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String accessory;

    @ApiModelProperty(notes = "附件前端传输的数组")
    @TableField(exist = false)
    private List<String> accessoryArray;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "是否发布  0未发布 1已发布")
    private Integer status;

}
