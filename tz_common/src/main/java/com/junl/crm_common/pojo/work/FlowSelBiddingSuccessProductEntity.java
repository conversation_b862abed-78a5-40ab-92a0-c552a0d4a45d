package com.junl.crm_common.pojo.work;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 审批记录竞价企业交易成功商品表
 * @author:      daiqimeng
 **/
@TableName("flow_sel_bidding_success_product")
@Data
public class FlowSelBiddingSuccessProductEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    private String successId;

    private String productId;

    private String productName;

    private BigDecimal price;

    private BigDecimal count;

    private BigDecimal addPrice;

    private BigDecimal addCount;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String createBy;

    private String updateBy;

}
