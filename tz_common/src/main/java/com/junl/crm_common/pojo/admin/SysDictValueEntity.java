package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 字典映射表 (值表)
 * @author:      daiqimeng
 **/
@ApiModel(description = "字典映射表 (值表)")
@TableName("sys_dict_value")
@Data
public class SysDictValueEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @NotNull
    @TableId
    private String dictValueId;

    @ApiModelProperty(notes = "字典主表Id",allowEmptyValue = true, required = false)
    @NotNull
    private String dictId;

    @ApiModelProperty(notes = "可选值",allowEmptyValue = true, required = false)
    @NotNull
    private String dictValue;

    @ApiModelProperty(notes = "显示值")
    @NotNull
    private String dictName;

    @ApiModelProperty(notes = "老的显示值")
    @TableField(exist = false)
    private String oldDictName;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

}
