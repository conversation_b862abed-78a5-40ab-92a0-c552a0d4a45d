package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.status.Opposite;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 公告读表(读写表分离)
 * @author:      daiqimeng
 **/
@ApiModel(description = "公告读表(读写表分离)")
@TableName("sys_user_msg")
@Data
@Accessors(chain = true)
public class SysUserMsgEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    private String msgId;

    @ApiModelProperty(notes = "消息标题",allowEmptyValue = true, required = false)
    private String msgTitle;

    @ApiModelProperty(notes = "消息内容",allowEmptyValue = true, required = false)
    private String msgContent;

    @ApiModelProperty(notes = "消息附件",allowEmptyValue = true, required = false)
    private String msgAccessory;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "阅读标识 0否 1是",allowEmptyValue = true, required = false)
    private String status= Opposite.ZERO;

    @ApiModelProperty(notes = "阅读人",allowEmptyValue = true, required = false)
    private String reader;

    @ApiModelProperty(notes = "消息类型 用于后端处理不同的业务场景监听使用",allowEmptyValue = true, required = false)
    private Integer msgType;

    @ApiModelProperty(notes = "接收时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createDate;

    @ApiModelProperty(notes = "是否删除 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "用户前端判断是什么消息类型")
    private Integer busCode;

    @ApiModelProperty(notes = "具体是做什么的  如：销售计划  提交竞价等 可以锁定某个具体业务")
    @TableField("`specific`")
    private Integer specific;

}
