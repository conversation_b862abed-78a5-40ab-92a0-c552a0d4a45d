package com.junl.crm_common.pojo.admin;

import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.status.Opposite;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 用户历史信息表
 * @author: daiqimeng
 **/
@ApiModel(description = "用户历史信息表")
@TableName("sys_history_msg")
@Data
public class SysHistoryMsgEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "", allowEmptyValue = true, required = false)
    @TableId
    private String msgId;

    @ApiModelProperty(notes = "消息标题", allowEmptyValue = true, required = false)
    private String msgTitle;

    @ApiModelProperty(notes = "消息内容", allowEmptyValue = true, required = false)
    private String msgContent;

    @ApiModelProperty(notes = "消息附件", allowEmptyValue = true, required = false)
    private String msgAccessory;

    @ApiModelProperty(notes = "公司Id", allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "创建人", allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "锁定具体业务", allowEmptyValue = true, required = false)
    @TableField("`specific`")
    private String specific;

    @ApiModelProperty(notes = "阅读标识 0否 1是",allowEmptyValue = true, required = false)
    private Integer status= 0;

    @ApiModelProperty(notes = "阅读人", allowEmptyValue = true, required = false)
    private String reader;

    @ApiModelProperty(notes = "业务编码", allowEmptyValue = true, required = false)
    private Integer busCode;

    @ApiModelProperty(notes = "消息类型 暂定（0公告）", allowEmptyValue = true, required = false)
    private Integer msgType;

    @ApiModelProperty(notes = "接收时间", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(notes = "是否删除 0否 1是", allowEmptyValue = true, required = false)
    private Integer deleteFlag;

}
