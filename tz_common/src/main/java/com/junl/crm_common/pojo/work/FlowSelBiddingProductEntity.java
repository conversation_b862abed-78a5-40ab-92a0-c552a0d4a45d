package com.junl.crm_common.pojo.work;
import java.lang.String;
import java.lang.Integer;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
* @description: 竞价商品详情表
* @author:      daiqimeng
**/
@TableName("flow_sel_bidding_product")
@Data
public class FlowSelBiddingProductEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    private String sequenceId;

    private String productName;

    private String productId;

    private Integer modeType;

    private BigDecimal baseFee;

    private BigDecimal contractQuantity;

    private BigDecimal stockQuantity;

    private BigDecimal dailyQuantity;

    private BigDecimal saleQuantity;

    private String remark;

}
