package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;



import com.fasterxml.jackson.annotation.JsonFormat;
                         import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 短信回复表
* @author:      daiqimeng
**/
@ApiModel(description = "短信回复表")
@TableName("RecvSms")
@Data
public class RecvsmsEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "主键",allowEmptyValue = true, required = false)
    private String smsindex;

    @ApiModelProperty(notes = "回复短信的号码",allowEmptyValue = true, required = false)
    private String sendnumber;

    @ApiModelProperty(notes = "回复短信的内容",allowEmptyValue = true, required = false)
    private String smscontent;

    @ApiModelProperty(notes = "回复短信的时间",allowEmptyValue = true, required = false)
     @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
     @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date smstime;

    @ApiModelProperty(notes = "回复人号码",allowEmptyValue = true, required = false)
    private String callmdn;

}
