package com.junl.crm_common.pojo.work;

import java.util.Date;
import java.lang.String;


import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 系统日志
 * @author: daiqimeng
 **/
@ApiModel(description = "系统日志")
@TableName("sel_log")
@Data
public class SelLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "", allowEmptyValue = true, required = false)
    private String logId;

    @ApiModelProperty(notes = "类型:0小程序，1PC端", allowEmptyValue = true, required = false)
    private String type;

    @ApiModelProperty(notes = "模块描述", allowEmptyValue = true, required = false)
    private String logDescribe;

    @ApiModelProperty(notes = "操作类型 crud", allowEmptyValue = true, required = false)
    private String logType;

    @ApiModelProperty(notes = "入参", allowEmptyValue = true, required = false)
    private String logParam;

    @ApiModelProperty(notes = "内容", allowEmptyValue = true, required = false)
    private String logContent;

    @ApiModelProperty(notes = "创建时间", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间", allowEmptyValue = true, required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人", allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人", allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是", allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "公司Id", allowEmptyValue = true, required = false)
    private String companyId;

}
