package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 部门表
* @author:      daiqimeng
**/
@ApiModel(description = "部门表")
@TableName("sys_dept")
@Data
public class SysDeptEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    @NotNull
    private String deptId;

    @ApiModelProperty(notes = "部门名称",allowEmptyValue = true, required = false)
    @NotNull
    private String deptName;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "父级id",allowEmptyValue = true, required = false)
    @NotNull
    private String parentId;

    @ApiModelProperty(notes = "负责人",allowEmptyValue = true, required = false)
    private String principal;

    @ApiModelProperty(notes = "部门联系电话",allowEmptyValue = true, required = false)
    private String telephone;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
     @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
     @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
     @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
     @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @TableField(exist = false)
    private List<SysUserEntity> users;

    @TableField(exist = false)
    private List<SysPositionEntity> position;

    @TableField(exist = false)
    private List<SysDeptEntity> children;

}
