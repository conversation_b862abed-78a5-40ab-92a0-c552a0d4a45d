package com.junl.crm_common.pojo.admin;
import java.lang.String;
import java.lang.Integer;



 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description: 系统身份表
* @author:      daiqimeng
**/
@ApiModel(description = "系统身份表")
@TableName("sys_identity")
@Data
public class SysIdentityEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    private String identityId;

    @ApiModelProperty(notes = "系统身份名称",allowEmptyValue = true, required = false)
    private String identityName;

    @ApiModelProperty(notes = "系统身份编码 0 超级管理员 1管理员 2部门管理员 3普通用户",allowEmptyValue = true, required = false)
    private Integer identityCode;

}
