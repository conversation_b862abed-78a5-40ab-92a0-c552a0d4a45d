package com.junl.crm_common.pojo.work;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.pojo.AuditDto;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 流程定义表
 * @author:      daiqimeng
 **/
@ApiModel(description = "流程定义表")
@TableName("sel_approval_definition")
@Data
public class SelApprovalDefinitionEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @NotNull
    private String id;

    @ApiModelProperty(notes = "公司id",allowEmptyValue = true, required = false)
    private String companyId;

    @ApiModelProperty(notes = "流程json串",allowEmptyValue = true, required = false)
    private String schedule;

    @ApiModelProperty(notes = "业务类型 0销售计划审核 1 提交竞价审核 2 交易完成审核",allowEmptyValue = true, required = false)
    @NotNull
    private Integer busCode;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;
    @ApiModelProperty(notes = "更新人姓名")
    @TableField(exist = false)
    private String updateName;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("前端审批流程节点定义交互对象")
    @TableField(exist = false)
    @NotNull
    private AuditDto treeData;

}
