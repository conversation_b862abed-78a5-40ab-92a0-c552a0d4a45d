package com.junl.crm_common.pojo.admin;
import java.util.Date;
import java.lang.String;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 权限表
 * @author:      daiqimeng
 **/
@ApiModel(description = "权限表")
@TableName("sys_jurisdiction")
@Data
public class SysJurisdictionEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @TableId
    @NotNull
    private String jurisdictionId;
    @ApiModelProperty(notes = "权限名称",allowEmptyValue = true, required = false)
    @NotNull
    private String jurisdictionName;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除标识 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty(notes = "权限标识 *号代表获取所有菜单 1则根据关联表查询")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String jurisdictionFlag;

    @TableField(exist = false)
    @ApiModelProperty(notes = "关联的菜单")
    @NotNull
    private List<SysMenuEntity> menus;

}
