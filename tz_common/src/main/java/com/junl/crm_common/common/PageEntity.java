package com.junl.crm_common.common;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: Mr <PERSON>
 * @date: 2021/5/20 17:56
 */
@Data
public class PageEntity<T> {

    @ApiModelProperty("当前页码")
    private Integer page;
    @ApiModelProperty("分页条数")
    private Integer limit;
    @ApiModelProperty("总条数")
    private Long count;

    @ApiModelProperty("总页数")
    private Integer pageCount;
    @ApiModelProperty("返回数据")
    private List<T> list;

    public PageEntity(PageInfo<T> pageInfo){
        this.page=pageInfo.getPageNum();
        this.limit=pageInfo.getPageSize();
        this.count=pageInfo.getTotal();
        this.pageCount=pageInfo.getPages();
        this.list=pageInfo.getList();
    }

} 