package com.junl.crm_common.common;

import com.junl.crm_common.status.ResponseCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 统一返回类
 * @author: Mr Dai
 * @date: 2021/5/19 15:53
 */
@Data
@ApiModel("统一返回对象")
public class Result implements Serializable {

    @ApiModelProperty("返回状态码")
    private int code;
    @ApiModelProperty("返回数据")
    private Object data;
    @ApiModelProperty("返回消息")
    private String msg;

    public Result(){
        this(null);
    }

    public <T>Result(T t){
        this(t,"操作成功");
    }

    public <T>Result(T t,String msg){
        this(msg,t, ResponseCode.SUCCESS.getCode());
    }

    public <T>Result(String msg,T data,int code){
        this.msg=msg;
        this.data=data;
        this.code=code;
    }

    public static Result error(String msg){
        return new Result(msg,null,ResponseCode.ERROR.getCode());
    }

    public static Result error(Integer code,String msg){
        return new Result(msg,null,code);
    }




    public static Result success(){
        return new Result();
    }

    public static <T>Result success(T data){
        return new Result(data);
    }

    public static Result reCall(String msg){
        return new Result(msg,null,ResponseCode.RECALL.getCode());
    }


} 