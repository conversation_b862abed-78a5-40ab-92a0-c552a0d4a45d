package com.junl.crm_common.common;


import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.JWTUtil;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;

/**
 * @program: mms-car-wechat
 * @description:
 * @author: daiqimeng
 * @create: 2020-07-03 14:12
 **/
public class CustomRealm extends AuthorizingRealm {


    /**
     * 必须重写此方法，不然会报错
     */
    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof JWTToken;
    }

    /**
     * 默认使用此方法进行用户名正确与否验证，错误抛出异常即可。
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        String token = (String) authenticationToken.getCredentials();
        SysUserEntity sysUserEntity = JWTUtil.getUsername(token);
        if (!JWTUtil.verify(token,sysUserEntity)) {
            throw new AuthenticationException("token错误 不合法");
        }
        // 解密获得username，用于和数据库进行对比
        return new SimpleAuthenticationInfo(token, token, "MyRealm");
    }

    /**
     * 只有当需要检测用户权限的时候才会调用此方法，例如checkRole,checkPermission之类的
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        // 此处最好使用缓存提升速度
        //authorizationInfo.addRole();
        //authorizationInfo.addStringPermission();
        return authorizationInfo;
    }

}