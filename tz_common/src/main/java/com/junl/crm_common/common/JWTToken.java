package com.junl.crm_common.common;

import org.apache.shiro.authc.AuthenticationToken;

/**
 * @description: 自定义的shiro接口token，可以通过这个类将string的token转型成AuthenticationToken，可供shiro使用
 *  * 注意：需要重写getPrincipal和getCredentials方法

 * @author: Mr <PERSON>
 * @date: 2021/5/20 13:53
 */
public class JWTToken implements AuthenticationToken {

    private String token;

    public JWTToken(String token){
        this.token=token;
    }

    @Override
    public Object getPrincipal() {
        return token;
    }

    @Override
    public Object getCredentials() {
        return token;
    }
}