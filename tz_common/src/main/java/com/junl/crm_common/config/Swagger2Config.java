package com.junl.crm_common.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 增强swagger配置
 * @author: Mr Dai
 * @date: 2021/2/25 9:27
 */

@Configuration
@EnableSwagger2
@EnableKnife4j
public class Swagger2Config {


    @Bean
    public Docket createRestApi(){
        //可以添加多个header或参数
        ParameterBuilder aParameterBuilder = new ParameterBuilder();
        Parameter tokenParam = aParameterBuilder
                //参数类型支持header, cookie, body, query etc
                .parameterType("header")
                .required(true)
                //http头授权header
                .name("Authorization")
                .defaultValue("token")
                .description("统一认证,除了登陆和其他部分接口，所有请求必须携带该认证头")
                //指定参数值的类型
                .modelRef(new ModelRef("string"))
                //非必需，这里是全局配置，然而在登陆的时候是不用验证的
                .required(false).build();
        List<Parameter> aParameters = new ArrayList<>();
        aParameters.add(tokenParam);
        return  new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.junl"))
                .paths(PathSelectors.any())
                .build().globalOperationParameters(aParameters);
    }

    private ApiInfo apiInfo(){
        return new ApiInfoBuilder()
                .title("泰州竞拍Api接口文档")
                .description("泰州竞拍Api接口文档")
                //.termsOfServiceUrl("127.0.0.1")
                .version("V1.0")
                .contact("Mr Dai")
                .build();
    }


} 