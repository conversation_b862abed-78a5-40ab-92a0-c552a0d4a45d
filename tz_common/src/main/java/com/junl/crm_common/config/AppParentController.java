package com.junl.crm_common.config;


import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.JWTUtil;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Data
@Log4j2
/**
 * 获取权限信息
 * <AUTHOR>
 */
public class AppParentController {

    private String customerId;
    private String userId;
    private String customerName;



    @ModelAttribute
    public void start(){
        Object principal1 = SecurityUtils.getSubject().getPrincipal();
        if(Assert.notNull(principal1)){
            String principal = (String)principal1;
            SysUserEntity user = JWTUtil.getUsername(principal);
            this.customerId=user.getCompanyId();
            this.userId=user.getUserId();
            this.customerName=user.getCompanyName();
        }
    }

}
