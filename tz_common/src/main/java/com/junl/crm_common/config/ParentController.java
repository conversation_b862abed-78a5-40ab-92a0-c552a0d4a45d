package com.junl.crm_common.config;


import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.JWTUtil;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Data
@Log4j2
/**
 * 获取权限信息
 * <AUTHOR>
 */
public class ParentController {

    private String companyId;
    private String userId;
    private String deptId;
    private String userName;
    private SysUserEntity sysUserEntity;


    @ModelAttribute
    public void start(){
        Object principal1 = SecurityUtils.getSubject().getPrincipal();
        if(Assert.notNull(principal1)){
            String principal = (String)principal1;
            SysUserEntity sysUserEntity = JWTUtil.getUsername(principal);
            this.companyId=sysUserEntity.getCompanyId();
            this.deptId=sysUserEntity.getDeptId();
            this.userId=sysUserEntity.getUserId();
            this.userName=sysUserEntity.getUserName();
            this.sysUserEntity=sysUserEntity;
        }
    }

}
