package com.junl.crm_common.config;

import com.junl.crm_common.common.Result;
import com.junl.crm_common.tools.MinIoUtil;
import com.junl.crm_common.tools.OSSFileUtils;
import com.junl.crm_common.tools.Regular;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/5/3117:12
 */
@RestController
@RequestMapping("/file")
@Api(tags = "文件上传")
@Log4j2
public class FileUploadController {

    @Autowired
    private MinIoUtil minIoUtil;

    @Value("${minio.url}")
    private String domain;

    @RequestMapping(value = "/upload",method = {RequestMethod.POST,RequestMethod.PUT})
    @ApiOperation("上传文件")
    public Result upload(@RequestParam("file") MultipartFile file){
        String url="";
        try {
            String originalFilename = file.getOriginalFilename();
            String name=System.currentTimeMillis()+ UUID.randomUUID().toString()+getSuffix(originalFilename);
            InputStream inputStream = file.getInputStream();
            url=minIoUtil.putObject(name,"file",inputStream);
        }catch (Exception e){
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }

        return Result.success(Regular.replace(url,domain));
    }

    private String getSuffix(String fileName){
        int i = fileName.lastIndexOf(".");
        if(i>=0){
            return fileName.substring(i);
        }else{
            return "";
        }
    }

}
