package com.junl.crm_common.config;

import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.work.SelPlanEntity;
import com.junl.crm_common.status.ResponseCode;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.*;
import java.util.List;
import java.util.Optional;

/**
 * @description: 全局异常处理
 * @author: Mr <PERSON>
 * @date: 2021/5/21 17:32
 */
@RestControllerAdvice
@Log4j2
public class CommonController {

    @ExceptionHandler({Exception.class})
    public Result exception(Exception e) throws IOException {

        if(e instanceof  NullPointerException){
            StringWriter stringWriter = new StringWriter();
            PrintWriter writer=new PrintWriter(stringWriter);
            e.printStackTrace(writer);
            log.error("发生错误：{}",stringWriter.toString());
            stringWriter.close();
            writer.close();
        }else{
            log.error("发生错误：{}",e.getMessage());
        }

        return Result.error(e.getMessage());
    }

} 