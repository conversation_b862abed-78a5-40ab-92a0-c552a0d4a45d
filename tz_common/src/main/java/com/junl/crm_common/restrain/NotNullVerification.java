package com.junl.crm_common.restrain;

import com.junl.crm_common.annotation.NotNull;
import com.junl.crm_common.tools.Assert;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 参数非空判断
 * 校验自定义注解Not NUll
 */
public class NotNullVerification extends Calibrator {


    public NotNullVerification(Object o, Object service) {
        super(o, service);
    }

    @Override
    protected void validate(Object data, Object o2) {
        Class<?> aClass = data.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            if (declaredField.isAnnotationPresent(NotNull.class)) {
                try {
                    declaredField.setAccessible(true);
                    Object o = declaredField.get(data);
                    Assert.notNull(o, declaredField.getName() + "不能为null");
                } catch (Exception e) {
                    throw new RuntimeException(declaredField.getName() + "不能为null");
                }
            }
        }
    }
}