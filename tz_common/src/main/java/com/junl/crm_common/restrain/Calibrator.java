package com.junl.crm_common.restrain;

import com.junl.crm_common.common.ValidateEntity;
import com.junl.crm_common.exception.ValidateException;

/**
 * @description: 业务校验对象
 * @author: Mr <PERSON>
 * @date: 2021/5/21 16:15
 */
public abstract class Calibrator<T,R> {

    private ValidateEntity validateEntity;
    private T data;
    private R service;

    public Calibrator(T t,R service){
        this.data=t;
        this.validateEntity=new ValidateEntity();
        this.service=service;
        validateEntity.setResult(true);
    }

    /**错误处理
     * @param msg*/
    protected void error(String msg){
        throw new ValidateException(msg);
    }
    /**成功处理*/
    protected void success(){ }

    /**处理逻辑*/
    protected ValidateEntity validate(){
        try {
            validate(data,service);
        }catch (Exception e){
            validateEntity.setResult(false);
            validateEntity.setMsg(e.getMessage());
        }
        return validateEntity;
    }

    /**提交验证*/
    protected void submit(){
        ValidateEntity validate = validate();
        if (validate.getResult().equals(Boolean.TRUE)) {
            success();
        }else{
            error(validate.getMsg());
        }
    }

    /**
     * 校验逻辑  需要实现者自己写
     * @param t
     */
    protected abstract void validate(T t,R r);


}
