package com.junl.crm_common.status;


/**
 * redis  key  枚举类
 *
 * <AUTHOR>
 */
public enum RedisKey {

    //竞价
    BIDDING("bidding:"),
    //询价
    INQUIRY("inquiry:"),

    OPEN_ID("opneid:"),

    PHONE_RECORD("phone:record"),

    //竞价即将结束通知
    BIDDING_NOTICE("bidding_notice:"),
    //询价即将结束通知
    INQUIRY_NOTICE("inquiry_notice:"),

    IP("ip:"),

    //验证码
    CODE("code:"),
    //手机验证码(微信公众号)
    PHONE("phone:"),

    //手机验证码(管理端)
    PHONE_SYS("phone_sys:"),
    //手机验证码 (管理端小程序登陆)
    APPLET_PHONE("appletPhone:"),

    //公司名称信息
    COMPANY("company:");



    private String name;

    RedisKey(String name){
        this.name=name;
    }

    public String getName(){
        return this.name;
    }
}
