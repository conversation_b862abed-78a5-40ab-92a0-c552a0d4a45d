package com.junl.crm_common.status;

import java.util.Arrays;
import java.util.Optional;

/**
 * 系统身份枚举类
 * <AUTHOR>
 */
public enum IdentityStatus {
    //顶级的意思 无论在什么场景
    GOD("*",88),
    SUPER_ADMIN("超级系统管理员",0),
    ADMIN("系统管理员",1),
    DEPT_ADMIN("部门管理员",2),
    USER("普通用户",3),
    MANAGEMENT("系统端管理",4);

    private String name;
    private Integer code;

    IdentityStatus(String name,Integer code){
        this.name=name;
        this.code=code;
    }


    public Integer getCode(){
        return this.code;
    }

    public String getName(){
        return this.name;
    }

    public static Integer getCode(String name){
        Optional<IdentityStatus> first = Arrays.stream(IdentityStatus.values()).filter(x -> x.getName().equals(name)).findFirst();
        return first.isPresent()?first.get().getCode():null;
    }

    public static String getName(String code){
        Optional<IdentityStatus> first = Arrays.stream(IdentityStatus.values()).filter(x -> x.getCode().equals(code)).findFirst();
        return first.isPresent()?first.get().getName():null;
    }

}
