package com.junl.crm_common.listener;

import com.junl.crm_common.common.RedisQueueService;
import com.junl.crm_common.status.RedisKey;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: redis key过期监听
 * @author: daiqimeng
 * @date: 2021/7/2913:43
 */
@Component
@Log4j2
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    @Autowired
    private List<RedisQueueService> queueServices;


    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }



    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expiredKey = message.toString();
        log.info("key过期： {}",expiredKey);
        try {
            if (expiredKey.startsWith(RedisKey.BIDDING.getName())
                    ||expiredKey.startsWith(RedisKey.INQUIRY.getName())
                    ||expiredKey.startsWith(RedisKey.BIDDING_NOTICE.getName())
                    ||expiredKey.startsWith(RedisKey.INQUIRY_NOTICE.getName())) {
                String[] split = expiredKey.split(":");
                String type=split[0];
                String busId=split[1];
                queueServices.forEach(x->{
                    x.dispose(type,busId);
                });
            }
        }catch (Exception e){
            log.error(e.getMessage());
        }


    }
}
