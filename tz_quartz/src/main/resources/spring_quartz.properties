# ¹Ì¶¨Ç°×ºorg.quartz
# Ö÷Òª·ÖÎªscheduler¡¢threadPool¡¢jobStore¡¢pluginµÈ²¿·Ö
#
#
org.quartz.scheduler.instanceName = crm_junl

#×Ô¶¯Éú³Éid
org.quartz.scheduler.instanceId=AUTO
org.quartz.scheduler.instanceIdGenerator.class=org.quartz.simpl.SimpleInstanceIdGenerator


#Èç¹ûÄúÏ£ÍûQuartz SchedulerÍ¨¹ýRMI×÷Îª·þÎñÆ÷µ¼³ö±¾Éí£¬Ôò½«¡°rmi.export¡±±êÖ¾ÉèÖÃÎªtrue¡£
#ÔÚÍ¬Ò»¸öÅäÖÃÎÄ¼þÖÐÎª'org.quartz.scheduler.rmi.export'ºÍ'org.quartz.scheduler.rmi.proxy'Ö¸¶¨Ò»¸ö'true'ÖµÊÇÃ»ÓÐÒâÒåµÄ,Èç¹ûÄãÕâÑù×ö£¬'export 'Ñ¡Ïî½«±»ºöÂÔ
org.quartz.scheduler.rmi.export = false
#Èç¹ûÒªÁ¬½Ó£¨Ê¹ÓÃ£©Ô¶³Ì·þÎñµÄµ÷¶È³ÌÐò£¬Ôò½«¡°org.quartz.scheduler.rmi.proxy¡±±êÖ¾ÉèÖÃÎªtrue¡£Äú»¹±ØÐëÖ¸¶¨RMI×¢²á±í½ø³ÌµÄÖ÷»úºÍ¶Ë¿Ú - Í¨³£ÊÇ¡°localhost¡±¶Ë¿Ú1099¡£
org.quartz.scheduler.rmi.proxy = false

org.quartz.scheduler.wrapJobExecutionInUserTransaction = false

# ÊµÀý»¯ThreadPoolÊ±£¬Ê¹ÓÃµÄÏß³ÌÀàÎªSimpleThreadPool
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool

# threadCountºÍthreadPriority½«ÒÔsetterµÄÐÎÊ½×¢ÈëThreadPoolÊµÀý
# ²¢·¢¸öÊý  Èç¹ûÄãÖ»ÓÐ¼¸¸ö¹¤×÷Ã¿Ìì´¥·¢¼¸´Î ÄÇÃ´1¸öÏß³Ì¾Í¿ÉÒÔ,Èç¹ûÄãÓÐ³ÉÇ§ÉÏÍòµÄ¹¤×÷£¬Ã¿·ÖÖÓ¶¼ÓÐºÜ¶à¹¤×÷ ÄÇÃ´¾ÃÐèÒª50-100Ö®¼ä.
# Ö»ÓÐ1µ½100Ö®¼äµÄÊý×ÖÊÇ·Ç³£ÊµÓÃµÄ
org.quartz.threadPool.threadCount = 5

# ÓÅÏÈ¼¶ Ä¬ÈÏÖµÎª5
org.quartz.threadPool.threadPriority = 5

#¿ÉÒÔÊÇ¡°true¡±»ò¡°false¡±£¬Ä¬ÈÏÎªfalse¡£
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = true

#ÔÚ±»ÈÏÎª¡°misfired¡±(Ê§»ð)Ö®Ç°£¬µ÷¶È³ÌÐò½«¡°tolerate(ÈÝÈÌ)¡±Ò»¸öTriggers(´¥·¢Æ÷)½«ÆäÏÂÒ»¸öÆô¶¯Ê±¼äÍ¨¹ýµÄºÁÃëÊý¡£Ä¬ÈÏÖµ£¨Èç¹ûÄúÔÚÅäÖÃÖÐÎ´ÊäÈë´ËÊôÐÔ£©Îª60000£¨60Ãë£©
org.quartz.jobStore.misfireThreshold = 5000

# Ä¬ÈÏ´æ´¢ÔÚÄÚ´æÖÐ,RAMJobStore¿ìËÙÇá±ã£¬µ«ÊÇµ±½ø³ÌÖÕÖ¹Ê±£¬ËùÓÐµ÷¶ÈÐÅÏ¢¶¼»á¶ªÊ§¡£
#org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore

#³Ö¾Ã»¯
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX

#ÄúÐèÒªÎªJobStoreÑ¡ÔñÒ»¸öDriverDelegate²ÅÄÜÊ¹ÓÃ¡£DriverDelegate¸ºÔðÖ´ÐÐÌØ¶¨Êý¾Ý¿â¿ÉÄÜÐèÒªµÄÈÎºÎJDBC¹¤×÷¡£
# StdJDBCDelegateÊÇÒ»¸öÊ¹ÓÃ¡°vanilla¡±JDBC´úÂë£¨ºÍSQLÓï¾ä£©À´Ö´ÐÐÆä¹¤×÷µÄÎ¯ÍÐ,ÓÃÓÚÍêÈ«·ûºÏJDBCµÄÇý¶¯³ÌÐò
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate

#¿ÉÒÔ½«¡°org.quartz.jobStore.useProperties¡±ÅäÖÃ²ÎÊýÉèÖÃÎª¡°true¡±£¨Ä¬ÈÏÎªfalse£©£¬ÒÔÖ¸Ê¾JDBCJobStore½«JobDataMapsÖÐµÄËùÓÐÖµ¶¼×÷Îª×Ö·û´®£¬
# Òò´Ë¿ÉÒÔ×÷ÎªÃû³Æ - Öµ¶Ô´æ´¢¶ø²»ÊÇÔÚBLOBÁÐÖÐÒÔÆäÐòÁÐ»¯ÐÎÊ½´æ´¢¸ü¶à¸´ÔÓµÄ¶ÔÏó¡£´Ó³¤Ô¶À´¿´£¬ÕâÊÇ¸ü°²È«µÄ£¬ÒòÎªÄú±ÜÃâÁË½«·ÇStringÀàÐòÁÐ»¯ÎªBLOBµÄÀà°æ±¾ÎÊÌâ¡£
org.quartz.jobStore.useProperties=true
#±íÇ°×º
org.quartz.jobStore.tablePrefix = QRTZ_

#ÉèÖÃÎª¡°true¡±ÒÔ´ò¿ªÈº¼¯¹¦ÄÜ¡£Èç¹ûÄúÓÐ¶à¸öQuartzÊµÀýÊ¹ÓÃÍ¬Ò»×éÊý¾Ý¿â±í£¬Ôò´ËÊôÐÔ±ØÐëÉèÖÃÎª¡°true¡±£¬·ñÔòÄú½«Óöµ½ÆÆ»µ¡£
org.quartz.jobStore.isClustered=false
