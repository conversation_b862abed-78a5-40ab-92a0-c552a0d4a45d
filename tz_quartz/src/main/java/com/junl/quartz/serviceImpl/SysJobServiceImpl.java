package com.junl.quartz.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.quartz.dao.SysJobDao;
import com.junl.quartz.validate.SchedulerValidate;
import com.junl.quartz.vo.SysJobVo;
import com.junl.quartz.entity.SysJobEntity;
import com.junl.quartz.service.SysJobService;
import lombok.extern.log4j.Log4j2;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
@Log4j2
public class SysJobServiceImpl extends ServiceImpl<SysJobDao, SysJobEntity> implements SysJobService {

    @Qualifier("Schedulers")
    @Autowired
    private Scheduler scheduler;


    /**
     * @description: 分页查询列表
     * @param sysJobVo
     */
    @Override
    public PageEntity queryPage(SysJobVo sysJobVo) {
        PageUtils.execute(sysJobVo);
        List<SysJobEntity> list = baseMapper.queryList(sysJobVo);
        return PageUtils.getData(list);
    }

    @Override
    @Transactional
    public boolean insert(SysJobEntity sysJobEntity) {
        Validate.startValidate(new SchedulerValidate(sysJobEntity,this,scheduler));
        try {
            if (scheduler.checkExists(JobKey.jobKey(sysJobEntity.getJobClass(),getCompanyGroup(sysJobEntity)))) {
                throw new RuntimeException("该任务已存在,请检查组名和执行任务的class是否重复");
            }
        }catch (Exception e){
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        start(sysJobEntity);
        baseMapper.insert(sysJobEntity);
        return true;
    }

    @Override
    @Transactional
    public boolean update(SysJobEntity sysJobEntity) {
        Validate.startValidate(new SchedulerValidate(sysJobEntity,this,scheduler));
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(sysJobEntity.getJobClass(), getCompanyGroup(sysJobEntity));
            CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(sysJobEntity.getJobCron());
            CronTrigger trigger =(CronTrigger) scheduler.getTrigger(triggerKey);
            CronTrigger build = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(cronScheduleBuilder).build();
            scheduler.rescheduleJob(triggerKey,build);
        }catch (Exception e){
            log.error("更新定时任务错误: {}",e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        baseMapper.updateById(sysJobEntity);
        return true;
    }

    @Override
    @Transactional
    public boolean stop(String id) {
        SysJobEntity job = getJob(id);
        try {
            scheduler.pauseJob(JobKey.jobKey(job.getJobClass(),getCompanyGroup(job)));
        } catch (SchedulerException e) {
            log.error("停止任务失败: {}",e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        job.setJobStatus(Opposite.ZERO);
        baseMapper.updateById(job);
        return true;
    }

    @Override
    @Transactional
    public boolean restart(String id) {
        SysJobEntity job = getJob(id);
        try {
            scheduler.resumeJob(JobKey.jobKey(job.getJobClass(),getCompanyGroup(job)));
        } catch (SchedulerException e) {
            log.error("重新启动任务失败: {}",e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        job.setJobStatus(Opposite.SINGLE);
        baseMapper.updateById(job);
        return false;
    }

    @Override
    @Transactional
    public boolean start(String id) {
        SysJobEntity job = getJob(id);
        job.setJobStatus(Opposite.SINGLE);
        start(job);
        baseMapper.updateById(job);
        return true;
    }

    @Override
    public boolean again(String id) {
        SysJobEntity job = getJob(id);
        try {
            scheduler.triggerJob(JobKey.jobKey(job.getJobClass(),getCompanyGroup(job)));
        }catch (SchedulerException e){
            log.error("定时任务执行一次失败：{}",e.getMessage());
            throw new RuntimeException("执行任务一次失败 :"+e.getMessage());
        }
        return true;
    }

    @Override
    @Transactional
    public boolean delete(List<String> ids) {
        for (String x : ids) {
            try {
                SysJobEntity job = getJob(x);
                String jobClassName = job.getJobClass();
                String jobGroupName = getCompanyGroup(job);
                scheduler.pauseTrigger(TriggerKey.triggerKey(jobClassName, jobGroupName));
                scheduler.unscheduleJob(TriggerKey.triggerKey(jobClassName,jobGroupName));
                scheduler.deleteJob(JobKey.jobKey(jobClassName, jobGroupName));
                job.setDeleteFlag(Opposite.SINGLE);
                baseMapper.updateById(job);
            }catch (Exception e){
                log.error("删除任务失败：{}",e.getMessage());
                throw new RuntimeException("删除任务失败："+e.getMessage());
            }
        }
        return true;
    }

    private String getCompanyGroup(SysJobEntity sysJobEntity){
        return sysJobEntity.getCompanyId()+sysJobEntity.getJobGroup();
    }


    private SysJobEntity getJob(String id){
        SysJobEntity sysJobEntity = baseMapper.selectById(id);
        if (!Assert.notNull(sysJobEntity)) {
            throw new RuntimeException("找不到该任务");
        }
        return sysJobEntity;
    }

    private void start(SysJobEntity jobEntity){
        try {
            //表示再运行中 就是保存并运行的操作
            if (jobEntity.getJobStatus().equals(Opposite.SINGLE)) {
                Class<?> aClass = Class.forName(jobEntity.getJobClass());
                JobDetail jobDetail = JobBuilder.newJob((Class<? extends Job>) aClass).
                        withIdentity(jobEntity.getJobClass(), getCompanyGroup(jobEntity))
                        .build();

                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(jobEntity.getJobCron());
                //按新的cronExpression表达式构建一个新的trigger
                CronTrigger trigger = TriggerBuilder.newTrigger().
                        withIdentity(jobEntity.getJobClass(), getCompanyGroup(jobEntity))
                        .withSchedule(scheduleBuilder)
                        .build();
                scheduler.scheduleJob(jobDetail, trigger);
            }
        }catch (SchedulerException e){
            log.error("定时任务启动失败： {}",e.getMessage());
            throw new RuntimeException(e.getMessage());
        }catch (ClassNotFoundException e){
            log.error("定时任务找不到执行类 ：{}",e.getMessage());
            throw new RuntimeException("找不到指定的任务类");
        }

    }

}