package com.junl.quartz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.quartz.entity.SysJobEntity;
import com.junl.quartz.vo.SysJobVo;
import com.junl.crm_common.common.PageEntity;


import java.util.List;

/**
* @description:SysJobservice接口
* @author: daiqimeng
**/
public interface SysJobService extends IService<SysJobEntity> {
    PageEntity queryPage(SysJobVo sysJobVo);

    boolean insert(SysJobEntity sysJobEntity);

    boolean update(SysJobEntity sysJobEntity);

    boolean stop(String id);
    boolean restart(String id);

    boolean start(String id);
    boolean again(String id);

    boolean delete(List<String> ids);
}