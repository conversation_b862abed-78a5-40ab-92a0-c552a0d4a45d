package com.junl.quartz.config;

import org.quartz.Scheduler;
import org.quartz.ee.servlet.QuartzInitializerListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Properties;

/**
 * @description: 任务调度器配置
 * @author: daiqimeng
 * @date: 2021/6/915:32
 */

@Configuration
public class SchedulerConfig {

    @Autowired
    private JobFactory jobFactory;


    @Bean(name="SchedulerFactory")
    public SchedulerFactoryBean schedulerFactoryBean(@Autowired DataSource dataSource) throws IOException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setQuartzProperties(quartzProperties());
        //项目启动后5秒后开始作业
        factory.setStartupDelay(60);
        //设置调度器自动运行
        factory.setAutoStartup(true);
        factory.setDataSource(dataSource);
        factory.setSchedulerName("crm_junl");
        factory.setJobFactory(jobFactory);
        return factory;
    }

    @Bean
    public Properties quartzProperties() throws IOException {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        propertiesFactoryBean.setLocation(new ClassPathResource("/spring_quartz.properties"));
        //在quartz.properties中的属性被读取并注入后再初始化对象
        propertiesFactoryBean.afterPropertiesSet();
        return propertiesFactoryBean.getObject();
    }

    /**
     * quartz初始化监听器
     * 这个监听器可以监听到工程的启动，在工程停止再启动时可以让已有的定时任务继续进行。
     * @return
     */
    @Bean
    public QuartzInitializerListener executorListener() {
        return new QuartzInitializerListener();
    }

    /**
     *
     *通过SchedulerFactoryBean获取Scheduler的实例
     */

    @Bean(name="Schedulers")
    public Scheduler scheduler(@Autowired SchedulerFactoryBean schedulerFactoryBean) throws IOException {
        return schedulerFactoryBean.getObject();
    }

}


