package com.junl.quartz.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.restrain.Calibrator;
import com.junl.quartz.entity.SysJobEntity;
import com.junl.quartz.service.SysJobService;
import lombok.extern.log4j.Log4j2;
import org.quartz.CronExpression;
import org.quartz.Scheduler;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/6/109:29
 */
@Log4j2
public class SchedulerValidate  extends Calibrator<SysJobEntity, SysJobService> {
    private Scheduler scheduler;


    public SchedulerValidate(SysJobEntity sysJobEntity, SysJobService service) {
        this(sysJobEntity,service,null);
    }
    public SchedulerValidate(SysJobEntity sysJobEntity, SysJobService service,Scheduler scheduler) {
        super(sysJobEntity, service);
        this.scheduler=scheduler;
    }

    @Override
    protected void validate(SysJobEntity sysJobEntity, SysJobService sysJobService) {
        if (!CronExpression.isValidExpression(sysJobEntity.getJobCron())) {
            throw new RuntimeException("cron表达式不合法");
        }

        if (sysJobService.count(new LambdaQueryWrapper<SysJobEntity>().and(x->x.eq(SysJobEntity::getJobName,sysJobEntity.getJobName())
        .ne(SysJobEntity::getJobId,sysJobEntity.getJobId())))>0) {
            throw new RuntimeException("该任务名称已经存在,请修改后重新提交");
        }
    }
}
