package com.junl.quartz.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SysJobVo
* @author:      daiqimeng
**/
@ApiModel(description = "表-Vo类")
@Data
public class SysJobVo extends ParentDto {
    @ApiModelProperty(notes = "任务名称", allowEmptyValue = true, required = false)
    private  String jobName;
    @ApiModelProperty(notes = "任务状态 0 停止  1运行中", allowEmptyValue = true, required = false)
    private  String jobStatus;
    @ApiModelProperty(notes = "公司Id", allowEmptyValue = true, required = false)
    private  String companyId;

}