package com.junl.quartz.entity;
import java.util.Date;
import java.lang.String;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.junl.crm_common.annotation.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author:      daiqimeng
 **/
@ApiModel(description = "")
@TableName("sys_job")
@Data
public class SysJobEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(notes = "",allowEmptyValue = true, required = false)
    @NotNull
    @TableId
    private String jobId;

    @ApiModelProperty(notes = "任务名称",allowEmptyValue = true, required = false)
    @NotNull
    private String jobName;

    @ApiModelProperty(notes = "执行的class类相对路径",allowEmptyValue = true, required = false)
    @NotNull
    private String jobClass;

    @ApiModelProperty(notes = "组名 用于锁定分组的任务",allowEmptyValue = true, required = false)
    @NotNull
    private String jobGroup;

    @ApiModelProperty(notes = "任务状态 0 停止  1运行中",allowEmptyValue = true, required = false)
    @NotNull
    private String jobStatus;

    @ApiModelProperty(notes = "任务描述",allowEmptyValue = true, required = false)
    private String jobDescribe;

    @ApiModelProperty(notes = "cron表达式 ",allowEmptyValue = true, required = false)
    @NotNull
    private String jobCron;

    @ApiModelProperty(notes = "创建时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间",allowEmptyValue = true, required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(notes = "创建人",allowEmptyValue = true, required = false)
    private String createBy;

    @ApiModelProperty(notes = "更新人",allowEmptyValue = true, required = false)
    private String updateBy;

    @ApiModelProperty(notes = "删除状态 0否 1是",allowEmptyValue = true, required = false)
    private String deleteFlag;

    @ApiModelProperty(notes = "公司Id",allowEmptyValue = true, required = false)
    @NotNull
    private String companyId;

}
