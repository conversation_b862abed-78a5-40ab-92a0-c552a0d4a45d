package com.junl.quartz.controller;

import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.quartz.entity.SysJobEntity;
import com.junl.quartz.service.SysJobService;
import com.junl.quartz.vo.SysJobVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysJob")
@Api(tags = "接口")
public class SysJobController extends ParentController {

    @Autowired
    private SysJobService sysJobService;


    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "表--分页列表查询",notes="表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SysJobVo sysJobVo){
        sysJobVo.setCompanyId(getCompanyId());
        PageEntity page = sysJobService.queryPage(sysJobVo);
        return Result.success(page);
    }


    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "查询详情",notes="查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "jobId", value = "jobId",required=true))
    @GetMapping("/info/{jobId}")
    public Result info(@PathVariable("jobId") String jobId){
        return Result.success(sysJobService.getById(jobId));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "列表--新增", notes = "列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SysJobEntity sysJobEntity){
        sysJobEntity.setCompanyId(getCompanyId());
        sysJobEntity.setJobId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysJobEntity,null));
        RuleTools.setField(sysJobEntity,getSysUserEntity(),true);
        sysJobService.insert(sysJobEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "--修改", notes = "--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SysJobEntity sysJobEntity){
        Validate.startValidate(new NotNullVerification(sysJobEntity,null));
        RuleTools.setField(sysJobEntity,getSysUserEntity(),false);
        sysJobService.update(sysJobEntity);
        return Result.success();
    }

    @ApiOperation("删除任务")
    @PostMapping("/delete")
    public Result delete(@RequestBody List<String> ids){
        return Result.success(sysJobService.delete(ids));
    }


    @ApiOperation("停止任务")
    @GetMapping("/stop/{id}")
    public Result stop(@PathVariable("id")String id){
        return Result.success(sysJobService.stop(id));
    }

    @ApiOperation("重新启动任务(停止任务后的启动)")
    @GetMapping("/restart/{id}")
    public Result restart(@PathVariable("id")String id){
        return Result.success(sysJobService.restart(id));
    }

    @ApiOperation("开始任务")
    @GetMapping("/start/{id}")
    public Result start(@PathVariable("id")String id){
        return Result.success(sysJobService.start(id));
    }

    @ApiOperation("任务立马执行一次")
    @GetMapping("/rightAway/{id}")
    public Result rightAway(@PathVariable("id")String id){
        return Result.success(sysJobService.again(id));
    }



}