stages:
  - start
  - deploy

start-job:
  variables:
    DES: 'Start building the project。。。。。。。。。。。。。。。。。。。。'
  stage: start
  tags:
    - tzbidding
  script:
    - echo $DES


deploy-job:
  variables:
    DES: 'build success  url: http://daiqimeng.top:21002/'
  stage: deploy
  image: docker
  tags:
    - tzbidding
  script:
    - docker build -t admin_test_container .
    - if [ $(docker ps -aq --filter name=admin_test) ]; then docker stop admin_test; docker rm admin_test; fi
    - docker run -d -p 21002:8004 --name admin_test admin_test_container
    - echo $DES
  when: manual
