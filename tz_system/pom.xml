<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.junl</groupId>
		<artifactId>tz_bidding</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<groupId>com.junl</groupId>
	<artifactId>tz_system</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>tz_system</name>
	<description>系统模块</description>
	<properties>

	</properties>

	<dependencies>
<!--		<dependency>-->
<!--			<groupId>com.junl</groupId>-->
<!--			<artifactId>tz_quartz</artifactId>-->
<!--		</dependency>-->


		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_rpc</artifactId>
		</dependency>

		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_work</artifactId>
		</dependency>

		<dependency>
			<groupId>com.junl</groupId>
			<artifactId>tz_msg</artifactId>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>


	<build>
		<finalName>tz-bidding</finalName>
			<plugins>
				<!-- 打jar包时如果不配置该插件，打出来的jar包没有清单文件 -->
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<version>${spring.version}</version>
					<executions>
						<execution>
							<goals>
								<goal>repackage</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<mainClass>com.junl.crm_system.CrmSystemApplication</mainClass>
						<fork>true</fork>
					</configuration>
				</plugin>
			</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
	</build>
</project>
