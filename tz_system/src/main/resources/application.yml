
#全局公共配置文件
server:
  # 服务器的HTTP端口，默认为8080
  port: 8004
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # post请求大小不受限制
    max-http-form-post-size: -1

# Spring配置
spring:
   #引入其他配置文件
  profiles:
    active: '@profiles.active@'
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  jackson:
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 10MB
  #是否开启入参出参日志打印
  logger : false
#  devtools:
#    restart:
#      enabled: true
#      exclude: WEB-INF/**
#      additional-paths: src/main/java


# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

#mybatis-plus  配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      db-type: mysql
    refresh: false
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


smsapi:
  spCode: 223402
  loginName: tz_zhyqsh
  password: 520xxczz






