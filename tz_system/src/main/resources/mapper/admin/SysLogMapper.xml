<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysLogDao">
    <resultMap id="SysLogMap" type="com.junl.crm_common.pojo.admin.SysLogEntity">
        <result property="logId" column="log_id"/>
        <result property="logDescribe" column="log_describe"/>
        <result property="logType" column="log_type"/>
        <result property="logParam" column="log_param"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="companyId" column="company_id"/>
        <result property="createByName" column="user_name"/>
    </resultMap>

    <sql id="SysLog">
            a.log_id,
            a.log_describe,
            a.log_type,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag,
            a.company_id    </sql>

    <select id="queryList" resultMap="SysLogMap">
        select <include refid="SysLog"/>,b.user_name from sys_log a
        left join  sys_user b on  a.create_by=b.user_id
        <where>
            <if test="companyId!=null and companyId !=''">
                AND a.company_id=#{companyId}
            </if>
            <if test="startTime!=null">
                AND date(#{startTime}) &lt;= date(a.create_time) and date(#{endTime}) >=  date(a.create_time)
            </if>
        </where>
        order by a.create_time DESC
    </select>
    <select id="getInfo" resultType="com.junl.crm_common.pojo.admin.SysLogEntity">
        select log_param from sys_log where log_id=#{logId}
    </select>
</mapper>