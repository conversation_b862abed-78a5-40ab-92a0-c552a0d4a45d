<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysDataDao">
    <resultMap id="SysDataMap" type="com.junl.crm_common.pojo.admin.SysDataEntity">
        <result property="id" column="id"/>
        <result property="dataName" column="data_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="companyId" column="company_id"/>
        <result property="createName" column="user_name"/>
        <collection property="dataRole" ofType="com.junl.crm_common.pojo.admin.SysDataRoleEntity">
            <result property="deptId" column="dept_id"/>
            <result property="deptName" column="dept_name"/>
        </collection>
    </resultMap>

    <sql id="SysData">
        a.id,
            a.data_name,
            a.create_time,
            a.create_by,
            a.update_time,
            a.update_by,
            a.company_id</sql>

    <select id="queryList" resultMap="SysDataMap">
        select <include refid="SysData"/>,b.user_name from sys_data a
        left join sys_user b on a.create_by=b.user_id
        <where>
            <if test="dataName!=null and dataName!=''">
                AND a.data_name like CONCAT('%',#{dataName},'%')
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
        </where>
    </select>
    <select id="getInfo" resultMap="SysDataMap">
        select <include refid="SysData"/>,b.dept_id,c.dept_name from sys_data a
        left join sys_data_role b on a.id=b.data_id
        left join sys_dept c on b.dept_id=c.dept_id
        where a.id=#{id}
    </select>
</mapper>