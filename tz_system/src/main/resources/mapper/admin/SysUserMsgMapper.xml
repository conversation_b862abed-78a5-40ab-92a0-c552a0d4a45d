<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysUserMsgDao">
    <resultMap id="SysUserMsgMap" type="com.junl.crm_common.pojo.admin.SysUserMsgEntity">
        <result property="msgId" column="msg_id"/>
        <result property="msgTitle" column="msg_title"/>
        <!--        <result property="msgContent" column="msg_content"/>-->
        <result property="msgAccessory" column="msg_accessory"/>
        <result property="companyId" column="company_id"/>
        <result property="createBy" column="create_by"/>
        <result property="status" column="status"/>
        <result property="reader" column="reader"/>
        <result property="msgType" column="msg_type"/>
        <result property="createDate" column="create_date"/>
        <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <sql id="SysUserMsg">
        a.msg_id,
            a.msg_title,
            a.company_id,
            a.create_by,
            a.status,
            a.reader,
            a.msg_type,
            a.bus_code,
            a.`specific`,
            a.create_date,
            a.delete_flag    </sql>

    <sql id="SysHistory">
        a.msg_id,
            a.msg_title,
            a.company_id,
            a.create_by,
            a.reader,
            a.msg_accessory,
            a.msg_content,
            a.msg_type,
            a.bus_code,
            a.status,
            a.`specific`,
            a.create_date,
            a.delete_flag    </sql>

    <insert id="saveHistory">
        insert into sys_history_msg(msg_id,msg_title,msg_content,msg_accessory,reader,create_by,msg_type,bus_code,`specific`,company_id)
        values (#{msgId},#{msgTitle},#{msgContent},#{msgAccessory},#{reader},#{createBy},#{msgType},#{busCode},#{specific},#{companyId})
    </insert>
    <update id="updateStatus">
        update sys_history_msg set status = 1 where msg_id=#{id}
    </update>
    <update id="updateStatusAccessory">
        update sys_history_msg set status = 1 where
                                                  bus_code = #{busCode}
            and
            msg_accessory = #{msgAccessory}
    </update>

    <select id="queryList" resultMap="SysUserMsgMap">
        select <include refid="SysUserMsg"/> from sys_user_msg a
        <where>
            a.delete_flag=0
            <if test="msgTitle!=null and msgTitle!=''">
                AND a.msg_title like CONCAT('%',#{msgTitle},'%')
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
            <if test="createBy!=null and createBy!=''">
                AND a.create_by=#{createBy}
            </if>
            <if test="status!=null and status!=''">
                AND a.status=#{status}
            </if>
            <if test="msgType!=null and msgType!=''">
                AND a.msg_type=#{msgType}
            </if>
        </where>
    </select>
    <select id="queryHistory" resultType="com.junl.crm_common.pojo.admin.SysUserMsgEntity">
        select <include refid="SysHistory"/> from sys_history_msg a
        <where>
            <if test="busCode!=null">
                AND a.bus_code=#{busCode}
            </if>
            <if test="status!=null">
                AND a.status=#{status}
            </if>
            <if test="msgType">
                and a.msg_type = #{msgType}
            </if>
            <if test="userIds!=null">
                AND a.reader in (
                <foreach collection="userIds" separator="," item="userId">
                    #{userId}
                </foreach>
                )
            </if>
        </where>
        ORDER BY create_date DESC
    </select>
    <select id="getInfo" resultType="com.junl.crm_common.pojo.admin.SysUserMsgEntity">
        select <include refid="SysHistory"/>,a.msg_content from sys_history_msg a where a.msg_id=#{id}
    </select>
</mapper>