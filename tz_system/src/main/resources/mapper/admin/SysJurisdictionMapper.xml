<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysJurisdictionDao">
    <resultMap id="SysJurisdictionMap" type="com.junl.crm_common.pojo.admin.SysJurisdictionEntity">
                <result property="jurisdictionId" column="jurisdiction_id"/>
                <result property="jurisdictionName" column="jurisdiction_name"/>
                <result property="createTime" column="create_time"/>
                <result property="updateTime" column="update_time"/>
                <result property="createBy" column="create_by"/>
                <result property="updateBy" column="update_by"/>
                <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <sql id="SysJurisdiction">
            a.jurisdiction_id,
            a.jurisdiction_name,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag    </sql>
    <update id="delete">
        update sys_jurisdiction set delete_flag=1 where jurisdiction_id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="queryList" resultMap="SysJurisdictionMap">
        select <include refid="SysJurisdiction"/> from sys_jurisdiction a
        <where>
        a.delete_flag=0
        <if test="companyId!=null and companyId!=''">
           and  a.company_id=#{companyId}
        </if>
        <if test="jurisdictionName!=null and jurisdictionName!=''">
            and a.jurisdiction_name like CONCAT('%',#{jurisdictionName},'%')
        </if>
        </where>
    </select>
</mapper>