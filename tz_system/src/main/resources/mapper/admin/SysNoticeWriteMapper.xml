<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysNoticeWriteDao">
    <resultMap id="SysNoticeWriteMap" type="com.junl.crm_common.pojo.admin.SysNoticeWriteEntity">
                <result property="noticeId" column="notice_id"/>
                <result property="noticeTitle" column="notice_title"/>
             <!--   <result property="noticeContent" column="notice_content"/>
                <result property="accessory" column="accessory"/>-->
                <result property="companyId" column="company_id"/>
                <result property="createTime" column="create_time"/>
                <result property="updateTime" column="update_time"/>
                <result property="createBy" column="create_by"/>
                <result property="updateBy" column="update_by"/>
                <result property="deleteFlag" column="delete_flag"/>
                <result property="status" column="status"/>
    </resultMap>

    <sql id="SysNoticeWrite">
            a.notice_id,
            a.notice_title,
            a.company_id,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag,
            a.status    </sql>

    <select id="queryList" resultMap="SysNoticeWriteMap">
        select <include refid="SysNoticeWrite"/> from sys_notice_write a
        <where>
        a.delete_flag=0
        <if test="companyId!=null and companyId!=''">
            and a.company_id=#{companyId}
        </if>
        <if test="noticeTitle!=null and noticeTitle!=''">
            and a.notice_title like concat('%',#{noticeTitle},'%')
        </if>
        </where>
        order by a.create_time DESC,status ASC
    </select>
</mapper>