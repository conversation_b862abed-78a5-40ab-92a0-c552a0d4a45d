<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysMenuDao">
    <resultMap id="SysMenuMap" type="com.junl.crm_common.pojo.admin.SysMenuEntity">
                <id property="menuId" column="menu_id"/>
                <result property="menuName" column="menu_name"/>
                <result property="parentId" column="parent_id"/>
                <result property="orderNum" column="order_num"/>
                <result property="path" column="path"/>
                <result property="component" column="component"/>
                <result property="isFrame" column="is_frame"/>
                <result property="isCache" column="is_cache"/>
                <result property="menuType" column="menu_type"/>
                <result property="visible" column="visible"/>
                <result property="status" column="status"/>
                <result property="icon" column="icon"/>
                <result property="createBy" column="create_by"/>
                <result property="createTime" column="create_time"/>
                <result property="updateBy" column="update_by"/>
                <result property="updateTime" column="update_time"/>
                <result property="remark" column="remark"/>
    </resultMap>

    <sql id="SysMenu">
            a.menu_id,
            a.menu_name,
            a.parent_id,
            a.order_num,
            a.path,
            a.component,
            a.is_frame,
            a.is_cache,
            a.menu_type,
            a.visible,
            a.status,
            a.icon,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.remark    </sql>

</mapper>