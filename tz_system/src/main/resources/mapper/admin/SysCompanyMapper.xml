<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysCompanyDao">
    <resultMap id="SysCompanyMap" type="com.junl.crm_common.pojo.admin.SysCompanyEntity">
        <result property="companyId" column="company_id"/>
        <result property="companyName" column="company_name"/>
        <result property="provinces" column="provinces"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="logo" column="logo"/>
        <result property="subsidiary" column="subsidiary"/>
        <result property="flag" column="flag"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="telephone" column="telephone"/>
        <result property="phone" column="phone"/>
<!--        <result property="accessory" column="accessory"/>-->
        <result property="deleteFlag" column="delete_flag"/>
        <result property="trade" column="trade"/>
        <result property="abbreviation" column="abbreviation"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="SysCompany">
            a.company_id,
            a.company_name,
            a.provinces,
            a.detailed_address,
            a.logo,
            a.subsidiary,
            a.flag,
            a.legal_person,
            a.telephone,
            a.phone,
            a.delete_flag,
            a.trade,
            a.abbreviation,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by    </sql>

    <select id="queryList" resultMap="SysCompanyMap">
        select <include refid="SysCompany"/> from sys_company a
        <where>
            <if test="legalPerson!=null and legalPerson!=''">
                AND a.legal_person like CONCAT('%',#{legalPerson},'%')
            </if>
            <if test="phone!=null and phone!=''">
                AND a.phone like CONCAT(#{phone},'%')
            </if>
            <if test="abbreviation!=null and abbreviation!=''">
                AND a.abbreviation like CONCAT('%',#{abbreviation},'%')
            </if>
            <if test="companyName!=null and companyName!=''">
                AND a.company_name like concat('%',#{companyName},'%')
            </if>
        </where>
    </select>

    <resultMap id="sysUserMap" type="com.junl.crm_common.pojo.admin.SysCompanyEntity">
        <result property="companyId" column="company_id"/>
        <result property="companyName" column="company_name"/>
    </resultMap>

    <select id="getUserCompanyAll" resultMap="sysUserMap">
    select b.company_id,b.company_name from sys_user a left join sys_company b
    on a.company_id=b.company_id
    <where>
        <if test="phone!=null and phone!=''">
            and a.phone=#{phone}
        </if>
        <if test="userName !=null and userName!=''">
            and a.user_name=#{userName}
        </if>
    </where>
    </select>
</mapper>