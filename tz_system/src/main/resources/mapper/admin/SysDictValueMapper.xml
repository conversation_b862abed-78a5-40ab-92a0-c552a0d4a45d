<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysDictValueDao">
    <resultMap id="SysDictValueMap" type="com.junl.crm_common.pojo.admin.SysDictValueEntity">
        <result property="dictValueId" column="dict_value_id"/>
        <result property="dictId" column="dict_id"/>
        <result property="dictValue" column="dict_value"/>
        <result property="dictName" column="dict_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <sql id="SysDictValue">
            a.dict_value_id,
            a.dict_id,
            a.dict_value,
            a.dict_name,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag    </sql>
    <delete id="deletes">
        delete from sys_dict_value   where  dict_value_id in (
         <foreach collection="ids" separator="," item="id">
             #{id}
         </foreach>
        )
    </delete>

    <select id="queryList" resultMap="SysDictValueMap">
        select <include refid="SysDictValue"/> from sys_dict_value a
        <where>
        <if test="dictId!=null and dict!=''">
            AND a.dict_id=#{dictId}
        </if>
        </where>
    </select>
</mapper>