<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysDictDao">
    <resultMap id="SysDictMap" type="com.junl.crm_common.pojo.admin.SysDictEntity">
        <id property="dictId" column="dict_id"/>
        <result property="dictName" column="dict_name"/>
        <result property="dictFlag" column="dict_flag"/>
        <result property="companyId" column="company_id"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <sql id="SysDict">
        a.dict_id,
            a.dict_name,
            a.dict_flag,
            a.company_id,
            a.sort,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag    </sql>

    <select id="queryList" resultMap="SysDictMap">
        select <include refid="SysDict"/> from sys_dict a
        <where>
            a.delete_flag=0
            <if test="dictName!=null and dictName!=''">
                AND a.dict_name like CONCAT('%',#{dictName},'%')
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
            <if test="dictFlag!=null and dictFlag!=''">
                AND a.dict_flag like CONCAT('%',#{dictFlag},'%')
            </if>
        </where>
    </select>
    <select id="queryValue" resultMap="com.junl.crm_system.admin.dao.SysDictValueDao.SysDictValueMap">
        select <include refid="com.junl.crm_system.admin.dao.SysDictValueDao.SysDictValue"/> from sys_dict_value a
        where a.dict_id=#{dictId}
    </select>

    <resultMap id="sysDictMapValue" type="com.junl.crm_common.pojo.admin.SysDictEntity">
        <id property="dictId" column="dict_id"/>
        <result property="dictName" column="dict_name"/>
        <result property="dictFlag" column="dict_flag"/>
        <result property="companyId" column="company_id"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
        <collection property="values" ofType="com.junl.crm_common.pojo.admin.SysDictValueEntity">
            <id property="dictValueId" column="dict_value_id"/>
            <result property="dictValue" column="dict_value"/>
            <result property="dictId" column="dict_ids"/>
            <result property="dictName" column="dict_names"/>
        </collection>
    </resultMap>

    <select id="queryDictAll" resultMap="sysDictMapValue">
        select <include refid="SysDict"/>,b.dict_value_id,b.dict_name as dict_names,b.dict_id as dict_ids,
        b.dict_value
        from sys_dict a
        left join sys_dict_value b on a.dict_id =b.dict_id
        where a.company_id=#{companyId}
    </select>
</mapper>