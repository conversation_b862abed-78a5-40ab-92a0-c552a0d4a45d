<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysHistoryMsgDao">
    <resultMap id="SysHistoryMsgMap" type="com.junl.crm_common.pojo.admin.SysHistoryMsgEntity">
        <result property="msgId" column="msg_id"/>
        <result property="msgTitle" column="msg_title"/>
        <result property="msgContent" column="msg_content"/>
        <result property="msgAccessory" column="msg_accessory"/>
        <result property="companyId" column="company_id"/>
        <result property="createBy" column="create_by"/>
        <result property="specific" column="specific"/>
        <result property="status" column="status"/>
        <result property="reader" column="reader"/>
        <result property="busCode" column="bus_code"/>
        <result property="msgType" column="msg_type"/>
        <result property="createDate" column="create_date"/>
        <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <sql id="SysHistoryMsg">
            a.msg_id,
            a.msg_title,
            a.msg_content,
            a.msg_accessory,
            a.company_id,
            a.create_by,
            a.specific,
            a.status,
            a.reader,
            a.bus_code,
            a.msg_type,
            a.create_date,
            a.delete_flag    </sql>

    <select id="queryList" resultMap="SysHistoryMsgMap">
        select
        <include refid="SysHistoryMsg"/>
        from sys_history_msg a
        <where>
            <if test="msgTitle!=null and msgTitle!=''">
                AND a.msg_title=#{msgTitle}
            </if>
            <if test="reader!=null and reader!=''">
                AND a.reader=#{reader}
            </if>
            ORDER BY a.`status` asc,a.create_date desc
        </where>
    </select>
</mapper>