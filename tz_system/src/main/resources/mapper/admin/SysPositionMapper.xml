<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysPositionDao">
    <resultMap id="SysPositionMap" type="com.junl.crm_common.pojo.admin.SysPositionEntity">
        <id property="positionId" column="position_id"/>
        <result property="positionName" column="position_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="deptName" column="dept_name"/>
        <collection property="users" ofType="com.junl.crm_common.pojo.admin.SysUserEntity">
            <id property="userId" column="user_id"/>
            <result property="userName" column="user_name"/>
            <result property="realName" column="real_name"/>
        </collection>
    </resultMap>

    <sql id="SysPosition">
            a.position_id,
            a.position_name,
            a.dept_id,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag,
            a.company_id    </sql>

    <select id="queryList" resultMap="SysPositionMap">
        select <include refid="SysPosition"/>,b.dept_name from sys_position a
        left join sys_dept b  on a.dept_id=b.dept_id
        <where>
            a.delete_flag=0
            <if test="positionName!=null and positionName!=''">
                AND a.position_name like CONCAT('%',#{positionName},'%')
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
        </where>
    </select>
    <select id="getPositionAll" resultMap="SysPositionMap">
        select <include refid="SysPosition"/>,b.user_id,b.user_name,b.status,b.real_name from sys_position a
         left join sys_user b on a.position_id=b.position_id
        where a.dept_id=#{deptId}
        and a.delete_flag=0
    </select>
</mapper>