<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysUserDao">
    <resultMap id="SysUserMap" type="com.junl.crm_common.pojo.admin.SysUserEntity">
        <id property="userId" column="user_id"/>
        <result property="realName" column="real_name"/>
        <result property="userName" column="user_name"/>
        <result property="companyId" column="company_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="positionId" column="position_id"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="password" column="password"/>
        <result property="roleId" column="role_id"/>
        <result property="sex" column="sex"/>
        <result property="headImg" column="head_img"/>
        <result property="finallyLoginTime" column="finally_login_time"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="deptName" column="dept_name"/>
        <result property="positionName" column="position_name"/>
        <result property="identityCode" column="identity_code"/>
        <result property="dataId" column="data_id"/>
    </resultMap>

    <sql id="SysUser">
            a.identity_code,
            a.user_id,
            a.real_name,
            a.user_name,
            a.company_id,
            a.dept_id,
            a.position_id,
            a.email,
            a.phone,
            a.password,
            a.role_id,
            a.sex,
            a.head_img,
            a.finally_login_time,
            a.status,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag,
            a.data_id</sql>
    <update id="updateOpenId">
        update sys_user_sole set open_id=#{openId}
        where sole_id=#{soleId}
    </update>

    <select id="queryList" resultMap="SysUserMap">
        select <include refid="SysUser"/>,b.dept_name,c.position_name from sys_user a
        left join sys_dept b on a.dept_id=b.dept_id
        left join sys_position c on a.position_id=c.position_id
        <where>
            a.delete_flag=0 and (a.dept_id != '*' or a.dept_id is null)
            <if test="userName!=null and userName!=''">
                AND a.user_name like CONCAT('%',#{userName},'%')
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
            <if test="phone!=null and phone!=''">
                AND a.phone like CONCAT(#{phone},'%')
            </if>
            <if test="status!=null and status!=''">
                AND a.status=#{status}
            </if>
            <if test="deptId!=null and deptId!=''">
                ANd a.dept_id=#{deptId}
            </if>
            <if test="startTime!=null">
                AND date(#{startTime}) &lt;= date(a.create_time) and date(#{endTime}) >=  date(a.create_time)
            </if>
            <if test="identityStatus!=null">
                AND a.identity_code=#{identityStatus}
            </if>
        </where>
    </select>
    <select id="getUsers" resultMap="SysUserMap">
        select <include refid="SysUser"/> from sys_user a where
        a.company_id=#{companyId}
        AND a.delete_flag=0
        AND (a.dept_id is null or a.dept_id =' ')
    </select>
    <select id="getInfo" resultMap="SysUserMap">
        select <include refid="SysUser"/>,b.dept_name,c.position_name from sys_user a
        left join sys_dept b on a.dept_id=b.dept_id
        left join sys_position c on a.position_id=c.position_id
        where a.user_id=#{userId}
    </select>
    <select id="getUserEntity" resultType="com.junl.crm_common.pojo.admin.SysUserEntity">
        select <include refid="SysUser"/> from sys_user a
        where a.user_name=#{userName} and a.phone =#{phone} and a.delete_flag=0
        and a.company_id != '*' limit 1
    </select>
    <select id="getUser" resultType="com.junl.crm_common.pojo.admin.SysUserEntity">
        select <include refid="SysUser"/> from sys_user a
        where a.phone =#{phone} and  a.delete_flag=0
        and a.company_id != '*'limit 1
    </select>
    <select id="getEntity" resultType="com.junl.crm_common.pojo.admin.SysUserEntity">
        select <include refid="SysUser"/> from sys_user a
        where a.user_name=#{userName} and a.delete_flag=0
        limit 1
    </select>
    <select id="getSoleId" resultType="com.junl.crm_common.pojo.admin.SysUserSoleEntity">
        select id ,user_id,sole_id,open_id from sys_user_sole where user_id=#{userId} limit 1
    </select>
</mapper>