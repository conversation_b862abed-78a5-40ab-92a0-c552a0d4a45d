<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysDataRoleDao">
    <resultMap id="SysDataRoleMap" type="com.junl.crm_common.pojo.admin.SysDataRoleEntity">
                <result property="id" column="id"/>
                <result property="dataId" column="data_id"/>
                <result property="deptId" column="dept_id"/>
    </resultMap>

    <sql id="SysDataRole">
            a.id,
            a.data_id,
            a.dept_id    </sql>

    <select id="queryList" resultMap="SysDataRoleMap">
        select <include refid="SysDataRole"/> from sys_data_role a
        <where>
        </where>
    </select>
</mapper>