<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysRoleDao">
    <resultMap id="SysRoleMap" type="com.junl.crm_common.pojo.admin.SysRoleEntity">
        <result property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="jurisdiction" column="jurisdiction"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="jurisdictionName" column="jurisdiction_name"/>
    </resultMap>

    <sql id="SysRole">
            a.role_id,
            a.role_name,
            a.jurisdiction,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag
             </sql>
    <update id="delete">
        update sys_role  set delete_flag=1 where role_id in(
        <foreach collection="ids" separator="," item="id">
            #{id}
        </foreach>
        )
    </update>

    <select id="queryList" resultMap="SysRoleMap">
        select <include refid="SysRole"/>,b.jurisdiction_name from sys_role a
        left join sys_jurisdiction b on a.jurisdiction =b.jurisdiction_id
        <where>
            a.delete_flag=0
            <if test="roleName!=null and roleName!=''">
                AND a.role_name like CONCAT('%',#{roleName},'%')
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
        </where>
    </select>
</mapper>