<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.junl.crm_system.admin.dao.SysDeptDao">
    <resultMap id="SysDeptMap" type="com.junl.crm_common.pojo.admin.SysDeptEntity">
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="companyId" column="company_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="principal" column="principal"/>
        <result property="telephone" column="telephone"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <sql id="SysDept">
            a.dept_id,
            a.dept_name,
            a.company_id,
            a.parent_id,
            a.principal,
            a.telephone,
            a.create_time,
            a.update_time,
            a.create_by,
            a.update_by,
            a.delete_flag
            </sql>
    <update id="delete">
        update sys_dept set delete_flag=1 where dept_id in (
        <foreach collection="ids" separator="," item="id">
            #{id}
        </foreach>
        )
    </update>

    <select id="queryList" resultMap="SysDeptMap">
        select <include refid="SysDept"/> from sys_dept a
        <where>
            a.delete_flag=0
            <if test="deptName!=null and deptName!=''">
                AND a.dept_name like CONCAT('%',#{deptName},'%')
            </if>
            <if test="principal!=null and principal!=''">
                AND a.principal like CONCAT('%',#{principal},'%')
            </if>
            <if test="parentId!=null and parentId!=''">
                AND a.parent_id=#{parentId}
            </if>
            <if test="companyId!=null and companyId!=''">
                AND a.company_id=#{companyId}
            </if>
        </where>
    </select>

    <resultMap id="SysDeptAll" type="com.junl.crm_common.pojo.admin.SysDeptEntity">
        <id property="deptId" column="deptId"/>
        <result property="deptName" column="deptName"/>
        <collection property="users" ofType="com.junl.crm_common.pojo.admin.SysUserEntity">
            <id property="userId" column="userId"/>
            <result property="userName" column="userName"/>
            <result property="realName" column="realName"/>
            <result property="status" column="status"/>
        </collection>
        <collection property="children" ofType="com.junl.crm_common.pojo.admin.SysDeptEntity">
            <id property="deptId" column="deptIds"/>
            <result property="deptName" column="deptNames"/>
        </collection>
    </resultMap>

    <select id="getAllDept" resultMap="SysDeptAll">
    select a.dept_id as deptId,
    a.dept_name as deptName,
    b.dept_id as deptIds,
    b.dept_name as deptNames,
    c.user_id as userId,
    c.user_name as userName,
    c.real_name as realName,
    c.status as status
    from sys_dept a left join sys_dept b
    on a.dept_id=b.parent_id and b.delete_flag=0
    left join sys_user c
    on a.dept_id=c.dept_id and (c.position_id is null or c.position_id =' ')
    and c.delete_flag=0
    <where>
        a.company_id=#{companyId}
        and a.delete_flag=0
        <if test="parentId!=null and parentId!=''">
            and a.parent_id=#{parentId}
        </if>
        <if test="id!=null and id!=''">
            and a.dept_id=#{id}
        </if>
    </where>
    </select>
</mapper>