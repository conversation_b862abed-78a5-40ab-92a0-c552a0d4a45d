#开发环境配置文件

#dubbo配置
dubbo:
  application:
    name: junl-crm-saas
    logger: log4j2
  registry:
    address: zookeeper://**************:10007
    #simplified: true
  protocol:
    name: dubbo
    port: 20880
  metadata-report:
    timeout: 10000
    address: zookeeper://**************:10007
  config-center:
    timeout: 10000
  consumer:
    timeout: 10000


#reids 配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **********************************************************************************************************************************************************
        username: junl
        password: junl123!@#
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  redis:
    # 地址
    host: **************
    port: 10005
    # 密码
    password: junl123
    # 连接超时时间
    timeout: 5000
    jedis:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 10
        # 连接池中的最大空闲连接
        max-idle: 100
        # 连接池的最大数据库连接数
        max-active: 500
        #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 20000
        time-between-eviction-runs: 3600000
    #rabbit配置
  rabbitmq:
    custom:
      username: admin
      password: junladmin
      virtualHost: /
      host: **************
      port: 10001

#阿里云文件上传参数配置
aliYunImag:
  endpoint: oss-cn-shanghai.aliyuncs.com
  accessKeyId: LTAIi26DriMkElk0
  accessKeySecret: dwTRY0UgvgYNmkAp6C4jXTMdDlpPxH
  aliyunBucketName: crmmms

#minio
minio:
  endpoint: ************
  accessKey: adminminio
  secretKey: adminminio
  port: 9000
  url: ************:9000

#微信公众号
wx:
  appid: wx361547ce36eb2185
  appsecret: 17fa4725b493ac5477c9ba174fd2456f
  auth_url: http://tzjja.tzpec.com.cn/tx
#微信小程序
wxminapp:
  appId: wx58540823b666fe3c
  appSecret: 82725ac1ba6e499b64303e723c1a8561
  wxLoginUrl: https://api.weixin.qq.com/sns/jscode2session