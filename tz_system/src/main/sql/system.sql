-- MySQL dump 10.13  Distrib 8.0.31, for Win64 (x86_64)
--
-- Host: localhost    Database: tz_bidding
-- ------------------------------------------------------
-- Server version	8.0.31

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `flow_sel_bidding`
--

DROP TABLE IF EXISTS `flow_sel_bidding`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flow_sel_bidding` (
                                    `id` bigint NOT NULL COMMENT '主键',
                                    `bidding_id` bigint DEFAULT NULL COMMENT '真实业务的主键',
                                    `round_id` bigint DEFAULT NULL COMMENT '轮次id',
                                    `underway_id` bigint DEFAULT NULL COMMENT '审批id',
                                    `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                                    `plan_id` bigint DEFAULT NULL COMMENT '计划主ID',
                                    `bidding_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT '' COMMENT '计划编码',
                                    `mode_type` int DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                                    `period_num` int DEFAULT '0' COMMENT '周期数',
                                    `round_num` int DEFAULT '0' COMMENT '场次数',
                                    `bidding_status` int DEFAULT '0' COMMENT '竞价状态1-竞价审核中2-竞价中3-竞价结束',
                                    `plan_name` varchar(128) DEFAULT '' COMMENT '竞价名称',
                                    `plan_start_date` datetime DEFAULT NULL COMMENT '当前竞价开始时间',
                                    `plan_end_time` datetime DEFAULT NULL COMMENT '当前竞价结束时间',
                                    `traffic_mode` int DEFAULT '0' COMMENT '运输方式0陆运',
                                    `delivery_mode` int DEFAULT '0' COMMENT '交货方式0配送',
                                    `describe` varchar(500) DEFAULT NULL COMMENT '交易描述',
                                    `quality` varchar(50) DEFAULT '0' COMMENT '质量标准',
                                    `delivery_start_date` datetime DEFAULT NULL COMMENT '交货日期起',
                                    `delivery_end_time` datetime DEFAULT NULL COMMENT '交货日期止',
                                    `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                    `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除0-否1-是',
                                    `create_by` varchar(32) DEFAULT '' COMMENT '创建人',
                                    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_by` varchar(32) DEFAULT NULL COMMENT '编辑人',
                                    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='销售竞价审批详情记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_sel_bidding_customer`
--

DROP TABLE IF EXISTS `flow_sel_bidding_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flow_sel_bidding_customer` (
                                             `id` bigint NOT NULL,
                                             `sequence` bigint DEFAULT NULL COMMENT '序列轮次id',
                                             `customer_id` bigint DEFAULT NULL COMMENT '客户id',
                                             `detail_address` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '收货详细地址',
                                             `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
                                             `is_bidding` tinyint(1) DEFAULT '0' COMMENT '是否参与竞价0-否1-是',
                                             `is_success` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否竞价成功0-否1-是',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价序号关联所选企业表审批记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_sel_bidding_product`
--

DROP TABLE IF EXISTS `flow_sel_bidding_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flow_sel_bidding_product` (
                                            `id` bigint NOT NULL COMMENT '主键',
                                            `sequence_id` bigint DEFAULT NULL COMMENT '序列id',
                                            `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
                                            `product_id` bigint DEFAULT NULL COMMENT '商品id',
                                            `mode_type` int DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                                            `base_fee` decimal(32,2) DEFAULT '0.00' COMMENT '制定基价',
                                            `contract_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '合同量',
                                            `stock_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '库存量',
                                            `daily_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '日产量',
                                            `sale_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '预计可销售量',
                                            `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='竞价商品审批记录详情表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_sel_bidding_success`
--

DROP TABLE IF EXISTS `flow_sel_bidding_success`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flow_sel_bidding_success` (
                                            `id` bigint NOT NULL,
                                            `underway_id` bigint DEFAULT NULL COMMENT '审批主流程id',
                                            `bidding_id` bigint DEFAULT NULL COMMENT '竞价id',
                                            `sequence_id` bigint DEFAULT NULL COMMENT '轮次id',
                                            `offer_id` bigint DEFAULT NULL COMMENT '报价人',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                            `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                            `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='审批记录竞价企业交易成功表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_sel_bidding_success_product`
--

DROP TABLE IF EXISTS `flow_sel_bidding_success_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flow_sel_bidding_success_product` (
                                                    `id` bigint NOT NULL,
                                                    `success_id` bigint DEFAULT NULL COMMENT '成功主表idid',
                                                    `product_id` bigint DEFAULT NULL COMMENT '产品id',
                                                    `product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品名称',
                                                    `price` decimal(10,2) DEFAULT NULL COMMENT '报价',
                                                    `count` decimal(10,2) DEFAULT NULL COMMENT '报量',
                                                    `add_price` decimal(10,2) DEFAULT NULL COMMENT '追加价',
                                                    `add_count` decimal(10,2) DEFAULT NULL COMMENT '追加量',
                                                    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                    `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                                    `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价企业交易成功表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_sel_plan`
--

DROP TABLE IF EXISTS `flow_sel_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flow_sel_plan` (
                                 `id` bigint NOT NULL COMMENT '主键',
                                 `plan_id` bigint DEFAULT NULL COMMENT '记录真正业务的主键Id',
                                 `underway_id` bigint DEFAULT NULL COMMENT '审批主流程id',
                                 `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                                 `plan_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '' COMMENT '计划编码',
                                 `mode_type` int NOT NULL DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                                 `plan_status` int NOT NULL DEFAULT '1' COMMENT '计划状态1-待提交2-计划审核中3-计划已通过 4已驳回',
                                 `plan_name` varchar(128) NOT NULL DEFAULT '' COMMENT '计划名称',
                                 `plan_start_date` datetime NOT NULL COMMENT '计划时段开始时间',
                                 `plan_end_time` datetime NOT NULL COMMENT '计划时段结束时间',
                                 `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                 `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除0-否1-是',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_by` bigint DEFAULT NULL COMMENT '编辑人',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                                 `period_num` int DEFAULT NULL COMMENT '期数',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='审批记录销售计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_sel_plan_product_detail`
--

DROP TABLE IF EXISTS `flow_sel_plan_product_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flow_sel_plan_product_detail` (
                                                `id` bigint NOT NULL COMMENT '主键',
                                                `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                                                `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
                                                `product_id` bigint DEFAULT NULL COMMENT '商品id',
                                                `plan_id` bigint DEFAULT NULL COMMENT '计划主ID',
                                                `mode_type` int NOT NULL DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                                                `base_fee` decimal(32,2) DEFAULT '0.00' COMMENT '制定基价',
                                                `contract_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '合同量',
                                                `stock_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '库存量',
                                                `daily_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '日产量',
                                                `sale_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '预计可销售量',
                                                `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='审批记录销售计划明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_blob_triggers`
--

DROP TABLE IF EXISTS `qrtz_blob_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_blob_triggers` (
                                      `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                      `TRIGGER_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                      `TRIGGER_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                      `BLOB_DATA` blob,
                                      PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_calendars`
--

DROP TABLE IF EXISTS `qrtz_calendars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_calendars` (
                                  `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                  `CALENDAR_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                  `CALENDAR` blob NOT NULL,
                                  PRIMARY KEY (`SCHED_NAME`,`CALENDAR_NAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_cron_triggers`
--

DROP TABLE IF EXISTS `qrtz_cron_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_cron_triggers` (
                                      `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                      `TRIGGER_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                      `TRIGGER_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                      `CRON_EXPRESSION` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                      `TIME_ZONE_ID` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                      PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_fired_triggers`
--

DROP TABLE IF EXISTS `qrtz_fired_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_fired_triggers` (
                                       `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                       `ENTRY_ID` varchar(95) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                       `TRIGGER_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                       `TRIGGER_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                       `INSTANCE_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                       `FIRED_TIME` bigint NOT NULL,
                                       `SCHED_TIME` bigint NOT NULL,
                                       `PRIORITY` int NOT NULL,
                                       `STATE` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                       `JOB_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                       `JOB_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                       `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                       `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                       PRIMARY KEY (`SCHED_NAME`,`ENTRY_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_job_details`
--

DROP TABLE IF EXISTS `qrtz_job_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_job_details` (
                                    `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `JOB_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `JOB_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `DESCRIPTION` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                    `JOB_CLASS_NAME` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `IS_DURABLE` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `IS_UPDATE_DATA` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                    `JOB_DATA` blob,
                                    PRIMARY KEY (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_locks`
--

DROP TABLE IF EXISTS `qrtz_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_locks` (
                              `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                              `LOCK_NAME` varchar(40) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                              PRIMARY KEY (`SCHED_NAME`,`LOCK_NAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_paused_trigger_grps`
--

DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_paused_trigger_grps` (
                                            `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                            `TRIGGER_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                            PRIMARY KEY (`SCHED_NAME`,`TRIGGER_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_scheduler_state`
--

DROP TABLE IF EXISTS `qrtz_scheduler_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_scheduler_state` (
                                        `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                        `INSTANCE_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                        `LAST_CHECKIN_TIME` bigint NOT NULL,
                                        `CHECKIN_INTERVAL` bigint NOT NULL,
                                        PRIMARY KEY (`SCHED_NAME`,`INSTANCE_NAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_simple_triggers`
--

DROP TABLE IF EXISTS `qrtz_simple_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simple_triggers` (
                                        `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                        `TRIGGER_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                        `TRIGGER_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                        `REPEAT_COUNT` bigint NOT NULL,
                                        `REPEAT_INTERVAL` bigint NOT NULL,
                                        `TIMES_TRIGGERED` bigint NOT NULL,
                                        PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_simprop_triggers`
--

DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simprop_triggers` (
                                         `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                         `TRIGGER_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                         `TRIGGER_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                         `STR_PROP_1` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                         `STR_PROP_2` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                         `STR_PROP_3` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                         `INT_PROP_1` int DEFAULT NULL,
                                         `INT_PROP_2` int DEFAULT NULL,
                                         `LONG_PROP_1` bigint DEFAULT NULL,
                                         `LONG_PROP_2` bigint DEFAULT NULL,
                                         `DEC_PROP_1` decimal(13,4) DEFAULT NULL,
                                         `DEC_PROP_2` decimal(13,4) DEFAULT NULL,
                                         `BOOL_PROP_1` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                         `BOOL_PROP_2` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                         PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_triggers`
--

DROP TABLE IF EXISTS `qrtz_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_triggers` (
                                 `SCHED_NAME` varchar(120) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                 `TRIGGER_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                 `TRIGGER_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                 `JOB_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                 `JOB_GROUP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                 `DESCRIPTION` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                 `NEXT_FIRE_TIME` bigint DEFAULT NULL,
                                 `PREV_FIRE_TIME` bigint DEFAULT NULL,
                                 `PRIORITY` int DEFAULT NULL,
                                 `TRIGGER_STATE` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                 `TRIGGER_TYPE` varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                 `START_TIME` bigint NOT NULL,
                                 `END_TIME` bigint DEFAULT NULL,
                                 `CALENDAR_NAME` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
                                 `MISFIRE_INSTR` smallint DEFAULT NULL,
                                 `JOB_DATA` blob,
                                 PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
                                 KEY `SCHED_NAME` (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recvsms`
--

DROP TABLE IF EXISTS `recvsms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recvsms` (
                           `smsIndex` char(32) NOT NULL COMMENT '主键',
                           `sendNumber` varchar(60) NOT NULL DEFAULT '' COMMENT '回复短信的号码',
                           `smsContent` varchar(500) NOT NULL DEFAULT '' COMMENT '回复短信的内容',
                           `smsTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '回复短信的时间',
                           `callMdn` varchar(50) DEFAULT NULL COMMENT '回复人号码',
                           PRIMARY KEY (`smsIndex`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='短信回复表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_approval_definition`
--

DROP TABLE IF EXISTS `sel_approval_definition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_approval_definition` (
                                           `id` bigint NOT NULL,
                                           `dept_id` bigint DEFAULT NULL COMMENT '部门Id',
                                           `company_id` bigint DEFAULT NULL COMMENT '公司id',
                                           `schedule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '流程json串',
                                           `bus_code` tinyint DEFAULT NULL COMMENT '业务类型 0销售计划审核 1 提交竞价审核 2 交易完成审核',
                                           `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='流程定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_approval_flow`
--

DROP TABLE IF EXISTS `sel_approval_flow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_approval_flow` (
                                     `id` bigint NOT NULL,
                                     `underway_id` bigint DEFAULT NULL COMMENT '进行中的审批流程主健',
                                     `approver` bigint DEFAULT NULL COMMENT '审批人',
                                     `result` tinyint(1) DEFAULT '0' COMMENT '0未审核 同意1  2驳回',
                                     `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `parent_id` char(19) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '父节点',
                                     `approver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '审批人名称',
                                     `panel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '节点名称',
                                     `update_time` datetime DEFAULT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `parent` (`parent_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='审批流程进度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_approval_underway`
--

DROP TABLE IF EXISTS `sel_approval_underway`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_approval_underway` (
                                         `id` bigint NOT NULL,
                                         `company_id` bigint DEFAULT NULL COMMENT '公司id',
                                         `bus_type` tinyint(1) DEFAULT NULL COMMENT '业务类型 0销售计划审核 1 提交竞价审核 2 交易完成审核',
                                         `bus_id` bigint DEFAULT NULL COMMENT '业务id',
                                         `schedule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '流程json串',
                                         `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                         `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `is_delete` tinyint(1) DEFAULT '0' COMMENT '0有效的  1无效的(已经审核通过或驳回的)',
                                         `is_repeal` tinyint(1) DEFAULT '0' COMMENT '是否撤销  0否 1是',
                                         `is_start` tinyint(1) DEFAULT '0' COMMENT '0未开始 1已开始',
                                         `sort` int DEFAULT NULL COMMENT '同一个业务反复提交 查询以最大值为最新的',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='正在进行中的流程';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding`
--

DROP TABLE IF EXISTS `sel_bidding`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding` (
                               `id` bigint NOT NULL COMMENT '主键',
                               `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                               `group_id` bigint DEFAULT NULL COMMENT '分组id',
                               `plan_id` bigint DEFAULT NULL COMMENT '计划主ID',
                               `bidding_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT '' COMMENT '计划编码',
                               `mode_type` int DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                               `period_num` int DEFAULT '0' COMMENT '周期数',
                               `round_num` int DEFAULT '0' COMMENT '场次数',
                               `bidding_status` int DEFAULT '0' COMMENT '竞价状态1-竞价审核中2-竞价中3-竞价结束',
                               `plan_name` varchar(128) DEFAULT '' COMMENT '竞价名称',
                               `plan_start_date` datetime DEFAULT NULL COMMENT '当前竞价开始时间',
                               `plan_end_time` datetime DEFAULT NULL COMMENT '当前竞价结束时间',
                               `traffic_mode` varchar(50) DEFAULT '0' COMMENT '运输方式',
                               `delivery_mode` int DEFAULT '0' COMMENT '交货方式0配送',
                               `describe` varchar(500) DEFAULT '' COMMENT '交易描述',
                               `quality` varchar(50) DEFAULT '' COMMENT '质量标准',
                               `delivery_start_date` datetime DEFAULT NULL COMMENT '交货日期起',
                               `delivery_end_time` datetime DEFAULT NULL COMMENT '交货日期止',
                               `remark` varchar(500) DEFAULT '' COMMENT '备注',
                               `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除0-否1-是',
                               `create_by` varchar(32) DEFAULT '' COMMENT '创建人',
                               `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_by` varchar(32) DEFAULT NULL COMMENT '编辑人',
                               `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='销售竞价';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding_customer`
--

DROP TABLE IF EXISTS `sel_bidding_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding_customer` (
                                        `id` bigint NOT NULL,
                                        `sequence` bigint DEFAULT NULL COMMENT '序列轮次id',
                                        `customer_id` bigint DEFAULT NULL COMMENT '客户id',
                                        `detail_address` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '收货详细地址',
                                        `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
                                        `is_bidding` tinyint(1) DEFAULT '0' COMMENT '是否参与竞价0-否1-是',
                                        `is_success` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否竞价成功0-否1-是',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价序号关联所选企业表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding_customer_offer`
--

DROP TABLE IF EXISTS `sel_bidding_customer_offer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding_customer_offer` (
                                              `id` bigint NOT NULL,
                                              `relevance_id` bigint DEFAULT NULL COMMENT '关联 bidding_customer主键',
                                              `product_id` bigint DEFAULT NULL COMMENT '商品id',
                                              `product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商品名称',
                                              `price` decimal(10,2) DEFAULT NULL COMMENT '竟价',
                                              `count` double(10,2) DEFAULT NULL COMMENT '竟量',
                                              `offerer` bigint DEFAULT NULL COMMENT '竟量人',
                                              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '报价时间',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价企业报量表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding_fee_record`
--

DROP TABLE IF EXISTS `sel_bidding_fee_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding_fee_record` (
                                          `id` bigint NOT NULL,
                                          `sequence_id` bigint DEFAULT NULL COMMENT '轮次',
                                          `base_fee` decimal(10,2) DEFAULT NULL COMMENT '基价',
                                          `product_id` bigint DEFAULT NULL COMMENT '产品id',
                                          `product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品名称',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价的基价调整记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding_product`
--

DROP TABLE IF EXISTS `sel_bidding_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding_product` (
                                       `id` bigint NOT NULL COMMENT '主键',
                                       `sequence_id` bigint DEFAULT NULL COMMENT '序列id',
                                       `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
                                       `product_id` bigint DEFAULT NULL COMMENT '商品id',
                                       `mode_type` int DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                                       `base_fee` decimal(32,2) DEFAULT '0.00' COMMENT '制定基价',
                                       `contract_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '合同量',
                                       `stock_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '库存量',
                                       `daily_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '日产量',
                                       `sale_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '预计可销售量',
                                       `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='竞价商品详情表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding_sequence`
--

DROP TABLE IF EXISTS `sel_bidding_sequence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding_sequence` (
                                        `id` bigint NOT NULL COMMENT 'id',
                                        `sequence` int DEFAULT NULL COMMENT '场次',
                                        `bidding_id` bigint DEFAULT NULL COMMENT '竞价id',
                                        `bidding_status` int DEFAULT '1' COMMENT '竞价状态',
                                        `plan_start_date` datetime DEFAULT NULL COMMENT '开始时间',
                                        `plan_end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价轮次关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding_success`
--

DROP TABLE IF EXISTS `sel_bidding_success`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding_success` (
                                       `id` bigint NOT NULL,
                                       `offer_id` bigint DEFAULT NULL COMMENT '企业id  也就是报价人',
                                       `bidding_id` bigint DEFAULT NULL COMMENT '竞价id',
                                       `sequence_id` bigint DEFAULT NULL COMMENT '轮次id',
                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                       `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价企业交易成功表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_bidding_success_product`
--

DROP TABLE IF EXISTS `sel_bidding_success_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_bidding_success_product` (
                                               `id` bigint NOT NULL,
                                               `success_id` bigint DEFAULT NULL COMMENT '成功主表idid',
                                               `product_id` bigint DEFAULT NULL COMMENT '产品id',
                                               `product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品名称',
                                               `price` decimal(10,2) DEFAULT NULL COMMENT '报价',
                                               `count` decimal(10,2) DEFAULT NULL COMMENT '报量',
                                               `add_price` decimal(10,2) DEFAULT NULL COMMENT '追加价',
                                               `add_count` decimal(10,2) DEFAULT NULL COMMENT '追加量',
                                               `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                               `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                               `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='竞价企业交易成功表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_customer`
--

DROP TABLE IF EXISTS `sel_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_customer` (
                                `id` bigint NOT NULL COMMENT '主键',
                                `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                                `customer_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
                                `customer_name` varchar(128) NOT NULL DEFAULT '' COMMENT '客户名称',
                                `business_license` varchar(500) DEFAULT NULL COMMENT '营业执照附件',
                                `manage_product` varchar(50) DEFAULT NULL COMMENT '经营产品类型:1燃料油类 2化工类 3苯类 4其它',
                                `product_file` varchar(500) DEFAULT NULL COMMENT '产品证件',
                                `deputy` varchar(50) DEFAULT NULL COMMENT '法人',
                                `deputy_card_no` varchar(50) DEFAULT NULL COMMENT '身份证号',
                                `deputy_phone` varchar(50) DEFAULT NULL COMMENT '法人电话',
                                `register_fee` decimal(32,2) DEFAULT '0.00' COMMENT '注册资金',
                                `telephone` varchar(50) DEFAULT NULL COMMENT '公司电话',
                                `head_img` varchar(500) DEFAULT NULL COMMENT '头像',
                                `authorization_file` varchar(500) DEFAULT NULL COMMENT '授权书',
                                `is_auth` int NOT NULL DEFAULT '0' COMMENT '是否是否企业认证0-否1-待审核 3审核失败 4审核通过',
                                `customer_status` int NOT NULL DEFAULT '0' COMMENT '客户审核状态1-待审核2-已审核',
                                `level_type` int NOT NULL DEFAULT '0' COMMENT '活跃度等级类型1-一级2-二级3-三级4-其它',
                                `province` varchar(20) DEFAULT '' COMMENT '省',
                                `city` varchar(20) DEFAULT '' COMMENT '市',
                                `region` varchar(20) DEFAULT '' COMMENT '区',
                                `detail_address` varchar(200) DEFAULT NULL COMMENT '详细地址',
                                `postal_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '' COMMENT '邮政编码',
                                `open_id` varchar(100) DEFAULT NULL COMMENT '微信openid',
                                `is_wx_auth` tinyint(1) DEFAULT '0' COMMENT '是否微信认证 0否 1是',
                                `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                `is_enable` int NOT NULL DEFAULT '0' COMMENT '是否启用0-否1-是',
                                `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除0-否1-是',
                                `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_by` bigint DEFAULT NULL COMMENT '编辑人',
                                `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='企业表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_customer_group`
--

DROP TABLE IF EXISTS `sel_customer_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_customer_group` (
                                      `id` bigint NOT NULL COMMENT '主键',
                                      `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                                      `group_name` varchar(128) NOT NULL DEFAULT '' COMMENT '组名称',
                                      `create_by` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_by` bigint DEFAULT NULL COMMENT '编辑人',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='企业分组';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_group_relevance`
--

DROP TABLE IF EXISTS `sel_group_relevance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_group_relevance` (
                                       `id` bigint NOT NULL COMMENT 'id',
                                       `customer_id` bigint DEFAULT NULL,
                                       `group_id` bigint DEFAULT NULL COMMENT '组id',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_inquiry`
--

DROP TABLE IF EXISTS `sel_inquiry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_inquiry` (
                               `id` bigint NOT NULL COMMENT '主键',
                               `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                               `plan_id` bigint DEFAULT NULL COMMENT '计划主ID',
                               `group_id` bigint DEFAULT NULL COMMENT '分组id',
                               `mode_type` int DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                               `inquiry_status` int DEFAULT '0' COMMENT '询价状态1-待发布2-询价中3询价结束',
                               `inquiry_name` varchar(128) DEFAULT '' COMMENT '询价名称',
                               `inquiry_start_date` datetime DEFAULT NULL COMMENT '询价开始时间',
                               `inquiry_end_time` datetime DEFAULT NULL COMMENT '询价结束时间',
                               `remark` varchar(500) DEFAULT '' COMMENT '备注',
                               `is_delete` int DEFAULT '0' COMMENT '是否删除0-否1-是',
                               `create_by` bigint DEFAULT NULL COMMENT '创建人',
                               `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_by` bigint DEFAULT NULL COMMENT '编辑人',
                               `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                               `period_num` int DEFAULT NULL COMMENT '期数',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='销售询价主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_inquiry_customer`
--

DROP TABLE IF EXISTS `sel_inquiry_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_inquiry_customer` (
                                        `id` bigint NOT NULL COMMENT 'id',
                                        `customer_id` bigint DEFAULT NULL COMMENT '企业id',
                                        `inquiry_id` bigint DEFAULT NULL COMMENT '询价Id',
                                        `is_inquiry` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否报过价 0否 1是',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='询价关联企业id';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_inquiry_offer`
--

DROP TABLE IF EXISTS `sel_inquiry_offer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_inquiry_offer` (
                                     `id` bigint NOT NULL COMMENT 'id',
                                     `relevancy_id` bigint DEFAULT NULL COMMENT '关联表id',
                                     `product_id` bigint DEFAULT NULL COMMENT '商品id',
                                     `product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商品名称',
                                     `price` decimal(10,2) DEFAULT NULL COMMENT '报价',
                                     `count` double(10,2) DEFAULT NULL COMMENT '量(吨)',
                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '报价时间',
                                     `offerer` bigint DEFAULT NULL COMMENT '报价人',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='企业对于询价的报价报量记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_log`
--

DROP TABLE IF EXISTS `sel_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_log` (
                           `log_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                           `type` tinyint(1) DEFAULT '0' COMMENT '类型:0小程序，1PC端',
                           `log_describe` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模块描述',
                           `log_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作类型 crud',
                           `log_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '入参',
                           `log_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '内容',
                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                           `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                           `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                           `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                           PRIMARY KEY (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='系统日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_mode`
--

DROP TABLE IF EXISTS `sel_mode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_mode` (
                            `id` bigint NOT NULL COMMENT '主键',
                            `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                            `mode_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '' COMMENT '模式编码',
                            `mode_type` int NOT NULL DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                            `mode_name` varchar(128) NOT NULL DEFAULT '' COMMENT '模式名称',
                            `is_enable` int NOT NULL DEFAULT '0' COMMENT '是否启用0-禁用1-启用',
                            `remark` varchar(500) DEFAULT '' COMMENT '备注',
                            `create_by` bigint NOT NULL COMMENT '创建人',
                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_by` bigint DEFAULT NULL COMMENT '编辑人',
                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='销售计划模式';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_plan`
--

DROP TABLE IF EXISTS `sel_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_plan` (
                            `id` bigint NOT NULL COMMENT '主键',
                            `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                            `plan_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '' COMMENT '计划编码',
                            `mode_type` int NOT NULL DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                            `plan_status` int NOT NULL DEFAULT '1' COMMENT '计划状态1-待提交2-计划审核中3-计划已通过 4已驳回',
                            `plan_name` varchar(128) NOT NULL DEFAULT '' COMMENT '计划名称',
                            `plan_start_date` datetime NOT NULL COMMENT '计划时段开始时间',
                            `plan_end_time` datetime NOT NULL COMMENT '计划时段结束时间',
                            `remark` varchar(500) DEFAULT '' COMMENT '备注',
                            `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除0-否1-是',
                            `create_by` bigint DEFAULT NULL COMMENT '创建人',
                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_by` bigint DEFAULT NULL COMMENT '编辑人',
                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                            `period_num` int DEFAULT NULL COMMENT '期数',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='销售计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_plan_product_detail`
--

DROP TABLE IF EXISTS `sel_plan_product_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_plan_product_detail` (
                                           `id` bigint NOT NULL COMMENT '主键',
                                           `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                                           `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
                                           `product_id` bigint DEFAULT NULL COMMENT '商品id',
                                           `plan_id` bigint DEFAULT NULL COMMENT '计划主ID',
                                           `mode_type` int NOT NULL DEFAULT '0' COMMENT '模式类型1-竞价竞量模式2-零售模式3-纯竞价模式4-定价模式',
                                           `base_fee` decimal(32,2) DEFAULT '0.00' COMMENT '制定基价',
                                           `contract_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '合同量',
                                           `stock_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '库存量',
                                           `daily_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '日产量',
                                           `sale_quantity` decimal(32,2) DEFAULT '0.00' COMMENT '预计可销售量',
                                           `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='销售计划明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sel_product`
--

DROP TABLE IF EXISTS `sel_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sel_product` (
                               `id` bigint NOT NULL COMMENT '主键',
                               `company_id` bigint DEFAULT NULL COMMENT '公司Id',
                               `product_code` varchar(128) NOT NULL DEFAULT '' COMMENT '商品编码',
                               `product_name` varchar(128) NOT NULL DEFAULT '' COMMENT '商品名称',
                               `product_type` int NOT NULL DEFAULT '0' COMMENT '商品分类1燃料油类 2化工类 3苯类 4其它',
                               `remark` varchar(500) DEFAULT '' COMMENT '备注',
                               `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除0-否1-是',
                               `create_by` bigint NOT NULL COMMENT '创建人',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_by` bigint DEFAULT NULL COMMENT '编辑人',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='商品表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sendsms`
--

DROP TABLE IF EXISTS `sendsms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sendsms` (
                           `smsIndex` char(32) NOT NULL COMMENT '主键',
                           `phoneNumber` varchar(1200) NOT NULL DEFAULT '' COMMENT '手机号码，一次最多发送100个手机号码',
                           `smsContent` varchar(500) NOT NULL DEFAULT '' COMMENT '短信内容',
                           `smsTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插表时间',
                           `smsUser` char(32) DEFAULT NULL COMMENT '信息发送人',
                           `staTime` datetime DEFAULT NULL COMMENT '预约发送开始时间。无值需写入null',
                           `endTime` datetime DEFAULT NULL COMMENT '预约发送结束时间。无值需写入null',
                           `status` int NOT NULL DEFAULT '0' COMMENT '发送状态（0-待发送,1-发送成功,2-发送失败）',
                           `extno` varchar(10) DEFAULT NULL COMMENT '扩展接入号，最多支持3位扩展',
                           `resultCode` varchar(10) DEFAULT NULL COMMENT '发送结果编码',
                           `resultDesc` varchar(500) DEFAULT NULL COMMENT '发送结果描述',
                           `failList` varchar(2000) DEFAULT NULL COMMENT '发送失败的手机号码',
                           PRIMARY KEY (`smsIndex`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='发送短信表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_company`
--

DROP TABLE IF EXISTS `sys_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_company` (
                               `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                               `company_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司名称',
                               `provinces` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '省市区',
                               `detailed_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '详细地址',
                               `logo` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'logo',
                               `subsidiary` tinyint(1) DEFAULT '0' COMMENT '是否子公司 0否 1是',
                               `flag` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司唯一标识 不区分公司和子公司的标识 主要用于区分是否是一家公司',
                               `legal_person` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '法人',
                               `telephone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司电话',
                               `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '法人联系方式',
                               `accessory` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '附件 （主要用于传输公司营业执照 等证明）',
                               `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                               `trade` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所属行业',
                               `abbreviation` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '简称',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                               `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                               `principal` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '负责人',
                               PRIMARY KEY (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='公司表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_data`
--

DROP TABLE IF EXISTS `sys_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_data` (
                            `id` bigint NOT NULL,
                            `data_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据权限名称',
                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据权限名称',
                            `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                            `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统数据权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_data_role`
--

DROP TABLE IF EXISTS `sys_data_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_data_role` (
                                 `id` bigint NOT NULL,
                                 `data_id` bigint DEFAULT NULL COMMENT '数据权限主表Id',
                                 `dept_id` bigint DEFAULT NULL COMMENT '部门id(按部门维度区分数据权限)',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dept` (
                            `dept_id` bigint NOT NULL,
                            `dept_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门名称',
                            `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                            `parent_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '父级id',
                            `principal` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '负责人',
                            `telephone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门联系电话',
                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                            `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                            `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                            PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_dict`
--

DROP TABLE IF EXISTS `sys_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict` (
                            `dict_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                            `dict_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字典名称',
                            `dict_flag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字典标识',
                            `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                            `sort` int DEFAULT NULL COMMENT '排序',
                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                            `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                            `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                            PRIMARY KEY (`dict_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='字典主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_dict_value`
--

DROP TABLE IF EXISTS `sys_dict_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_value` (
                                  `dict_value_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                  `dict_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字典主表Id',
                                  `dict_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '显示值',
                                  `dict_value` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '可选值',
                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                  `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                                  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                                  PRIMARY KEY (`dict_value_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='字典映射表 (值表)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_history_msg`
--

DROP TABLE IF EXISTS `sys_history_msg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_history_msg` (
                                   `msg_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                   `msg_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '消息标题',
                                   `msg_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '消息内容',
                                   `msg_accessory` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '消息附件',
                                   `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                                   `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                   `specific` tinyint(1) DEFAULT NULL COMMENT '锁定具体业务',
                                   `status` tinyint(1) DEFAULT '0' COMMENT '阅读标识 0否 1是',
                                   `reader` bigint DEFAULT NULL COMMENT '阅读人',
                                   `bus_code` tinyint(1) DEFAULT NULL COMMENT '业务编码',
                                   `msg_type` tinyint(1) DEFAULT NULL COMMENT '消息类型 暂定（0公告）',
                                   `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '接收时间',
                                   `delete_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除 0否 1是',
                                   PRIMARY KEY (`msg_id`) USING BTREE,
                                   KEY `reder_index` (`reader`) COMMENT '阅读人索引增加检索速度'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='用户历史信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_identity`
--

DROP TABLE IF EXISTS `sys_identity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_identity` (
                                `identity_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                `identity_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '系统身份名称',
                                `identity_code` tinyint DEFAULT NULL COMMENT '系统身份编码 0 超级管理员 1管理员 2部门管理员 3普通用户',
                                PRIMARY KEY (`identity_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='系统身份表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_job`
--

DROP TABLE IF EXISTS `sys_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job` (
                           `job_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                           `job_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务名称',
                           `job_class` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '执行的class类相对路径',
                           `job_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '组名 用于锁定分组的任务',
                           `job_status` tinyint(1) DEFAULT '0' COMMENT '任务状态 0 停止  1运行中',
                           `job_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务描述',
                           `job_cron` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'cron表达式 ',
                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                           `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                           `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除状态 0否 1是',
                           `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                           PRIMARY KEY (`job_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='系统定时任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_jurisdiction`
--

DROP TABLE IF EXISTS `sys_jurisdiction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_jurisdiction` (
                                    `jurisdiction_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                    `jurisdiction_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限名称',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                    `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                                    `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                                    `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
                                    `jurisdiction_flag` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '权限标识 *号代表获取所有菜单 1则根据关联表查询',
                                    PRIMARY KEY (`jurisdiction_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_log`
--

DROP TABLE IF EXISTS `sys_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log` (
                           `log_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                           `log_describe` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模块描述',
                           `log_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作类型 crud',
                           `log_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '入参',
                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                           `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                           `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                           `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                           PRIMARY KEY (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='系统日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu` (
                            `menu_id` bigint NOT NULL COMMENT '菜单ID',
                            `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
                            `parent_id` char(32) DEFAULT '0' COMMENT '父菜单ID',
                            `order_num` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
                            `path` varchar(200) DEFAULT '' COMMENT '路由地址',
                            `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
                            `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
                            `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
                            `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
                            `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
                            `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
                            `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `remark` varchar(500) DEFAULT '' COMMENT '备注',
                            `company_id` char(32) DEFAULT NULL COMMENT '公司id',
                            PRIMARY KEY (`menu_id`,`order_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_menu_connect_juris`
--

DROP TABLE IF EXISTS `sys_menu_connect_juris`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu_connect_juris` (
                                          `id` bigint NOT NULL,
                                          `jurisdiction_id` bigint DEFAULT NULL COMMENT '权限id',
                                          `menu_id` bigint DEFAULT NULL COMMENT '菜单id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='权限菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_notice_write`
--

DROP TABLE IF EXISTS `sys_notice_write`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_notice_write` (
                                    `notice_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                    `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公告标题',
                                    `notice_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '公告内容',
                                    `accessory` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '公告附件',
                                    `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                    `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                                    `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                                    `status` tinyint(1) DEFAULT '0' COMMENT '状态，0未发布 1已发布 ',
                                    PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='发布公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_position`
--

DROP TABLE IF EXISTS `sys_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_position` (
                                `position_id` bigint NOT NULL,
                                `position_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '岗位名称',
                                `dept_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门id',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                                `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                                `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
                                PRIMARY KEY (`position_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='岗位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
                            `role_id` bigint NOT NULL,
                            `role_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '角色名称',
                            `jurisdiction` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限id',
                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                            `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                            `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                            `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
                            PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
                            `user_id` bigint NOT NULL,
                            `real_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户真实姓名',
                            `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户名称',
                            `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                            `dept_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门Id',
                            `identity_code` tinyint(1) DEFAULT NULL COMMENT '系统身份标识',
                            `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮箱',
                            `position_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '职位Id',
                            `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '手机号码',
                            `data_id` bigint DEFAULT '0' COMMENT '数据权限id',
                            `password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '密码',
                            `role_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '角色Id',
                            `sex` tinyint(1) DEFAULT NULL COMMENT '性别 0女 1男',
                            `head_img` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '头像',
                            `finally_login_time` datetime DEFAULT NULL COMMENT '最后登陆时间',
                            `status` tinyint(1) DEFAULT '0' COMMENT '账号状态 0正常 1冻结',
                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                            `update_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                            `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识 0否 1是',
                            PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='系统用户';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_user_msg`
--

DROP TABLE IF EXISTS `sys_user_msg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_msg` (
                                `msg_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                `msg_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '消息标题',
                                `msg_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '消息内容',
                                `msg_accessory` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '消息附件',
                                `company_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司Id',
                                `create_by` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                `status` tinyint(1) DEFAULT '0' COMMENT '阅读标识 0否 1是',
                                `reader` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '阅读人',
                                `specific` tinyint(1) DEFAULT NULL COMMENT '具体业务锁定',
                                `bus_code` tinyint(1) DEFAULT NULL COMMENT '业务编码',
                                `msg_type` tinyint(1) DEFAULT NULL COMMENT '消息类型 暂定（0公告）',
                                `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '接收时间',
                                `delete_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除 0否 1是',
                                PRIMARY KEY (`msg_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_user_sole`
--

DROP TABLE IF EXISTS `sys_user_sole`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_sole` (
                                 `id` bigint NOT NULL,
                                 `user_id` bigint DEFAULT NULL COMMENT '用户Id',
                                 `sole_id` bigint DEFAULT NULL COMMENT '用户唯一标识  一用户在多家公司',
                                 `open_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信openId',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户唯一标识关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wx_customer`
--

DROP TABLE IF EXISTS `wx_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wx_customer` (
                               `id` char(32) NOT NULL COMMENT '主键',
                               `union_id` varchar(255) DEFAULT NULL COMMENT '公共平台唯一标识',
                               `open_id` varchar(500) DEFAULT NULL COMMENT '用户微信唯一标识',
                               `nick_name` varchar(100) DEFAULT NULL COMMENT '微信昵称',
                               `head_img` varchar(1000) DEFAULT NULL COMMENT '微信头像',
                               `sex` int DEFAULT NULL COMMENT '性别 1=男性；2=女性；3=未知',
                               `country` varchar(50) DEFAULT NULL COMMENT '国家',
                               `province` varchar(50) DEFAULT NULL COMMENT '省',
                               `city` varchar(200) DEFAULT NULL COMMENT '市',
                               `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
                               `phone` varchar(12) DEFAULT NULL COMMENT '电话号码',
                               `register_mode` int DEFAULT NULL COMMENT '获取方式 1：微信小程序，2：公众号',
                               `create_by` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_by` varchar(32) DEFAULT NULL COMMENT '编辑人',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='微信客户表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-03-18 14:04:23




INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865167668360101888', '商品分类', 'product_type', '*', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865560038956187648', '模式类型', 'status_type', '*', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865667829217738752', '计划状态', 'plan_status', '*', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('867333934608924672', '审批业务', 'approve_status', '*', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868133588221149184', '询价状态', 'inquiry_status', '*', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137268395753472', '竞价状态', 'bidding_status', '*', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745634913857536', '商品分类', 'product_type', '875745632334360576', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635069046784', '模式类型', 'status_type', '875745632334360576', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635224236032', '计划状态', 'plan_status', '875745632334360576', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635379425280', '审批业务', 'approve_status', '875745632334360576', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635513643008', '询价状态', 'inquiry_status', '875745632334360576', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635689803776', '竞价状态', 'bidding_status', '875745632334360576', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190063546368', '商品分类', 'product_type', '875746187542769664', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190214541312', '模式类型', 'status_type', '875746187542769664', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190369730560', '计划状态', 'plan_status', '875746187542769664', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190520725504', '审批业务', 'approve_status', '875746187542769664', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190650748928', '询价状态', 'inquiry_status', '875746187542769664', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190852075520', '竞价状态', 'bidding_status', '875746187542769664', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624305561600', '商品分类', 'product_type', '875773621952557056', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624464945152', '模式类型', 'status_type', '875773621952557056', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624611745792', '计划状态', 'plan_status', '875773621952557056', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624754352128', '审批业务', 'approve_status', '875773621952557056', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624875986944', '询价状态', 'inquiry_status', '875773621952557056', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625035370496', '竞价状态', 'bidding_status', '875773621952557056', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673120870400', '商品分类', 'product_type', '876437669991919616', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673305419776', '模式类型', 'status_type', '876437669991919616', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673464803328', '计划状态', 'plan_status', '876437669991919616', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673624186880', '审批业务', 'approve_status', '876437669991919616', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673762598912', '询价状态', 'inquiry_status', '876437669991919616', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673942953984', '竞价状态', 'bidding_status', '876437669991919616', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250540011520', '商品分类', 'product_type', '877608248065372160', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250711977984', '模式类型', 'status_type', '877608248065372160', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250871361536', '计划状态', 'plan_status', '877608248065372160', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251026550784', '审批业务', 'approve_status', '877608248065372160', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251156574208', '询价状态', 'inquiry_status', '877608248065372160', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251332734976', '竞价状态', 'bidding_status', '877608248065372160', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918073603080192', '商品分类', 'product_type', '877918067093520384', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074077036544', '模式类型', 'status_type', '877918067093520384', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074555187200', '计划状态', 'plan_status', '877918067093520384', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075134001152', '审批业务', 'approve_status', '877918067093520384', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075549237248', '询价状态', 'inquiry_status', '877918067093520384', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076203548672', '竞价状态', 'bidding_status', '877918067093520384', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918083958816768', '商品分类', 'product_type', '877918075712815104', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084390830080', '模式类型', 'status_type', '877918075712815104', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084843814912', '计划状态', 'plan_status', '877918075712815104', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085259051008', '审批业务', 'approve_status', '877918075712815104', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085598789632', '询价状态', 'inquiry_status', '877918075712815104', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086039191552', '竞价状态', 'bidding_status', '877918075712815104', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052137871257600', '商品分类', 'product_type', '880052135472115712', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138039029760', '模式类型', 'status_type', '880052135472115712', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138185830400', '计划状态', 'plan_status', '880052135472115712', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138332631040', '审批业务', 'approve_status', '880052135472115712', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138458460160', '询价状态', 'inquiry_status', '880052135472115712', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138622038016', '竞价状态', 'bidding_status', '880052135472115712', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348298526720', '商品分类', 'product_type', '882223345698058240', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348453715968', '模式类型', 'status_type', '882223345698058240', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348613099520', '计划状态', 'plan_status', '882223345698058240', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348768288768', '审批业务', 'approve_status', '882223345698058240', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348902506496', '询价状态', 'inquiry_status', '882223345698058240', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349078667264', '竞价状态', 'bidding_status', '882223345698058240', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713240866816', '商品分类', 'product_type', '883708710464237568', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713417027584', '模式类型', 'status_type', '883708710464237568', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713568022528', '计划状态', 'plan_status', '883708710464237568', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713765154816', '审批业务', 'approve_status', '883708710464237568', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713895178240', '询价状态', 'inquiry_status', '883708710464237568', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714071339008', '竞价状态', 'bidding_status', '883708710464237568', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876392189952', '商品分类', 'product_type', '884450873749778432', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876555767808', '模式类型', 'status_type', '884450873749778432', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876698374144', '计划状态', 'plan_status', '884450873749778432', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876887117824', '审批业务', 'approve_status', '884450873749778432', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877017141248', '询价状态', 'inquiry_status', '884450873749778432', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877180719104', '竞价状态', 'bidding_status', '884450873749778432', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451331860049920', '商品分类', 'product_type', '884451329251192832', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332002656256', '模式类型', 'status_type', '884451329251192832', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332149456896', '计划状态', 'plan_status', '884451329251192832', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332334006272', '审批业务', 'approve_status', '884451329251192832', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332459835392', '询价状态', 'inquiry_status', '884451329251192832', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332619218944', '竞价状态', 'bidding_status', '884451329251192832', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193578074112', '商品分类', 'product_type', '890203190717558784', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193758429184', '模式类型', 'status_type', '890203190717558784', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193913618432', '计划状态', 'plan_status', '890203190717558784', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194114945024', '审批业务', 'approve_status', '890203190717558784', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194249162752', '询价状态', 'inquiry_status', '890203190717558784', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194425323520', '竞价状态', 'bidding_status', '890203190717558784', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012027338752', '商品分类', 'product_type', '896449008508317696', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012199305216', '模式类型', 'status_type', '896449008508317696', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012354494464', '计划状态', 'plan_status', '896449008508317696', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012543238144', '审批业务', 'approve_status', '896449008508317696', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012673261568', '询价状态', 'inquiry_status', '896449008508317696', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012845228032', '竞价状态', 'bidding_status', '896449008508317696', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673525641216', '商品分类', 'product_type', '897056670740623360', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673672441856', '模式类型', 'status_type', '897056670740623360', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673815048192', '计划状态', 'plan_status', '897056670740623360', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673999597568', '审批业务', 'approve_status', '897056670740623360', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674125426688', '询价状态', 'inquiry_status', '897056670740623360', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674289004544', '竞价状态', 'bidding_status', '897056670740623360', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981154439168', '商品分类', 'product_type', '897057978402975744', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981297045504', '模式类型', 'status_type', '897057978402975744', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981443846144', '计划状态', 'plan_status', '897057978402975744', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981624201216', '审批业务', 'approve_status', '897057978402975744', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981745836032', '询价状态', 'inquiry_status', '897057978402975744', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981909413888', '竞价状态', 'bidding_status', '897057978402975744', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546637815808', '商品分类', 'product_type', '901113543836020736', null, '2021-07-15 01:47:26', '2021-07-15 01:47:26', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546801393664', '模式类型', 'status_type', '901113543836020736', null, '2021-07-16 03:46:35', '2021-07-16 03:46:35', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546948194304', '计划状态', 'plan_status', '901113543836020736', null, '2021-07-16 10:54:54', '2021-07-16 10:54:54', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547132743680', '审批业务', 'approve_status', '901113543836020736', null, '2021-07-21 01:15:27', '2021-07-21 01:15:27', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547317293056', '询价状态', 'inquiry_status', '901113543836020736', null, '2021-07-23 06:12:56', '2021-07-23 06:12:56', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict (dict_id, dict_name, dict_flag, company_id, sort, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547476676608', '竞价状态', 'bidding_status', '901113543836020736', null, '2021-07-23 06:27:34', '2021-07-23 06:28:09', '864906344300396544', '864906344300396544', 0);


INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865169883661451264', '865167668360101888', '燃料油类', '1', '2021-07-15 01:56:14', '2021-07-15 01:56:14', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865169980562456576', '865167668360101888', '化工类', '2', '2021-07-15 01:56:37', '2021-07-15 01:56:37', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865170036908736512', '865167668360101888', '苯类', '3', '2021-07-15 01:56:51', '2021-07-15 01:56:51', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865170076746235904', '865167668360101888', '其他', '4', '2021-07-15 01:57:00', '2021-07-15 01:57:00', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865560358977388544', '865560038956187648', '竞价竟量', '1', '2021-07-16 03:47:51', '2021-07-16 03:47:51', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865560420717543424', '865560038956187648', '零售', '2', '2021-07-16 03:48:06', '2021-07-16 03:48:06', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865560455832256512', '865560038956187648', '竞价', '3', '2021-07-16 03:48:14', '2021-07-16 03:48:14', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865560499050364928', '865560038956187648', '定价', '4', '2021-07-16 03:48:24', '2021-07-16 03:48:24', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865668168893448192', '865667829217738752', '未提交审核', '1', '2021-07-16 10:56:15', '2021-07-16 10:56:15', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865668225415888896', '865667829217738752', '审核中', '2', '2021-07-16 10:56:28', '2021-07-16 10:57:11', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865668249147260928', '865667829217738752', '审核通过', '3', '2021-07-16 10:56:34', '2021-07-16 10:57:14', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('865668310212132864', '865667829217738752', '已驳回', '4', '2021-07-16 10:56:48', '2021-07-16 10:56:48', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('867334384204759040', '867333934608924672', '销售计划审核', '0', '2021-07-21 01:17:14', '2021-07-21 01:17:14', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('867334504728084480', '867333934608924672', '竞价审核', '1', '2021-07-21 01:17:43', '2021-07-21 01:17:43', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('867334562424930304', '867333934608924672', '交易成功', '2', '2021-07-21 01:17:57', '2021-07-21 01:17:57', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868133895630077952', '868133588221149184', '未发布', '1', '2021-07-23 06:14:09', '2021-07-23 06:14:09', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868133938953043968', '868133588221149184', '已发布', '2', '2021-07-23 06:14:20', '2021-07-23 07:58:39', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868134001389453312', '868133588221149184', '询价中', '3', '2021-07-23 06:14:35', '2021-07-23 07:59:00', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137598940463104', '868137268395753472', '竞价审核中', '1', '2021-07-23 06:28:52', '2021-07-23 06:28:52', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137685154381824', '868137268395753472', '竞价审核已驳回', '2', '2021-07-23 06:29:13', '2021-07-23 06:29:13', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137712153116672', '868137268395753472', '竞价审核成功', '3', '2021-07-23 06:29:19', '2021-07-23 06:29:19', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137754658193408', '868137268395753472', '竞价中', '4', '2021-07-23 06:29:29', '2021-07-23 06:30:50', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137815022616576', '868137268395753472', '竞价结束', '5', '2021-07-23 06:29:44', '2021-07-23 06:29:44', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137908979220480', '868137268395753472', '交易完成审核', '6', '2021-07-23 06:30:06', '2021-07-23 06:30:06', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868137956840423424', '868137268395753472', '交易完成审核未通过', '7', '2021-07-23 06:30:18', '2021-07-23 06:30:18', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868138009357303808', '868137268395753472', '交易完成', '8', '2021-07-23 06:30:30', '2021-07-23 06:30:30', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868149926083805184', '868133588221149184', '询价结束', '4', '2021-07-23 07:17:51', '2021-07-23 08:01:28', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('868161319256309760', '868133588221149184', '已提交竞价', '5', '2021-07-23 08:03:08', '2021-07-23 08:03:08', '864906344300396544', '864906344300396544', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745634959994880', '875745634913857536', '燃料油类', '1', '2021-08-13 06:20:29', '2021-08-13 06:20:29', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745634959994881', '875745634913857536', '化工类', '2', '2021-08-13 06:20:29', '2021-08-13 06:20:29', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745634959994882', '875745634913857536', '苯类', '3', '2021-08-13 06:20:29', '2021-08-13 06:20:29', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745634959994883', '875745634913857536', '其他', '4', '2021-08-13 06:20:29', '2021-08-13 06:20:29', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635115184128', '875745635069046784', '竞价竟量', '1', '2021-08-13 06:20:29', '2021-08-13 06:20:29', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635115184129', '875745635069046784', '零售', '2', '2021-08-13 06:20:29', '2021-08-13 06:20:29', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635115184130', '875745635069046784', '竞价', '3', '2021-08-13 06:20:29', '2021-08-13 06:20:29', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635115184131', '875745635069046784', '定价', '4', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635270373376', '875745635224236032', '未提交审核', '1', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635270373377', '875745635224236032', '审核中', '2', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635270373378', '875745635224236032', '审核通过', '3', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635270373379', '875745635224236032', '已驳回', '4', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635425562624', '875745635379425280', '销售计划审核', '0', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635425562625', '875745635379425280', '竞价审核', '1', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635425562626', '875745635379425280', '交易成功', '2', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635559780352', '875745635513643008', '未发布', '1', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635559780353', '875745635513643008', '已发布', '2', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635559780354', '875745635513643008', '询价中', '3', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635559780355', '875745635513643008', '询价结束', '4', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635559780356', '875745635513643008', '已提交竞价', '5', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135424', '875745635689803776', '竞价审核中', '1', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135425', '875745635689803776', '竞价审核已驳回', '2', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135426', '875745635689803776', '竞价审核成功', '3', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135427', '875745635689803776', '竞价中', '4', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135428', '875745635689803776', '竞价结束', '5', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135429', '875745635689803776', '交易完成审核', '6', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135430', '875745635689803776', '交易完成审核未通过', '7', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875745635740135431', '875745635689803776', '交易完成', '8', '2021-08-13 06:20:30', '2021-08-13 06:20:30', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190109683712', '875746190063546368', '燃料油类', '1', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190109683713', '875746190063546368', '化工类', '2', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190109683714', '875746190063546368', '苯类', '3', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190109683715', '875746190063546368', '其他', '4', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190260678656', '875746190214541312', '竞价竟量', '1', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190260678657', '875746190214541312', '零售', '2', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190260678658', '875746190214541312', '竞价', '3', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190260678659', '875746190214541312', '定价', '4', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190411673600', '875746190369730560', '未提交审核', '1', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190411673601', '875746190369730560', '审核中', '2', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190411673602', '875746190369730560', '审核通过', '3', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190411673603', '875746190369730560', '已驳回', '4', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190566862848', '875746190520725504', '销售计划审核', '0', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190566862849', '875746190520725504', '竞价审核', '1', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190566862850', '875746190520725504', '交易成功', '2', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190722052096', '875746190650748928', '未发布', '1', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190722052097', '875746190650748928', '已发布', '2', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190722052098', '875746190650748928', '询价中', '3', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190722052099', '875746190650748928', '询价结束', '4', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190722052100', '875746190650748928', '已提交竞价', '5', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190894018560', '875746190852075520', '竞价审核中', '1', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190894018561', '875746190852075520', '竞价审核已驳回', '2', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190894018562', '875746190852075520', '竞价审核成功', '3', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190898212864', '875746190852075520', '竞价中', '4', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190898212865', '875746190852075520', '竞价结束', '5', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190898212866', '875746190852075520', '交易完成审核', '6', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190898212867', '875746190852075520', '交易完成审核未通过', '7', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875746190898212868', '875746190852075520', '交易完成', '8', '2021-08-13 06:22:42', '2021-08-13 06:22:42', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624347504640', '875773624305561600', '燃料油类', '1', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624347504641', '875773624305561600', '化工类', '2', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624347504642', '875773624305561600', '苯类', '3', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624347504643', '875773624305561600', '其他', '4', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624506888192', '875773624464945152', '竞价竟量', '1', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624506888193', '875773624464945152', '零售', '2', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624506888194', '875773624464945152', '竞价', '3', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624506888195', '875773624464945152', '定价', '4', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624653688832', '875773624611745792', '未提交审核', '1', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624653688833', '875773624611745792', '审核中', '2', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624653688834', '875773624611745792', '审核通过', '3', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624653688835', '875773624611745792', '已驳回', '4', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624792100864', '875773624754352128', '销售计划审核', '0', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624792100865', '875773624754352128', '竞价审核', '1', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624792100866', '875773624754352128', '交易成功', '2', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624917929984', '875773624875986944', '未发布', '1', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624917929985', '875773624875986944', '已发布', '2', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624917929986', '875773624875986944', '询价中', '3', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624917929987', '875773624875986944', '询价结束', '4', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773624917929988', '875773624875986944', '已提交竞价', '5', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507840', '875773625035370496', '竞价审核中', '1', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507841', '875773625035370496', '竞价审核已驳回', '2', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507842', '875773625035370496', '竞价审核成功', '3', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507843', '875773625035370496', '竞价中', '4', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507844', '875773625035370496', '竞价结束', '5', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507845', '875773625035370496', '交易完成审核', '6', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507846', '875773625035370496', '交易完成审核未通过', '7', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('875773625081507847', '875773625035370496', '交易完成', '8', '2021-08-13 08:11:43', '2021-08-13 08:11:43', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673171202048', '876437673120870400', '燃料油类', '1', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673171202049', '876437673120870400', '化工类', '2', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673171202050', '876437673120870400', '苯类', '3', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673171202051', '876437673120870400', '其他', '4', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673351557120', '876437673305419776', '竞价竟量', '1', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673351557121', '876437673305419776', '零售', '2', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673351557122', '876437673305419776', '竞价', '3', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673351557123', '876437673305419776', '定价', '4', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673510940672', '876437673464803328', '未提交审核', '1', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673510940673', '876437673464803328', '审核中', '2', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673510940674', '876437673464803328', '审核通过', '3', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673510940675', '876437673464803328', '已驳回', '4', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673670324224', '876437673624186880', '销售计划审核', '0', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673670324225', '876437673624186880', '竞价审核', '1', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673670324226', '876437673624186880', '交易成功', '2', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673808736256', '876437673762598912', '未发布', '1', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673808736257', '876437673762598912', '已发布', '2', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673808736258', '876437673762598912', '询价中', '3', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673808736259', '876437673762598912', '询价结束', '4', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673808736260', '876437673762598912', '已提交竞价', '5', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479936', '876437673942953984', '竞价审核中', '1', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479937', '876437673942953984', '竞价审核已驳回', '2', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479938', '876437673942953984', '竞价审核成功', '3', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479939', '876437673942953984', '竞价中', '4', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479940', '876437673942953984', '竞价结束', '5', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479941', '876437673942953984', '交易完成审核', '6', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479942', '876437673942953984', '交易完成审核未通过', '7', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('876437673997479943', '876437673942953984', '交易完成', '8', '2021-08-15 04:10:24', '2021-08-15 04:10:24', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250586148864', '877608250540011520', '燃料油类', '1', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250586148865', '877608250540011520', '化工类', '2', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250586148866', '877608250540011520', '苯类', '3', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250586148867', '877608250540011520', '其他', '4', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250762309632', '877608250711977984', '竞价竟量', '1', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250762309633', '877608250711977984', '零售', '2', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250762309634', '877608250711977984', '竞价', '3', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250762309635', '877608250711977984', '定价', '4', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250917498880', '877608250871361536', '未提交审核', '1', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250917498881', '877608250871361536', '审核中', '2', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250917498882', '877608250871361536', '审核通过', '3', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608250917498883', '877608250871361536', '已驳回', '4', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251068493824', '877608251026550784', '销售计划审核', '0', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251068493825', '877608251026550784', '竞价审核', '1', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251068493826', '877608251026550784', '交易成功', '2', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251202711552', '877608251156574208', '未发布', '1', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251202711553', '877608251156574208', '已发布', '2', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251202711554', '877608251156574208', '询价中', '3', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251202711555', '877608251156574208', '询价结束', '4', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251202711556', '877608251156574208', '已提交竞价', '5', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872320', '877608251332734976', '竞价审核中', '1', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872321', '877608251332734976', '竞价审核已驳回', '2', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872322', '877608251332734976', '竞价审核成功', '3', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872323', '877608251332734976', '竞价中', '4', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872324', '877608251332734976', '竞价结束', '5', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872325', '877608251332734976', '交易完成审核', '6', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872326', '877608251332734976', '交易完成审核未通过', '7', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877608251378872327', '877608251332734976', '交易完成', '8', '2021-08-18 09:41:52', '2021-08-18 09:41:52', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918073724715008', '877918073603080192', '燃料油类', '1', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918073724715009', '877918073603080192', '化工类', '2', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918073724715010', '877918073603080192', '苯类', '3', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918073724715011', '877918073603080192', '其他', '4', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074269974528', '877918074077036544', '竞价竟量', '1', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074269974529', '877918074077036544', '零售', '2', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074269974530', '877918074077036544', '竞价', '3', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074269974531', '877918074077036544', '定价', '4', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074693599232', '877918074555187200', '未提交审核', '1', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074693599233', '877918074555187200', '审核中', '2', '2021-08-19 06:12:59', '2021-08-19 06:12:59', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074693599234', '877918074555187200', '审核通过', '3', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918074693599235', '877918074555187200', '已驳回', '4', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075272413184', '877918075134001152', '销售计划审核', '0', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075272413185', '877918075134001152', '竞价审核', '1', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075272413186', '877918075134001152', '交易成功', '2', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075712815105', '877918075549237248', '未发布', '1', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075712815106', '877918075549237248', '已发布', '2', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075712815107', '877918075549237248', '询价中', '3', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075712815108', '877918075549237248', '询价结束', '4', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918075712815109', '877918075549237248', '已提交竞价', '5', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515136', '877918076203548672', '竞价审核中', '1', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515137', '877918076203548672', '竞价审核已驳回', '2', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515138', '877918076203548672', '竞价审核成功', '3', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515139', '877918076203548672', '竞价中', '4', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515140', '877918076203548672', '竞价结束', '5', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515141', '877918076203548672', '交易完成审核', '6', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515142', '877918076203548672', '交易完成审核未通过', '7', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918076375515143', '877918076203548672', '交易完成', '8', '2021-08-19 06:13:00', '2021-08-19 06:13:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084101423104', '877918083958816768', '燃料油类', '1', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084101423105', '877918083958816768', '化工类', '2', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084101423106', '877918083958816768', '苯类', '3', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084101423107', '877918083958816768', '其他', '4', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084525047808', '877918084390830080', '竞价竟量', '1', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084525047809', '877918084390830080', '零售', '2', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084525047810', '877918084390830080', '竞价', '3', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084525047811', '877918084390830080', '定价', '4', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084957061120', '877918084843814912', '未提交审核', '1', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084957061121', '877918084843814912', '审核中', '2', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084957061122', '877918084843814912', '审核通过', '3', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918084957061123', '877918084843814912', '已驳回', '4', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085372297216', '877918085259051008', '销售计划审核', '0', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085372297217', '877918085259051008', '竞价审核', '1', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085372297218', '877918085259051008', '交易成功', '2', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085716230144', '877918085598789632', '未发布', '1', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085716230145', '877918085598789632', '已发布', '2', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085716230146', '877918085598789632', '询价中', '3', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085716230147', '877918085598789632', '询价结束', '4', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918085716230148', '877918085598789632', '已提交竞价', '5', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437760', '877918086039191552', '竞价审核中', '1', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437761', '877918086039191552', '竞价审核已驳回', '2', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437762', '877918086039191552', '竞价审核成功', '3', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437763', '877918086039191552', '竞价中', '4', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437764', '877918086039191552', '竞价结束', '5', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437765', '877918086039191552', '交易完成审核', '6', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437766', '877918086039191552', '交易完成审核未通过', '7', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('877918086152437767', '877918086039191552', '交易完成', '8', '2021-08-19 06:13:02', '2021-08-19 06:13:02', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052137917394944', '880052137871257600', '燃料油类', '1', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052137917394945', '880052137871257600', '化工类', '2', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052137917394946', '880052137871257600', '苯类', '3', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052137917394947', '880052137871257600', '其他', '4', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138085167104', '880052138039029760', '竞价竟量', '1', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138085167105', '880052138039029760', '零售', '2', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138085167106', '880052138039029760', '竞价', '3', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138085167107', '880052138039029760', '定价', '4', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138227773440', '880052138185830400', '未提交审核', '1', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138227773441', '880052138185830400', '审核中', '2', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138227773442', '880052138185830400', '审核通过', '3', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138227773443', '880052138185830400', '已驳回', '4', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138374574080', '880052138332631040', '销售计划审核', '0', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138374574081', '880052138332631040', '竞价审核', '1', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138374574082', '880052138332631040', '交易成功', '2', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138500403200', '880052138458460160', '未发布', '1', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138500403201', '880052138458460160', '已发布', '2', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138500403202', '880052138458460160', '询价中', '3', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138500403203', '880052138458460160', '询价结束', '4', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138500403204', '880052138458460160', '已提交竞价', '5', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175360', '880052138622038016', '竞价审核中', '1', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175361', '880052138622038016', '竞价审核已驳回', '2', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175362', '880052138622038016', '竞价审核成功', '3', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175363', '880052138622038016', '竞价中', '4', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175364', '880052138622038016', '竞价结束', '5', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175365', '880052138622038016', '交易完成审核', '6', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175366', '880052138622038016', '交易完成审核未通过', '7', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('880052138668175367', '880052138622038016', '交易完成', '8', '2021-08-25 03:33:00', '2021-08-25 03:33:00', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348344664064', '882223348298526720', '燃料油类', '1', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348344664065', '882223348298526720', '化工类', '2', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348344664066', '882223348298526720', '苯类', '3', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348344664067', '882223348298526720', '其他', '4', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348499853312', '882223348453715968', '竞价竟量', '1', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348499853313', '882223348453715968', '零售', '2', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348499853314', '882223348453715968', '竞价', '3', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348499853315', '882223348453715968', '定价', '4', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348655042560', '882223348613099520', '未提交审核', '1', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348655042561', '882223348613099520', '审核中', '2', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348655042562', '882223348613099520', '审核通过', '3', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348655042563', '882223348613099520', '已驳回', '4', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348810231808', '882223348768288768', '销售计划审核', '0', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348810231809', '882223348768288768', '竞价审核', '1', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348810231810', '882223348768288768', '交易成功', '2', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348944449536', '882223348902506496', '未发布', '1', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348944449537', '882223348902506496', '已发布', '2', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348944449538', '882223348902506496', '询价中', '3', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348944449539', '882223348902506496', '询价结束', '4', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223348944449540', '882223348902506496', '已提交竞价', '5', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610304', '882223349078667264', '竞价审核中', '1', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610305', '882223349078667264', '竞价审核已驳回', '2', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610306', '882223349078667264', '竞价审核成功', '3', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610307', '882223349078667264', '竞价中', '4', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610308', '882223349078667264', '竞价结束', '5', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610309', '882223349078667264', '交易完成审核', '6', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610310', '882223349078667264', '交易完成审核未通过', '7', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882223349120610311', '882223349078667264', '交易完成', '8', '2021-08-31 03:20:37', '2021-08-31 03:20:37', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('882396684718239744', '876437673464803328', '已提交竞价', '5', '2021-08-31 14:49:23', '2021-08-31 14:49:23', '876437670151303168', '876437670151303168', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883372348074160128', '882223348613099520', '已提交竞价', '5', '2021-09-03 07:26:19', '2021-09-03 07:26:19', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404556746944512', '865667829217738752', '已提交竞价', '5', '2021-09-03 09:34:18', '2021-09-03 09:34:18', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404559796203520', '875745635224236032', '已提交竞价', '5', '2021-09-03 09:34:19', '2021-09-03 09:34:19', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404561016745984', '875746190369730560', '已提交竞价', '5', '2021-09-03 09:34:19', '2021-09-03 09:34:19', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404562304397312', '875773624611745792', '已提交竞价', '5', '2021-09-03 09:34:19', '2021-09-03 09:34:19', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404563160035328', '876437673464803328', '已提交竞价', '5', '2021-09-03 09:34:20', '2021-09-03 09:34:20', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404564028256256', '877608250871361536', '已提交竞价', '5', '2021-09-03 09:34:20', '2021-09-03 09:34:20', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404564883894272', '877918074555187200', '已提交竞价', '5', '2021-09-03 09:34:20', '2021-09-03 09:34:20', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404565739532288', '877918084843814912', '已提交竞价', '5', '2021-09-03 09:34:20', '2021-09-03 09:34:20', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404566599364608', '880052138185830400', '已提交竞价', '5', '2021-09-03 09:34:20', '2021-09-03 09:34:20', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404614733197312', '865667829217738752', '已撤销审批', '6', '2021-09-03 09:34:32', '2021-09-03 09:34:32', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404616880680960', '875745635224236032', '已撤销审批', '6', '2021-09-03 09:34:32', '2021-09-03 09:34:32', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404618180915200', '875746190369730560', '已撤销审批', '6', '2021-09-03 09:34:33', '2021-09-03 09:34:33', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404619007193088', '875773624611745792', '已撤销审批', '6', '2021-09-03 09:34:33', '2021-09-03 09:34:33', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404619892191232', '876437673464803328', '已撤销审批', '6', '2021-09-03 09:34:33', '2021-09-03 09:34:33', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404620705886208', '877608250871361536', '已撤销审批', '6', '2021-09-03 09:34:33', '2021-09-03 09:34:33', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404622018703360', '877918074555187200', '已撤销审批', '6', '2021-09-03 09:34:34', '2021-09-03 09:34:34', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404622886924288', '877918084843814912', '已撤销审批', '6', '2021-09-03 09:34:34', '2021-09-03 09:34:34', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404624153604096', '880052138185830400', '已撤销审批', '6', '2021-09-03 09:34:34', '2021-09-03 09:34:34', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404625009242112', '882223348613099520', '已撤销审批', '6', '2021-09-03 09:34:34', '2021-09-03 09:34:34', '882225659687186432', '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404695737790464', '868137268395753472', '已撤销审批', '0', '2021-09-03 09:34:51', '2021-09-03 09:34:51', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404698032074752', '875745635689803776', '已撤销审批', '0', '2021-09-03 09:34:52', '2021-09-03 09:34:52', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404698887712768', '875746190852075520', '已撤销审批', '0', '2021-09-03 09:34:52', '2021-09-03 09:34:52', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404699747545088', '875773625035370496', '已撤销审批', '0', '2021-09-03 09:34:52', '2021-09-03 09:34:52', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404700598988800', '876437673942953984', '已撤销审批', '0', '2021-09-03 09:34:52', '2021-09-03 09:34:52', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404701463015424', '877608251332734976', '已撤销审批', '0', '2021-09-03 09:34:53', '2021-09-03 09:34:53', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404702750666752', '877918076203548672', '已撤销审批', '0', '2021-09-03 09:34:53', '2021-09-03 09:34:53', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404703597916160', '877918086039191552', '已撤销审批', '0', '2021-09-03 09:34:53', '2021-09-03 09:34:53', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404704470331392', '880052138622038016', '已撤销审批', '0', '2021-09-03 09:34:53', '2021-09-03 09:34:53', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404705149808640', '882223349078667264', '已撤销审批', '0', '2021-09-03 09:34:54', '2021-09-03 09:34:54', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404792571686912', '868137268395753472', '已撤销审批', '9', '2021-09-03 09:35:14', '2021-09-03 09:35:14', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404794660450304', '875745635689803776', '已撤销审批', '9', '2021-09-03 09:35:15', '2021-09-03 09:35:15', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404795511894016', '875746190852075520', '已撤销审批', '9', '2021-09-03 09:35:15', '2021-09-03 09:35:15', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404796317200384', '875773625035370496', '已撤销审批', '9', '2021-09-03 09:35:15', '2021-09-03 09:35:15', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404797630017536', '876437673942953984', '已撤销审批', '9', '2021-09-03 09:35:15', '2021-09-03 09:35:15', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404798515015680', '877608251332734976', '已撤销审批', '9', '2021-09-03 09:35:16', '2021-09-03 09:35:16', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404799374848000', '877918076203548672', '已撤销审批', '9', '2021-09-03 09:35:16', '2021-09-03 09:35:16', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404800666693632', '877918086039191552', '已撤销审批', '9', '2021-09-03 09:35:16', '2021-09-03 09:35:16', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883404801526525952', '880052138622038016', '已撤销审批', '9', '2021-09-03 09:35:16', '2021-09-03 09:35:16', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883462860944764928', '882223349078667264', '已撤销审批', '9', '2021-09-03 13:25:59', '2021-09-03 13:25:59', '882225659687186432', '882225659687186432', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713291198464', '883708713240866816', '燃料油类', '1', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713291198465', '883708713240866816', '化工类', '2', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713291198466', '883708713240866816', '苯类', '3', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713291198467', '883708713240866816', '其他', '4', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713458970624', '883708713417027584', '竞价竟量', '1', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713458970625', '883708713417027584', '零售', '2', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713458970626', '883708713417027584', '竞价', '3', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713458970627', '883708713417027584', '定价', '4', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713614159872', '883708713568022528', '未提交审核', '1', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713614159873', '883708713568022528', '审核中', '2', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713614159874', '883708713568022528', '审核通过', '3', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713614159875', '883708713568022528', '已驳回', '4', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713614159876', '883708713568022528', '已提交竞价', '5', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713614159877', '883708713568022528', '已撤销审批', '6', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, '877608248178618368', 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713807097856', '883708713765154816', '销售计划审核', '0', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713807097857', '883708713765154816', '竞价审核', '1', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713807097858', '883708713765154816', '交易成功', '2', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713941315584', '883708713895178240', '未发布', '1', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713941315585', '883708713895178240', '已发布', '2', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713941315586', '883708713895178240', '询价中', '3', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713941315587', '883708713895178240', '询价结束', '4', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708713941315588', '883708713895178240', '已提交竞价', '5', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476352', '883708714071339008', '竞价审核中', '1', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476353', '883708714071339008', '竞价审核已驳回', '2', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476354', '883708714071339008', '竞价审核成功', '3', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476355', '883708714071339008', '竞价中', '4', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476356', '883708714071339008', '竞价结束', '5', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476357', '883708714071339008', '交易完成审核', '6', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476358', '883708714071339008', '交易完成审核未通过', '7', '2021-09-04 05:42:55', '2021-09-04 05:42:55', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476359', '883708714071339008', '交易完成', '8', '2021-09-04 05:42:56', '2021-09-04 05:42:56', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476360', '883708714071339008', '已撤销审批', '0', '2021-09-04 05:42:56', '2021-09-04 05:42:56', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('883708714117476361', '883708714071339008', '已撤销审批', '9', '2021-09-04 05:42:56', '2021-09-04 05:42:56', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876434132992', '884450876392189952', '燃料油类', '1', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876434132993', '884450876392189952', '化工类', '2', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876434132994', '884450876392189952', '苯类', '3', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876434132995', '884450876392189952', '其他', '4', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876597710848', '884450876555767808', '竞价竟量', '1', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876597710849', '884450876555767808', '零售', '2', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876597710850', '884450876555767808', '竞价', '3', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876597710851', '884450876555767808', '定价', '4', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876740317184', '884450876698374144', '未提交审核', '1', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876740317185', '884450876698374144', '审核中', '2', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876740317186', '884450876698374144', '审核通过', '3', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876740317187', '884450876698374144', '已驳回', '4', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876740317188', '884450876698374144', '已提交竞价', '5', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876740317189', '884450876698374144', '已撤销审批', '6', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876933255168', '884450876887117824', '销售计划审核', '0', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876933255169', '884450876887117824', '竞价审核', '1', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450876933255170', '884450876887117824', '交易成功', '2', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877054889984', '884450877017141248', '未发布', '1', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877054889985', '884450877017141248', '已发布', '2', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877054889986', '884450877017141248', '询价中', '3', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877054889987', '884450877017141248', '询价结束', '4', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877054889988', '884450877017141248', '已提交竞价', '5', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856448', '884450877180719104', '竞价审核中', '1', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856449', '884450877180719104', '竞价审核已驳回', '2', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856450', '884450877180719104', '竞价审核成功', '3', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856451', '884450877180719104', '竞价中', '4', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856452', '884450877180719104', '竞价结束', '5', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856453', '884450877180719104', '交易完成审核', '6', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856454', '884450877180719104', '交易完成审核未通过', '7', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856455', '884450877180719104', '交易完成', '8', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856456', '884450877180719104', '已撤销审批', '0', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884450877226856457', '884450877180719104', '已撤销审批', '9', '2021-09-06 06:52:01', '2021-09-06 06:52:01', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451331897798656', '884451331860049920', '燃料油类', '1', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451331897798657', '884451331860049920', '化工类', '2', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451331897798658', '884451331860049920', '苯类', '3', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451331897798659', '884451331860049920', '其他', '4', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332044599296', '884451332002656256', '竞价竟量', '1', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332044599297', '884451332002656256', '零售', '2', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332044599298', '884451332002656256', '竞价', '3', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332044599299', '884451332002656256', '定价', '4', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332191399936', '884451332149456896', '未提交审核', '1', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332191399937', '884451332149456896', '审核中', '2', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332191399938', '884451332149456896', '审核通过', '3', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332191399939', '884451332149456896', '已驳回', '4', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332191399940', '884451332149456896', '已提交竞价', '5', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332191399941', '884451332149456896', '已撤销审批', '6', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332375949312', '884451332334006272', '销售计划审核', '0', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332375949313', '884451332334006272', '竞价审核', '1', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332375949314', '884451332334006272', '交易成功', '2', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332497584128', '884451332459835392', '未发布', '1', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332497584129', '884451332459835392', '已发布', '2', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332497584130', '884451332459835392', '询价中', '3', '2021-09-06 06:53:49', '2021-09-06 06:53:49', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332497584131', '884451332459835392', '询价结束', '4', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332497584132', '884451332459835392', '已提交竞价', '5', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161984', '884451332619218944', '竞价审核中', '1', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161985', '884451332619218944', '竞价审核已驳回', '2', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161986', '884451332619218944', '竞价审核成功', '3', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161987', '884451332619218944', '竞价中', '4', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161988', '884451332619218944', '竞价结束', '5', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161989', '884451332619218944', '交易完成审核', '6', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161990', '884451332619218944', '交易完成审核未通过', '7', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161991', '884451332619218944', '交易完成', '8', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161992', '884451332619218944', '已撤销审批', '0', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('884451332661161993', '884451332619218944', '已撤销审批', '9', '2021-09-06 06:53:50', '2021-09-06 06:53:50', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193628405760', '890203193578074112', '燃料油类', '1', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193628405761', '890203193578074112', '化工类', '2', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193628405762', '890203193578074112', '苯类', '3', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193628405763', '890203193578074112', '其他', '4', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193804566528', '890203193758429184', '竞价竟量', '1', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193804566529', '890203193758429184', '零售', '2', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193804566530', '890203193758429184', '竞价', '3', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193804566531', '890203193758429184', '定价', '4', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193959755776', '890203193913618432', '未提交审核', '1', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193959755777', '890203193913618432', '审核中', '2', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193959755778', '890203193913618432', '审核通过', '3', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193959755779', '890203193913618432', '已驳回', '4', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193959755780', '890203193913618432', '已提交竞价', '5', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203193959755781', '890203193913618432', '已撤销审批', '6', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194161082368', '890203194114945024', '销售计划审核', '0', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194161082369', '890203194114945024', '竞价审核', '1', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194161082370', '890203194114945024', '交易成功', '2', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194291105792', '890203194249162752', '未发布', '1', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194291105793', '890203194249162752', '已发布', '2', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194291105794', '890203194249162752', '询价中', '3', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194291105795', '890203194249162752', '询价结束', '4', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194291105796', '890203194249162752', '已提交竞价', '5', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460864', '890203194425323520', '竞价审核中', '1', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460865', '890203194425323520', '竞价审核已驳回', '2', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460866', '890203194425323520', '竞价审核成功', '3', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460867', '890203194425323520', '竞价中', '4', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460868', '890203194425323520', '竞价结束', '5', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460869', '890203194425323520', '交易完成审核', '6', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460870', '890203194425323520', '交易完成审核未通过', '7', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460871', '890203194425323520', '交易完成', '8', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460872', '890203194425323520', '已撤销审批', '0', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('890203194471460873', '890203194425323520', '已撤销审批', '9', '2021-09-22 03:49:40', '2021-09-22 03:49:40', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012077670400', '896449012027338752', '燃料油类', '1', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012077670401', '896449012027338752', '化工类', '2', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012077670402', '896449012027338752', '苯类', '3', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012077670403', '896449012027338752', '其他', '4', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012245442560', '896449012199305216', '竞价竟量', '1', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012245442561', '896449012199305216', '零售', '2', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012245442562', '896449012199305216', '竞价', '3', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012245442563', '896449012199305216', '定价', '4', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012396437504', '896449012354494464', '未提交审核', '1', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012396437505', '896449012354494464', '审核中', '2', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012396437506', '896449012354494464', '审核通过', '3', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012396437507', '896449012354494464', '已驳回', '4', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012396437508', '896449012354494464', '已提交竞价', '5', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012396437509', '896449012354494464', '已撤销审批', '6', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012589375488', '896449012543238144', '销售计划审核', '0', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012589375489', '896449012543238144', '竞价审核', '1', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012589375490', '896449012543238144', '交易成功', '2', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012719398912', '896449012673261568', '未发布', '1', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012719398913', '896449012673261568', '已发布', '2', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012719398914', '896449012673261568', '询价中', '3', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012719398915', '896449012673261568', '询价结束', '4', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012719398916', '896449012673261568', '已提交竞价', '5', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559680', '896449012845228032', '竞价审核中', '1', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559681', '896449012845228032', '竞价审核已驳回', '2', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559682', '896449012845228032', '竞价审核成功', '3', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559683', '896449012845228032', '竞价中', '4', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559684', '896449012845228032', '竞价结束', '5', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559685', '896449012845228032', '交易完成审核', '6', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559686', '896449012845228032', '交易完成审核未通过', '7', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559687', '896449012845228032', '交易完成', '8', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559688', '896449012845228032', '已撤销审批', '0', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('896449012895559689', '896449012845228032', '已撤销审批', '9', '2021-10-09 09:28:19', '2021-10-09 09:28:19', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673567584256', '897056673525641216', '燃料油类', '1', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673567584257', '897056673525641216', '化工类', '2', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673567584258', '897056673525641216', '苯类', '3', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673567584259', '897056673525641216', '其他', '4', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673714384896', '897056673672441856', '竞价竟量', '1', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673714384897', '897056673672441856', '零售', '2', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673714384898', '897056673672441856', '竞价', '3', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673714384899', '897056673672441856', '定价', '4', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673856991232', '897056673815048192', '未提交审核', '1', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673856991233', '897056673815048192', '审核中', '2', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673856991234', '897056673815048192', '审核通过', '3', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673856991235', '897056673815048192', '已驳回', '4', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673856991236', '897056673815048192', '已提交竞价', '5', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056673856991237', '897056673815048192', '已撤销审批', '6', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674041540608', '897056673999597568', '销售计划审核', '0', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674041540609', '897056673999597568', '竞价审核', '1', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674041540610', '897056673999597568', '交易成功', '2', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674167369728', '897056674125426688', '未发布', '1', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674167369729', '897056674125426688', '已发布', '2', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674167369730', '897056674125426688', '询价中', '3', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674167369731', '897056674125426688', '询价结束', '4', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674167369732', '897056674125426688', '已提交竞价', '5', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947584', '897056674289004544', '竞价审核中', '1', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947585', '897056674289004544', '竞价审核已驳回', '2', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947586', '897056674289004544', '竞价审核成功', '3', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947587', '897056674289004544', '竞价中', '4', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947588', '897056674289004544', '竞价结束', '5', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947589', '897056674289004544', '交易完成审核', '6', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947590', '897056674289004544', '交易完成审核未通过', '7', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947591', '897056674289004544', '交易完成', '8', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947592', '897056674289004544', '已撤销审批', '0', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897056674330947593', '897056674289004544', '已撤销审批', '9', '2021-10-11 01:42:57', '2021-10-11 01:42:57', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981196382208', '897057981154439168', '燃料油类', '1', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981196382209', '897057981154439168', '化工类', '2', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981196382210', '897057981154439168', '苯类', '3', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981196382211', '897057981154439168', '其他', '4', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981338988544', '897057981297045504', '竞价竟量', '1', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981338988545', '897057981297045504', '零售', '2', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981338988546', '897057981297045504', '竞价', '3', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981338988547', '897057981297045504', '定价', '4', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981481594880', '897057981443846144', '未提交审核', '1', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981481594881', '897057981443846144', '审核中', '2', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981481594882', '897057981443846144', '审核通过', '3', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981481594883', '897057981443846144', '已驳回', '4', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981481594884', '897057981443846144', '已提交竞价', '5', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981481594885', '897057981443846144', '已撤销审批', '6', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981666144256', '897057981624201216', '销售计划审核', '0', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981666144257', '897057981624201216', '竞价审核', '1', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981666144258', '897057981624201216', '交易成功', '2', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981787779072', '897057981745836032', '未发布', '1', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981787779073', '897057981745836032', '已发布', '2', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981787779074', '897057981745836032', '询价中', '3', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981787779075', '897057981745836032', '询价结束', '4', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981787779076', '897057981745836032', '已提交竞价', '5', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356928', '897057981909413888', '竞价审核中', '1', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356929', '897057981909413888', '竞价审核已驳回', '2', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356930', '897057981909413888', '竞价审核成功', '3', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356931', '897057981909413888', '竞价中', '4', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356932', '897057981909413888', '竞价结束', '5', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356933', '897057981909413888', '交易完成审核', '6', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356934', '897057981909413888', '交易完成审核未通过', '7', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356935', '897057981909413888', '交易完成', '8', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356936', '897057981909413888', '已撤销审批', '0', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('897057981951356937', '897057981909413888', '已撤销审批', '9', '2021-10-11 01:48:09', '2021-10-11 01:48:09', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546683953152', '901113546637815808', '燃料油类', '1', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546683953153', '901113546637815808', '化工类', '2', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546683953154', '901113546637815808', '苯类', '3', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546683953155', '901113546637815808', '其他', '4', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546843336704', '901113546801393664', '竞价竟量', '1', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546843336705', '901113546801393664', '零售', '2', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546843336706', '901113546801393664', '竞价', '3', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546843336707', '901113546801393664', '定价', '4', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546990137344', '901113546948194304', '未提交审核', '1', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546990137345', '901113546948194304', '审核中', '2', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546990137346', '901113546948194304', '审核通过', '3', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546990137347', '901113546948194304', '已驳回', '4', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546990137348', '901113546948194304', '已提交竞价', '5', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113546990137349', '901113546948194304', '已撤销审批', '6', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547233406976', '901113547132743680', '销售计划审核', '0', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547233406977', '901113547132743680', '竞价审核', '1', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547233406978', '901113547132743680', '交易成功', '2', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547359236096', '901113547317293056', '未发布', '1', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547359236097', '901113547317293056', '已发布', '2', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547359236098', '901113547317293056', '询价中', '3', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547359236099', '901113547317293056', '询价结束', '4', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547359236100', '901113547317293056', '已提交竞价', '5', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813952', '901113547476676608', '竞价审核中', '1', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813953', '901113547476676608', '竞价审核已驳回', '2', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813954', '901113547476676608', '竞价审核成功', '3', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813955', '901113547476676608', '竞价中', '4', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813956', '901113547476676608', '竞价结束', '5', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813957', '901113547476676608', '交易完成审核', '6', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813958', '901113547476676608', '交易完成审核未通过', '7', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813959', '901113547476676608', '交易完成', '8', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813960', '901113547476676608', '已撤销审批', '0', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);
INSERT INTO sys_dict_value (dict_value_id, dict_id, dict_name, dict_value, create_time, update_time, create_by, update_by, delete_flag) VALUES ('901113547522813961', '901113547476676608', '已撤销审批', '9', '2021-10-22 06:23:31', '2021-10-22 06:23:31', null, null, 0);



INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873344, '首页', '*', 145, '/index', 'index', 1, 0, 'C', '0', '0', 'dashboard', '*', NULL, '864906344300396544', '2021-07-28 04:14:17', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873346, '系统管理', '*', 100, '/system', NULL, 1, 0, 'M', '0', '0', 'system', '*', NULL, '864906344300396544', '2021-07-28 03:48:44', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873347, '用户管理', '847779126050873346', 0, '/user', 'system/user/index', 1, 0, 'C', '0', '0', 'user', '*', NULL, '', '2021-05-31 10:38:46', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873348, '角色管理', '847779126050873346', 1, '/role', 'system/role/index', 1, 0, 'C', '0', '0', 'peoples', '*', NULL, '', '2021-05-31 10:39:02', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873349, '菜单管理', '847779126050873346', 2, '/menu', 'system/menu/index', 1, 0, 'C', '0', '0', 'tree-table', '*', NULL, '', '2021-05-31 10:39:43', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873350, '部门管理', '847779126050873346', 3, '/dept', 'system/dept/index', 1, 0, 'C', '0', '0', 'tree', '*', NULL, '', '2021-05-31 10:39:57', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873351, '岗位管理', '847779126050873346', 4, '/post', 'system/post/index', 1, 0, 'C', '0', '0', 'post', '*', NULL, '', '2021-06-01 11:30:13', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873352, '字典管理', '847779126050873346', 5, '/dict', 'system/dict/index', 1, 0, 'C', '0', '0', 'dict', '*', NULL, '', '2021-05-31 10:40:31', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (847779126050873353, '权限管理', '847779126050873346', 6, '/config', 'system/config/index', 1, 0, 'C', '0', '0', 'edit', '*', NULL, '', '2021-05-31 10:40:37', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (864542426914734080, '基础数据管理', '*', 90, '/company', NULL, 1, 0, 'M', '0', '0', 'table', '*', '2021-07-13 08:22:56', '864906344300396544', '2021-07-28 03:49:00', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (864543344179658752, '商品信息管理', '864542426914734080', 10, '/company/product', 'taizhou/company/product', 0, 0, 'C', '0', '0', 'example', '*', '2021-07-13 08:26:35', '*', '2021-07-13 08:26:35', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (864544248630984704, '销售计划管理', '*', 50, '/sell', NULL, 1, 0, 'M', '0', '0', 'chart', '*', '2021-07-13 08:30:10', '864906344300396544', '2021-07-28 03:49:08', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (864544614911164416, '销售计划', '864544248630984704', 100, '/sell/plan', 'taizhou/sell/sellplanning/index', 1, 0, 'C', '0', '0', 'chart', '*', '2021-07-13 08:31:38', '864906344300396544', '2021-07-28 03:49:28', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (864545473715879936, '竞价管理', '864544248630984704', 70, '/sell/bidding', 'taizhou/sell/bidding/index', 1, 0, 'C', '0', '0', 'druid', '*', '2021-07-13 08:35:02', '864906344300396544', '2021-07-13 08:35:02', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (865226378633658368, '合作伙伴', '*', 11, '/people', NULL, 1, 0, 'M', '0', '0', 'peoples', '864906344300396544', '2021-07-15 05:40:44', '864906344300396544', '2021-07-15 05:40:44', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (865230124788858880, '企业管理', '865226378633658368', 35, '/people/message', 'taizhou/people/message', 1, 0, 'C', '0', '0', 'guide', '864906344300396544', '2021-07-15 05:55:37', '864906344300396544', '2021-07-15 05:55:37', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (865230852798398464, '企业分组', '865226378633658368', 20, '/people/group', 'taizhou/people/group', 1, 0, 'C', '0', '0', 'table', '864906344300396544', '2021-07-15 05:58:30', '864906344300396544', '2021-07-15 05:58:30', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (865672215146905600, '询价管理', '864544248630984704', 80, '/sell/enquiry', 'taizhou/sell/enquiry/index', 1, 0, 'C', '0', '0', 'example', '864906344300396544', '2021-07-16 11:12:19', '864906344300396544', '2021-07-28 03:50:25', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (867098387193905152, '审批流程', '864542426914734080', 20, '/examine', 'taizhou/examine/index', 1, 0, 'C', '0', '0', 'tree-table', '864906344300396544', '2021-07-20 09:39:27', '864906344300396544', '2021-07-20 09:39:27', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (874665607099035648, '数据权限', '847779126050873346', 7, '/data/index', 'system/data/index', 1, 0, 'C', '0', '0', 'tab', '864906344300396544', '2021-08-10 06:48:51', '864906344300396544', '2021-08-10 06:48:51', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875125081565675520, '新增', '874665607099035648', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:14:38', '864906344300396544', '2021-08-11 13:14:38', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875125427704807424, '修改', '874665607099035648', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:16:01', '864906344300396544', '2021-08-11 13:16:01', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126341941444608, '新增', '847779126050873353', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:19:39', '864906344300396544', '2021-08-11 13:19:39', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126504609136640, '修改', '847779126050873353', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:20:17', '864906344300396544', '2021-08-11 13:20:17', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126608426549248, '删除', '847779126050873353', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:20:42', '864906344300396544', '2021-08-11 13:20:42', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126719055511552, '新增', '847779126050873352', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:21:08', '864906344300396544', '2021-08-11 13:21:08', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126751204851712, '修改', '847779126050873352', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:21:16', '864906344300396544', '2021-08-11 13:21:16', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126817407746048, '新增', '847779126050873351', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:21:32', '864906344300396544', '2021-08-11 13:21:32', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126849443840000, '修改', '847779126050873351', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:21:40', '864906344300396544', '2021-08-11 13:21:40', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126886760562688, '删除', '847779126050873351', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:21:48', '864906344300396544', '2021-08-11 13:21:48', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126939579432960, '新增', '847779126050873350', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:22:01', '864906344300396544', '2021-08-11 13:22:01', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875126979131719680, '修改', '847779126050873350', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:22:10', '864906344300396544', '2021-08-11 13:22:10', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127015097876480, '删除', '847779126050873350', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:22:19', '864906344300396544', '2021-08-11 13:22:19', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127092554088448, '新增', '847779126050873348', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:22:37', '864906344300396544', '2021-08-11 13:22:37', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127365339037696, '修改', '847779126050873348', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:23:43', '864906344300396544', '2021-08-11 13:23:43', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127402051780608, '删除', '847779126050873348', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:23:51', '864906344300396544', '2021-08-11 13:23:51', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127462693027840, '新增', '847779126050873347', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:24:06', '864906344300396544', '2021-08-11 13:24:06', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127492480974848, '修改', '847779126050873347', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:24:13', '864906344300396544', '2021-08-11 13:24:13', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127523288137728, '删除', '847779126050873347', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:24:20', '864906344300396544', '2021-08-11 13:24:20', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127566447525888, '新增', '847779126050873349', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:24:30', '864906344300396544', '2021-08-11 13:24:30', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127619014737920, '修改', '847779126050873349', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:24:43', '864906344300396544', '2021-08-11 13:24:43', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127659850481664, '删除', '847779126050873349', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:24:53', '864906344300396544', '2021-08-11 13:24:53', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127794810601472, '新增', '864543344179658752', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:25:25', '864906344300396544', '2021-08-11 13:25:25', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127823864545280, '修改', '864543344179658752', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:25:32', '864906344300396544', '2021-08-11 13:25:32', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127861244182528, '删除', '864543344179658752', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:25:41', '864906344300396544', '2021-08-11 13:25:41', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127945897820160, '导入', '864543344179658752', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:26:01', '864906344300396544', '2021-08-11 13:26:01', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875127997978492928, '新增', '867098387193905152', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:26:13', '864906344300396544', '2021-08-11 13:26:13', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128032740884480, '修改', '867098387193905152', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:26:22', '864906344300396544', '2021-08-11 13:26:22', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128305106403328, '新增', '865230852798398464', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:27:27', '864906344300396544', '2021-08-11 13:27:27', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128343425564672, '修改', '865230852798398464', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:27:36', '864906344300396544', '2021-08-11 13:27:36', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128376006918144, '删除', '865230852798398464', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:27:43', '864906344300396544', '2021-08-11 13:27:43', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128423956201472, '公司维护', '865230852798398464', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:27:55', '864906344300396544', '2021-08-11 13:27:55', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128457007316992, '新增', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:28:03', '864906344300396544', '2021-08-11 13:28:03', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128483741810688, '修改', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:28:09', '864906344300396544', '2021-08-11 13:28:09', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128510354669568, '删除', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:28:16', '864906344300396544', '2021-08-11 13:28:16', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128548837408768, '认证', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:28:25', '864906344300396544', '2021-08-11 13:28:25', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128594475630592, '禁用', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:28:36', '864906344300396544', '2021-08-11 13:28:36', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128649370681344, '查询', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:28:49', '864906344300396544', '2021-08-11 13:28:49', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128675949985792, '导入', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:28:55', '864906344300396544', '2021-08-11 13:28:55', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128770544123904, '新增', '864544614911164416', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:29:18', '864906344300396544', '2021-08-11 13:29:18', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128801649082368, '修改', '864544614911164416', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:29:25', '864906344300396544', '2021-08-11 13:29:25', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128838013698048, '删除', '864544614911164416', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:29:34', '864906344300396544', '2021-08-11 13:29:34', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128916870807552, '审核提交', '864544614911164416', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:29:52', '864906344300396544', '2021-08-11 13:29:52', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875128964820090880, '查看审批进度', '864544614911164416', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:30:04', '864906344300396544', '2021-08-11 13:30:04', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129040678273024, '审批历史', '864544614911164416', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:30:22', '864906344300396544', '2021-08-11 13:30:22', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129113126486016, '查询', '865672215146905600', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:30:39', '864906344300396544', '2021-08-11 13:30:39', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129164607373312, '发布询价', '865672215146905600', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:30:51', '864906344300396544', '2021-08-11 13:30:51', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129226905370624, '开始竞价', '865672215146905600', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:31:06', '864906344300396544', '2021-08-11 13:31:06', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129275332804608, '提前开始询价', '865672215146905600', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:31:18', '864906344300396544', '2021-08-11 13:31:18', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129337941180416, '提前结束询价', '865672215146905600', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:31:33', '864906344300396544', '2021-08-11 13:31:33', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129468673441792, '查询', '864545473715879936', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:32:04', '864906344300396544', '2021-08-11 13:32:04', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129604166238208, '查看审批进度', '864545473715879936', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:32:36', '864906344300396544', '2021-08-11 13:32:36', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129667747692544, '查看交易', '864545473715879936', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:32:51', '864906344300396544', '2021-08-11 13:32:51', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129706440146944, '再次审核', '864545473715879936', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:33:01', '864906344300396544', '2021-08-11 13:33:01', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129770931765248, '提前开始竞价', '864545473715879936', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:33:16', '864906344300396544', '2021-08-11 13:33:16', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129811801063424, '提前结束竞价', '864545473715879936', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:33:26', '864906344300396544', '2021-08-11 13:33:26', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875129856323600384, '审批历史', '864545473715879936', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:33:36', '864906344300396544', '2021-08-11 13:33:36', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875130043695742976, '启用', '865230124788858880', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 13:34:21', '864906344300396544', '2021-08-11 13:34:21', '', '*');
INSERT INTO `tz_bidding`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `company_id`) VALUES (875148599653154816, '查看', '864544614911164416', 0, '', NULL, 1, 0, 'F', '0', '0', '#', '864906344300396544', '2021-08-11 14:48:05', '864906344300396544', '2021-08-11 14:48:05', '', '*');
INSERT INTO `tz_bidding`.`sys_identity`(`identity_id`, `identity_name`, `identity_code`) VALUES ('1', '系统超级管理员', 0);
INSERT INTO `tz_bidding`.`sys_identity`(`identity_id`, `identity_name`, `identity_code`) VALUES ('2', '系统管理员', 1);
INSERT INTO `tz_bidding`.`sys_identity`(`identity_id`, `identity_name`, `identity_code`) VALUES ('3', '部门管理员', 2);
INSERT INTO `tz_bidding`.`sys_identity`(`identity_id`, `identity_name`, `identity_code`) VALUES ('4', '普通用户', 3);
INSERT INTO `tz_bidding`.`sys_identity`(`identity_id`, `identity_name`, `identity_code`) VALUES ('5', '管理端人员(用户维护系统公司)', 4);
INSERT INTO `tz_bidding`.`sys_user`(`user_id`, `real_name`, `user_name`, `company_id`, `dept_id`, `identity_code`, `email`, `position_id`, `phone`, `data_id`, `password`, `role_id`, `sex`, `head_img`, `finally_login_time`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `delete_flag`) VALUES (847779126050873346, '管理员', 'admin', '*', '*', 4, NULL, NULL, NULL, 0, '21232f297a57a5a743894a0e4a801fc3', NULL, NULL, NULL, NULL, 0, '2021-08-13 06:14:27', '2021-08-13 06:14:27', NULL, NULL, 0);
