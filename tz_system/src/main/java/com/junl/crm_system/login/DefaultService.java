package com.junl.crm_system.login;

import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_system.admin.vo.SysLoginVo;
import com.junl.crm_common.status.IdentityStatus;
import lombok.AllArgsConstructor;


/**
 * 系统总管理员登录校验
 */
@AllArgsConstructor
public class DefaultService implements LoginService {

    private SysLoginVo sysLoginVo;
    @Override
    public SysUserEntity register() {
        String userName = sysLoginVo.getUserName();
        String password = sysLoginVo.getPassword();
        Assert.notNull(userName,"用户名不能为null");
        Assert.notNull(password,"密码不能为null");
        if(!userName.equals("junl123")){
            throw new RuntimeException("用户名错误");
        }

        if(!password.equals("junl123")){
            throw new RuntimeException("密码错误");
        }

        SysUserEntity sysUserEntity=new SysUserEntity();
        sysUserEntity.setUserId(IdentityStatus.GOD.getName());
        sysUserEntity.setDeptId(IdentityStatus.GOD.getName());
        sysUserEntity.setCompanyId(IdentityStatus.GOD.getName());
        return sysUserEntity;
    }
}
