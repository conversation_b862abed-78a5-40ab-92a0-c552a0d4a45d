package com.junl.crm_system.login;

import com.junl.crm_common.pojo.admin.SysUserEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @deprecated  登录处理
 */

@Component
@Log4j2
public class LoginServiceHandler {
    private LoginService loginService;

    /**
     * 更换处理逻辑类
     * @param loginService
     */
    public void setLoginService(LoginService loginService){
        this.loginService=loginService;
    }

    /**
     * 执行处理逻辑
     * @return
     */
    public SysUserEntity register(){
        if(loginService==null){
            log.error("校验对象缺失");
            throw new RuntimeException("无注册对象，请先赋值");
        }
        return loginService.register();
    }
}
