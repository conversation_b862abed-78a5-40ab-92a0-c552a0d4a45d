package com.junl.crm_system.login;

import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysLoginVo;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AppletService implements LoginService{

    private RedisUtils redisUtils;
    private SysLoginVo sysLoginVo;
    private SysUserService sysUserService;

    @Override
    public SysUserEntity register() {
        String code = sysLoginVo.getCode();
        Assert.notNull(code,"验证码不能为空.");

        String phone = sysLoginVo.getPhone();
        Assert.notNull(phone,"手机号不能为空.");

        String tempCode = redisUtils.get(RedisKey.APPLET_PHONE.getName() + phone);
        Assert.notNull(tempCode,"验证码已过期.");


        if (!code.equals(tempCode)) {
            throw new RuntimeException("验证码不正确.");
        }

        SysUserEntity userEntity = sysUserService.getUser(phone);

        Assert.notNull(userEntity,"手机号错误.");

        String status = userEntity.getStatus();
        if(status.equals(Opposite.SINGLE)){
            throw new RuntimeException("该账号已冻结.");
        }

        userEntity.setSoleId(sysUserService.getSoleId(userEntity.getUserId()).getSoleId());

        return userEntity;
    }
}
