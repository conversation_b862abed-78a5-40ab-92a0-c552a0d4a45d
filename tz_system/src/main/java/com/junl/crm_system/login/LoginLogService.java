package com.junl.crm_system.login;

import com.junl.crm_common.pojo.admin.SysLogEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysLogService;

/**
 * 登录无法切面获取日志信息 需要手动注入日志信息
 * <AUTHOR>
 */
public class LoginLogService {

    /**
     * 新增登录日志
     * @param sysUserEntity
     * @param sysLogService
     */
    public static void addLog(SysUserEntity sysUserEntity, SysLogService sysLogService){
        SysLogEntity sysLogEntity=new SysLogEntity();
        sysLogEntity.setCompanyId(sysUserEntity.getCompanyId());
        sysLogEntity.setCreateBy(sysUserEntity.getUserId());
        sysLogEntity.setLogDescribe("登录");
        sysLogEntity.setLogId(SnowFlake.getUUId());
        sysLogEntity.setLogType(LogType.LOGIN.getType());
        sysLogService.save(sysLogEntity);
    }

}
