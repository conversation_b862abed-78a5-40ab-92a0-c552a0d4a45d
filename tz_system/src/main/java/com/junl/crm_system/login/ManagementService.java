package com.junl.crm_system.login;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CipherUtils;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysLoginVo;
import com.junl.crm_system.admin.vo.SysUserVo;
import lombok.AllArgsConstructor;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/149:55
 */
@AllArgsConstructor
public class ManagementService implements LoginService{

    //private RedisUtils redisUtils;
    private SysLoginVo sysLoginVo;
    private SysUserService sysUserService;



    @Override
    public SysUserEntity register() {
//        String s = redisUtils.get(RedisKey.CODE.getName()+sysLoginVo.getUserName());
//        Assert.notNull(s,"验证码已过期");
        String userName = sysLoginVo.getUserName();
        String password = sysLoginVo.getPassword();
//        String code = sysLoginVo.getCode();

        Assert.notNull(userName,"用户名不能为空");
        Assert.notNull(password,"密码不能为null");
        // Assert.notNull(code,"验证码不能为null");

//        if (!s.equalsIgnoreCase(code)) {
//            throw new NullPointerException("验证码不正确，请重新输入");
//        }

        SysUserEntity sysUserEntity = sysUserService.getOne(new QueryWrapper<SysUserEntity>().lambda().and(e->e.eq(SysUserEntity::getIdentityCode, IdentityStatus.MANAGEMENT.getCode())
                .eq(SysUserEntity::getUserName, userName)
                .eq(SysUserEntity::getCompanyId,IdentityStatus.GOD.getName())));

        Assert.notNull(sysUserEntity,"该账户不存在");

        if (!CipherUtils.enCoderMd5(password).equals(sysUserEntity.getPassword())) {
            throw new IllegalArgumentException("密码不正确,请重新输入密码");
        }

        if (sysUserEntity.getDeleteFlag().equals(Opposite.SINGLE)) {
            throw new RuntimeException("账户异常，请联系公司负责人");
        }

        if(sysUserEntity.getStatus().equals(Opposite.SINGLE)){
            throw new RuntimeException("账户已冻结,无法登录");
        }

        return sysUserEntity;
    }
}
