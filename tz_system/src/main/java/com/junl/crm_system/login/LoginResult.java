package com.junl.crm_system.login;

import com.junl.crm_common.status.IdentityStatus;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Description:
 *
 * <AUTHOR> <PERSON>
 * @date 2024/1/2 14:47
 */
@Setter
@Getter
@Accessors(chain = true)
public class LoginResult {

    private String token;

    private Integer IdentityStatus;
}
