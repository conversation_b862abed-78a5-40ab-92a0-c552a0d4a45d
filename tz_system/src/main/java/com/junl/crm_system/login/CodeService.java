package com.junl.crm_system.login;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CipherUtils;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysLoginVo;
import lombok.AllArgsConstructor;
import org.springframework.util.ObjectUtils;

/**
 * 账户密码加验证码登录校验类
 * <AUTHOR>
 */
@AllArgsConstructor
public class CodeService implements LoginService {

   // private RedisUtils redisUtils;
    private SysLoginVo sysLoginVo;
    private SysUserService sysUserService;



    @Override
    public SysUserEntity register() {
        String userName = sysLoginVo.getUserName();
        String password = sysLoginVo.getPassword();

        Assert.notNull(userName,"用户名不能为空");
        Assert.notNull(password,"密码不能为null");

        SysUserEntity sysUserEntity = sysUserService.getEntity(userName);
        Assert.notNull(sysUserEntity,"该账户不存在");

        if (!CipherUtils.enCoderMd5(password).equals(sysUserEntity.getPassword())) {
            throw new IllegalArgumentException("密码不正确,请重新输入密码");
        }

        if(sysUserEntity.getStatus().equals(Opposite.SINGLE)){
            throw new RuntimeException("账户已冻结,无法登录");
        }
        SysUserSoleEntity soleId = sysUserService.getSoleId(sysUserEntity.getUserId());
        if (!ObjectUtils.isEmpty(soleId)) {
            sysUserEntity.setSoleId(soleId.getSoleId());
        }

        return sysUserEntity;
    }
}
