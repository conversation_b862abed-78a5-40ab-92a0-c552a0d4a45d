package com.junl.crm_system.login;

import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.constant.DySmsEnum;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.pojo.work.SmsRecordEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.ResponseCode;
import com.junl.crm_common.tools.sms.SmsRestResult;
import com.junl.crm_common.tools.sms.SmsSendParam;
import com.junl.crm_common.tools.sms.TzSmsHelper;
import com.junl.crm_work.dao.SmsErrorDao;
import com.junl.crm_work.wx.WxUtils;
import com.junl.msg.SocketUtils;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_system.admin.service.*;
import com.junl.crm_system.admin.vo.SysLoginVo;
import com.junl.msg.socket.ChannelSocketEntity;
import com.junl.msg.socket.SocketStatus;
import com.junl.msg.socket.WebSocketChannel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@RestController
@RequestMapping("/login")
@Api(tags = "登录服务")
@Log4j2
public class LoginController {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private LoginServiceHandler loginServiceHandler;

    @Autowired
    private LoginServiceFactory loginServiceFactory;


    @Autowired
    private TzSmsHelper tzSmsHelper;

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private WxUtils wxUtils;

    @Autowired
    private SmsErrorDao smsErrorDao;

    @GetMapping("/getCode")
    @ApiOperation("获取验证码")
    public Result getCOde(@RequestParam("userName") String userName) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();) {
            String code = VerifyCode.getCode(byteArrayOutputStream);
            redisUtils.set(RedisKey.CODE.getName() + userName, code, 120);
            String imgBase64 = CommonUtil.getImgBase64(byteArrayOutputStream);
            return Result.success(imgBase64);
        } catch (Exception e) {
            log.error("生成验证码错误：{}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }


    @GetMapping("/sendCode/{phone}")
    @ApiOperation("发送手机验证码")
    public Result sendCode(@PathVariable("phone") String phone) {
        SmsSendParam smsSendParam = new SmsSendParam();
        smsSendParam.setUserNumber(phone);
        String randomNumber = RandomsUtil.getRandomNumber(6);
        smsSendParam.setMessageContent(randomNumber);

        SmsRestResult smsRestResult = tzSmsHelper.sendSms(smsSendParam, DySmsEnum.LOGIN_TEMPLATE_CODE);
        if (smsRestResult.getResult() == Integer.valueOf(Opposite.ZERO)) {
            redisUtils.set(RedisKey.APPLET_PHONE.getName() + phone, randomNumber, 120);
            return Result.success();
        } else {

            smsErrorDao.insert(
                    new SmsRecordEntity()
                            .setPhone(phone)
                            .setDes(smsRestResult.getDescription())
            );
            return Result.error("短信繁忙,请稍后重试.");
        }
    }


    @PostMapping("/authToken")
    public Result authToken(@RequestBody String token, HttpServletRequest request) {
        try {
            DecodedJWT decode = JWT.decode(token);
            Claim adName = decode.getClaim("adName");
            Claim expTime = decode.getClaim("expTime");
            if (ObjectUtils.isEmpty(adName) || ObjectUtils.isEmpty(expTime)) {
                throw new JWTDecodeException("");
            }
            Date parse = DateUtils.parse(expTime.asString(), "yyyy-MM-dd");

            if (LocalDate.now().compareTo(
                    LocalDate.from(parse.toInstant().atZone(ZoneId.systemDefault()))
            ) > 0) {
                return new Result("token过期", null, ResponseCode.OVERDUE.getCode());
            }
            SysUserEntity register = sysUserService.getEntity(adName.asString());
            if (ObjectUtils.isEmpty(register)) {
                return Result.error("用户不存在");
            }

            String sysToken = JWTUtil.createToken(register);


            SysUserSoleEntity soleId = sysUserService.getSoleId(register.getUserId());

//            Map<String, ChannelSocketEntity> handlerList = WebSocketChannel.handlerList;
//            handlerList.forEach((x,y)->{
//                System.out.println("key："+x);
//                System.out.println("value:"+y.getName());
//            });
            if (!ObjectUtils.isEmpty(soleId)) {
                List<ChannelSocketEntity> channel = SocketUtils.query(
                        WebSocketChannel.handlerList, soleId.getSoleId()
                );
                if (!CollectionUtils.isEmpty(channel)) {
                    //在线就发送给前端
                    Result success = Result.success();
                    success.setCode(SocketStatus.EXIT.getCode());
                    success.setData("您的账号已在别处登录,如非本人操作请及时修改密码.登录IP：" + HttpUtils.getRealIp(request));
                    for (ChannelSocketEntity socketEntity : channel) {
                        socketEntity.getContext().writeAndFlush(
                                new TextWebSocketFrame(JSONObject.toJSONString(success))
                        );
                    }
                }
            }
            return Result.success(
                    new LoginResult()
                            .setToken(sysToken)
                            .setIdentityStatus(register.getIdentityCode())
            );
        } catch (JWTDecodeException e) {
            return Result.error("token校验失败,非法访问");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }

    }

    @PostMapping("/register")
    @ApiOperation("登录")
    public Result register(@RequestBody SysLoginVo sysLoginVo, HttpServletRequest request) {
        //获取校验类
        LoginService service = loginServiceFactory.getService(sysLoginVo);
        //开始执行登录校验
        loginServiceHandler.setLoginService(service);
        SysUserEntity register = loginServiceHandler.register();
        //校验通过 生成token

        //小程序端登陆生成永久token
        if (sysLoginVo.getStatus().equals(LoginEnum.PHONE.getCode())) {
            String alwaysToken = JWTUtil.createAlwaysToken(register);
            return Result.success(alwaysToken);
        }
        String token = JWTUtil.createToken(register);
        //加入登录日志
        //LoginLogService.addLog(register,sysLogService);
        //更新最后登录时间
//        if(!register.getUserId().equals(IdentityStatus.GOD.getName())){
//            register.setFinallyLoginTime(new Date());
//            sysUserService.updateById(register);
//        }

        if (sysLoginVo.getStatus().equals(LoginEnum.CODE.getCode())) {
            SysUserSoleEntity soleId = sysUserService.getSoleId(register.getUserId());
            if (!ObjectUtils.isEmpty(soleId)) {
//                Map<String, ChannelSocketEntity> handlerList = WebSocketChannel.handlerList;
//                handlerList.forEach((x,y)->{
//                    System.out.println("key："+x);
//                    System.out.println("value:"+y.getName());
//                });
                List<ChannelSocketEntity> channel = SocketUtils.query(
                        WebSocketChannel.handlerList, soleId.getSoleId()
                );
                if (!CollectionUtils.isEmpty(channel)) {
                    //在线就发送给前端
                    Result success = Result.success();
                    success.setCode(SocketStatus.EXIT.getCode());
                    success.setData("您的账号已在别处登录,如非本人操作请及时修改密码.登录IP：" + HttpUtils.getRealIp(request));
                    for (ChannelSocketEntity socketEntity : channel) {
                        socketEntity.getContext().writeAndFlush(new TextWebSocketFrame(JSONObject.toJSONString(success)));
                    }
                }
            }

        }
        return Result.success(
                new LoginResult()
                        .setToken(token)
                        .setIdentityStatus(register.getIdentityCode())
        );
    }

    @GetMapping("/test")
    public Result init() {

        return Result.success();
    }
}
