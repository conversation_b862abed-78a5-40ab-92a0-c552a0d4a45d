package com.junl.crm_system.login;

import java.util.Arrays;
import java.util.Optional;

/**
 * 登录枚举类
 */
public enum LoginEnum {
    //saas管理员登录
    SAAS_ADMIN(100),
    //系统维护人员登录
    MANAGEMENT(120),
    APPLET(2),
    //验证码登录(后台)
    CODE(0),
    //手机号登录(小程序后台)
    PHONE(1);


    private Integer code;
    LoginEnum(Integer code){
        this.code=code;
    }

    public Integer getCode(){
        return this.code;
    }

    public static LoginEnum getInstance(Integer code){
        Optional<LoginEnum> first = Arrays.stream(LoginEnum.values()).filter(x -> x.getCode().equals(code)).findFirst();
        return first.isPresent()?first.get():null;
    }
}
