package com.junl.crm_system.login;


import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysLoginVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 登录工厂  用户获取对应的校验类  每次新增登录方式 只需要改工厂 和新增校验类即可 其他都不需要动
 */
@Component
public class LoginServiceFactory {


    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SysUserService sysUserService;

    public LoginService getService(SysLoginVo sysLoginVo){
        //获取登录方式
        LoginEnum instance = LoginEnum.getInstance(sysLoginVo.getStatus());
        switch (instance){
            case CODE:
                return new CodeService(sysLoginVo,sysUserService);
            case PHONE:
                //还未有手机登录 后面补充
                return new AppletService(redisUtils,sysLoginVo,sysUserService);
            case SAAS_ADMIN:
                return new DefaultService(sysLoginVo);
            case MANAGEMENT:
                return new ManagementService(sysLoginVo,sysUserService);
            default:
                throw new RuntimeException("非法访问，无法识别的登录方式");
        }
    }
}
