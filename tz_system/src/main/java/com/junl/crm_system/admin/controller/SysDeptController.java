package com.junl.crm_system.admin.controller;

import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysDeptEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysDeptService;
import com.junl.crm_system.admin.vo.SysDeptVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.annotation.Log;
import com.junl.crm_common.status.IdentityStatus;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysDept")
@Api(tags = "部门表接口")
@Log("部门模块")
public class SysDeptController extends ParentController {

    @Autowired
    private SysDeptService sysDeptService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "部门表表--分页列表查询",notes="部门表表--分页列表查询")
    @PostMapping("/pageList")
    @Log(type = LogType.QUERY)
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysDeptEntity.class),
            @ApiResponse(code = 500,message = "error")})
    public Result pageList(@RequestBody SysDeptVo sysDeptVo){
        sysDeptVo.setCompanyId(getCompanyId());
        PageEntity page = sysDeptService.queryPage(sysDeptVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "部门表表--列表查询",notes="部门表表--列表查询")
    @GetMapping("/getList")
    @Log(type = LogType.QUERY)
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysDeptEntity.class),
            @ApiResponse(code = 500,message = "error")})
    public Result getList(){
        List<SysDeptEntity> list = sysDeptService.getAll(getCompanyId());
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "部门表查询详情",notes="部门表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "deptId", value = "deptId",required=true))
    @GetMapping("/info/{deptId}")
    @Log(type = LogType.QUERY)
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysDeptEntity.class),
            @ApiResponse(code = 500,message = "error")})
    public Result info(@PathVariable("deptId") String deptId){
        return Result.success(sysDeptService.getById(deptId));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "部门表列表--新增", notes = "部门表列表--新增")
    @PostMapping("/save")
    @Log(type = LogType.INSERT)
    public Result save(@RequestBody SysDeptEntity sysDeptEntity){
        if (!Assert.notNull(sysDeptEntity.getParentId())) {
            sysDeptEntity.setParentId(IdentityStatus.GOD.getName());
        }
        sysDeptEntity.setDeptId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysDeptEntity,null));
        RuleTools.setField(sysDeptEntity,getSysUserEntity(),true);
        sysDeptService.saveDept(sysDeptEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "部门表--修改", notes = "部门表--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysDeptEntity sysDeptEntity){
        Validate.startValidate(new NotNullVerification(sysDeptEntity,null));
        RuleTools.setField(sysDeptEntity,getSysUserEntity(),false);
        sysDeptService.updateDept(sysDeptEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */


    @ApiOperation(value = "部门表--删除", notes = "部门表--删除")
    @PostMapping("/delete")
    @Log(type = LogType.DELETE)
    public Result delete(@RequestBody String[] deptIds){
        if (sysDeptService.delete(Arrays.asList(deptIds))) {
            return Result.success();
        }else{
            return Result.error("该部门已有其他用户在职，请转移相关人员再删除");
        }

    }

    @ApiOperation("调离整个部门人员")
    @PostMapping("/relieveGuard ")
    @Log(type = LogType.UPDATE)
    public Result relieveGuard(@RequestBody SysDeptVo sysDeptVo){
        sysDeptService.relieveGuard(sysDeptVo);
        return Result.success();
    }
}