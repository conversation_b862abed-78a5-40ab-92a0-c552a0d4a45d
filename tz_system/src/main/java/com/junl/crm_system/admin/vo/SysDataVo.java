package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @description: SysDataVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "系统数据权限表表-Vo类")
@Data
public class SysDataVo extends ParentDto {
    @ApiModelProperty(notes = "数据权限名称", allowEmptyValue = true, required = false)
    private  String dataName;

    @ApiModelProperty(notes = "公司id")
    private String companyId;

}