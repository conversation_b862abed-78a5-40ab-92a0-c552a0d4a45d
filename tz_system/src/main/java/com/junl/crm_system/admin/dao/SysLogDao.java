package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysLogEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_system.admin.vo.SysLogVo;

import java.util.List;
/**
* @description: sys_logdao接口
* @author: daiqimeng
**/

public interface SysLogDao extends BaseMapper<SysLogEntity> {
    List<SysLogEntity> queryList(SysLogVo sysLogVo);
    SysLogEntity getInfo(String logId);
}