package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysDataRoleEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.util.ArrayList;
import java.util.List;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.dao.SysDataDao;
import com.junl.crm_system.admin.service.SysDataRoleService;
import com.junl.crm_system.admin.vo.SysDataVo;
import com.junl.crm_common.pojo.admin.SysDataEntity;
import com.junl.crm_system.admin.service.SysDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysDataServiceImpl extends ServiceImpl<SysDataDao, SysDataEntity> implements SysDataService {

    @Autowired
    private SysDataRoleService roleService;


    /**
     * @description: 分页查询列表
     * @param sysDataVo
     */
    @Override
    public PageEntity queryPage(SysDataVo sysDataVo) {
        PageUtils.execute(sysDataVo);
        List<SysDataEntity> list = baseMapper.queryList(sysDataVo);
        return PageUtils.getData(list);
    }

    @Override
    @Transactional
    public boolean saveData(SysDataEntity sysDataEntity) {
        sysDataEntity.setId(SnowFlake.getUUId());
        baseMapper.insert(sysDataEntity);


        List<SysDataRoleEntity> dataRole = sysDataEntity.getDataRole();
        dataRole.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setDataId(sysDataEntity.getId());
        });
        roleService.saveBatch(dataRole);
        return true;
    }

    @Override
    @Transactional
    public boolean updateData(SysDataEntity sysDataEntity) {
        baseMapper.updateById(sysDataEntity);

        roleService.remove(new LambdaQueryWrapper<SysDataRoleEntity>().eq(SysDataRoleEntity::getDataId,sysDataEntity.getId()));

        List<SysDataRoleEntity> dataRole = sysDataEntity.getDataRole();
        dataRole.forEach(x->{
            x.setId(SnowFlake.getUUId());
            x.setDataId(sysDataEntity.getId());
        });
        roleService.saveBatch(dataRole);
        return true;
    }

    @Override
    public boolean removeData(List<String> id) {
        //暂时不做 先保留
        return true;
    }

    @Override
    public SysDataEntity getInfo(String id) {
        return baseMapper.getInfo(id);
    }
}