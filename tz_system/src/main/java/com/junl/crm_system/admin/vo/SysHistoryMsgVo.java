package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SysHistoryMsgVo
* @author:      daiqimeng
**/
@ApiModel(description = "用户历史信息表表-Vo类")
@Data
public class SysHistoryMsgVo extends ParentDto {

    @ApiModelProperty(notes = "阅读人", required = false)
    private String reader;


    @ApiModelProperty(notes = "消息标题", required = false)
    private  String msgTitle;

}