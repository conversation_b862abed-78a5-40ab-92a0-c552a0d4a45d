package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.AppParentController;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysHistoryMsgEntity;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.status.ResponseCode;
import com.junl.crm_system.admin.service.SysHistoryMsgService;
import com.junl.crm_system.admin.service.SysUserMsgService;
import com.junl.crm_system.admin.vo.SysHistoryMsgVo;
import com.junl.crm_system.admin.vo.SysUserMsgVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("app/sell/sysUserMsg")
@Api(tags = "App用户消息表")
public class AppSysUserMsgController extends AppParentController {

    @Autowired
    private SysUserMsgService sysUserMsgService;

    // 历史消息
    @Autowired
    private SysHistoryMsgService sysHistoryMsgService;


    /*@ApiOperation("获取消息")
    @GetMapping("/getMsg")
    public Result getMsg(){
        String customerId = getSelCustomerEntity().getId();
        List<SysUserMsgEntity> list = sysUserMsgService.list(new LambdaQueryWrapper<SysUserMsgEntity>().eq(SysUserMsgEntity::getReader, customerId));
        sysUserMsgService.remove(new LambdaQueryWrapper<SysUserMsgEntity>().eq(SysUserMsgEntity::getReader,customerId));
        return Result.success(list);
    }*/



    @ApiOperation(value = "用户历史信息表表--分页列表查询",notes="用户历史信息表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SysHistoryMsgVo sysHistoryMsgVo){
        String customerId = getCustomerId();
        sysHistoryMsgVo.setReader(customerId);
        PageEntity page = sysHistoryMsgService.queryPage(sysHistoryMsgVo);
        return Result.success(page);
    }


   /* @ApiOperation(value = "获取消息查询详情",notes="获取消息查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "msgId", value = "msgId",required=true))
    @GetMapping("/info/{msgId}")
    public Result info(@PathVariable("msgId") String msgId){
        return Result.success(sysUserMsgService.getById(msgId));
    }
*/

    @ApiOperation("获取消息")
    @GetMapping("/getMsg")
    public Result getMsg(){
        String customerId = getCustomerId();
        List<SysHistoryMsgEntity> list = sysHistoryMsgService.list(
                new LambdaQueryWrapper<SysHistoryMsgEntity>()
                        .eq(SysHistoryMsgEntity::getReader, customerId)
                        .orderByDesc(SysHistoryMsgEntity::getCreateDate)
        );
        return Result.success(list);
    }

    @ApiOperation("获取未读总条数")
    @GetMapping("/getCount")
    public Result getCount(){
        String customerId = getCustomerId();
        Integer status= 0;
        List<SysHistoryMsgEntity> list = sysHistoryMsgService.list(new LambdaQueryWrapper<SysHistoryMsgEntity>().eq(SysHistoryMsgEntity::getReader, customerId).eq(SysHistoryMsgEntity::getStatus,status));
        Integer totalCount =list.size();
        return Result.success(totalCount);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "用户历史信息表查询详情",notes="用户历史信息表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "msgId", value = "msgId",required=true))
    @GetMapping("/info/{msgId}")
    public Result info(@PathVariable("msgId") String msgId){
        return Result.success(sysHistoryMsgService.getById(msgId));
    }


    /**
     * @description: 修改状态
     * @author: daiqimeng
     */
    @ApiOperation(value = "用户历史信息表--修改状态", notes = "用户历史信息表--修改状态")
    @PostMapping("/updateStatus")
    public Result updateStatus(@RequestBody  SysHistoryMsgEntity sysHistoryMsgEntity){
        SysHistoryMsgEntity msgEntity = sysHistoryMsgService.getById(sysHistoryMsgEntity.getMsgId());
        if(msgEntity==null) {
            return Result.error(ResponseCode.ERROR.getCode(), "Id不存在");
        }
        msgEntity.setStatus(1);
        sysHistoryMsgService.updateById(msgEntity);
        return Result.success();
    }


    /**
     * @description: 修改状态
     * @author: daiqimeng
     */
    @ApiOperation(value = "用户历史信息表--修改状态全部为已读", notes = "用户历史信息表--修改状态全部为已读")
    @PostMapping("/updateStatusByReaderId")
    public Result updateStatusByReaderId(@RequestBody  SysHistoryMsgEntity sysHistoryMsgEntity){
        String customerId = getCustomerId();
        SysHistoryMsgEntity upMsgEntity =new SysHistoryMsgEntity();
        upMsgEntity.setStatus(1);
        sysHistoryMsgService.update(
                upMsgEntity,
                new LambdaQueryWrapper<SysHistoryMsgEntity>()
                        .eq(SysHistoryMsgEntity::getReader, customerId)
        );
        return Result.success();
    }



}