package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_system.admin.vo.SysUserMsgVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * @description: sys_user_msgdao接口
 * @author: daiqimeng
 **/

public interface SysUserMsgDao extends BaseMapper<SysUserMsgEntity> {
    List<SysUserMsgEntity> queryList(SysUserMsgVo sysUserMsgVo);
    boolean saveHistory(SysUserMsgEntity sysUserMsgEntity);

    List<SysUserMsgEntity> queryHistory(SysUserMsgVo sysUserMsgVo);

    boolean updateStatus(String id);

    SysUserMsgEntity getInfo(String id);

    int updateStatusAccessory(@Param("busCode") Integer busCode,@Param("msgAccessory") String msgAccessory);
}