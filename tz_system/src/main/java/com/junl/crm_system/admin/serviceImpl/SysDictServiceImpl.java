package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.dao.SysCompanyDao;
import com.junl.crm_system.admin.dao.SysDictDao;
import com.junl.crm_system.admin.dao.SysDictValueDao;
import com.junl.crm_system.admin.service.SysCompanyService;
import com.junl.crm_system.admin.service.SysDictValueService;
import com.junl.crm_system.admin.vo.SysDictVo;
import com.junl.crm_common.pojo.admin.SysDictEntity;
import com.junl.crm_system.admin.service.SysDictService;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysDictServiceImpl extends ServiceImpl<SysDictDao, SysDictEntity> implements SysDictService {



    @Autowired
    private SysCompanyDao sysCompanyDao;

    @Autowired
    private SysDictValueDao sysDictValueDao;

    /**
     * @description: 分页查询列表
     * @param sysDictVo
     */

    @Override
    public PageEntity queryPage(SysDictVo sysDictVo) {
        PageUtils.execute(sysDictVo);
        List<SysDictEntity> list = baseMapper.queryList(sysDictVo);
        return PageUtils.getData(list);
    }

    @Override
    public PageEntity getValues(SysDictVo sysDictVo) {
        SysDictEntity sysDictEntity = baseMapper.selectOne(new LambdaQueryWrapper<SysDictEntity>().and(x -> x.eq(SysDictEntity::getDictFlag, sysDictVo.getKey()).eq(SysDictEntity::getCompanyId, sysDictVo.getCompanyId())));
        Assert.notNull(sysDictEntity,"该关键字字典不存在");
        sysDictVo.setDictId(sysDictEntity.getDictId());
        PageUtils.execute(sysDictVo);
        List<SysDictValueEntity> list=baseMapper.queryValue(sysDictVo);
        return PageUtils.getData(list);
    }

    @Override
    @Transactional
    public boolean saveCompanyAll(SysDictEntity sysDictEntity) {
        String dictFlag = sysDictEntity.getDictFlag();
        if (baseMapper.selectCount(new LambdaQueryWrapper<SysDictEntity>().and(it->it.eq(SysDictEntity::getDictFlag,dictFlag).ne(SysDictEntity::getDictId,sysDictEntity.getDictId()).eq(SysDictEntity::getCompanyId,IdentityStatus.GOD.getName())))>0) {
            throw new RuntimeException("字典标识重复");
        }
        sysDictEntity.setCompanyId(IdentityStatus.GOD.getName());
        baseMapper.insert(sysDictEntity);

        //查询出所有公司
        List<SysCompanyEntity> sysCompanyEntities = sysCompanyDao.selectList(new QueryWrapper<SysCompanyEntity>().select("company_id"));
        //查询所有公司的id
        List<String> ids = CommonUtil.collect(sysCompanyEntities, SysCompanyEntity::getCompanyId);

        ids.forEach(x->{
            sysDictEntity.setDictId(SnowFlake.getUUId());
            sysDictEntity.setCompanyId(x);
            baseMapper.insert(sysDictEntity);
        });
        return true;
    }

    @Override
    @Transactional
    public boolean updateCompanyAll(SysDictEntity sysDictEntity) {
        String dictFlag = sysDictEntity.getDictFlag();
        //根据唯一标识 查询所有需要修改的值
        List<SysDictEntity> list = baseMapper.selectList(new LambdaQueryWrapper<SysDictEntity>().eq(SysDictEntity::getDictFlag, sysDictEntity.getOldDictFlag()));
        list.forEach(x->{
            x.setDictFlag(dictFlag);
            x.setUpdateBy(sysDictEntity.getUpdateBy());
            x.setDictName(sysDictEntity.getDictName());
            x.setSort(sysDictEntity.getSort());
            baseMapper.updateById(x);
        });

        return true;
    }

    @Override
    public boolean deleteCompanyAll(String id) {
        SysDictEntity sysDictEntity = baseMapper.selectById(id);
        Assert.notNull(sysDictEntity,"未知的主键");
        //查询出要删除的主键 (用于删除他的value表)
        List<SysDictEntity> list = baseMapper.selectList(new QueryWrapper<SysDictEntity>().select("dict_id").eq("dict_flag", sysDictEntity.getDictFlag()));
        List<String> collect = CommonUtil.collect(list, SysDictEntity::getDictId);
        //删除主表数据
        baseMapper.delete(new LambdaQueryWrapper<SysDictEntity>().eq(SysDictEntity::getDictFlag,sysDictEntity.getDictFlag()));
        //删除value表数据
        sysDictValueDao.delete(new LambdaQueryWrapper<SysDictValueEntity>().in(SysDictValueEntity::getDictId,collect));
        return true;
    }

    @Override
    public List<SysDictEntity> queryDictAll(String companyId) {

        return baseMapper.queryDictAll(companyId);
    }
}