package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @description: SysCompanyVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "公司表表-Vo类")
@Data
public class SysCompanyVo extends ParentDto {
    @ApiModelProperty(notes = "法人", allowEmptyValue = true, required = false)
    private  String legalPerson;
    @ApiModelProperty(notes = "法人联系方式", allowEmptyValue = true, required = false)
    private  String phone;
    @ApiModelProperty(notes = "简称", allowEmptyValue = true, required = false)
    private  String abbreviation;

    @ApiModelProperty(notes = "用户名称")
    private String userName;

    @ApiModelProperty(notes = "公司名称")
    private String companyName;

}