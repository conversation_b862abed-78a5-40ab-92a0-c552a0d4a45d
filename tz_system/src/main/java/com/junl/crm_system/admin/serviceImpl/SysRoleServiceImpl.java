package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysJurisdictionEntity;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;

import java.util.*;

import com.junl.crm_system.admin.dao.SysRoleDao;
import com.junl.crm_system.admin.service.SysJurisdictionService;
import com.junl.crm_system.admin.vo.SysRoleVo;
import com.junl.crm_common.pojo.admin.SysRoleEntity;
import com.junl.crm_system.admin.service.SysRoleService;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_system.exception.RoleException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleDao, SysRoleEntity> implements SysRoleService {
    /**
     * @description: 分页查询列表
     * @param sysRoleVo
     */


    @Autowired
    private SysJurisdictionService sysJurisdictionService;

    @Override
    public PageEntity queryPage(SysRoleVo sysRoleVo) {
        PageUtils.execute(sysRoleVo);
        List<SysRoleEntity> list = baseMapper.queryList(sysRoleVo);
        list.forEach(x->{
            String jurisdiction = x.getJurisdiction();
            //多个权限逗号分隔
            String[] split = jurisdiction.split(",");
            ArrayList<String> arrayList = new ArrayList(Arrays.asList(split));
            //查询出对应的权限名称
            List<SysJurisdictionEntity> jurisdiction_name = sysJurisdictionService.list(new QueryWrapper<SysJurisdictionEntity>().select("jurisdiction_name").lambda().in(SysJurisdictionEntity::getJurisdictionId, arrayList));
            String collect = CommonUtil.collect(jurisdiction_name, SysJurisdictionEntity::getJurisdictionName, "、");
            //、号分割 赋值到角色关联的名称上给前端展示
            x.setJurisdictionName(collect);
        });
        return PageUtils.getData(list);
    }

    @Override
    public Set<String> getMenuIds(String roleId){
        //根据角色id 查询角色信息
        SysRoleEntity roleEntity = baseMapper.selectById(roleId);
        String jurisdiction = roleEntity.getJurisdiction();
        Set<String> setId=new HashSet<>();
        String[] split = jurisdiction.split(",");

        List<String> strings = Arrays.asList(split);
        //查询所有权限信息
        List<SysJurisdictionEntity> list = sysJurisdictionService.list(new LambdaQueryWrapper<SysJurisdictionEntity>().in(SysJurisdictionEntity::getJurisdictionId, strings));
        for (SysJurisdictionEntity jurisdictionEntity : list) {
            //判断是否有最高权限标识
            if(jurisdictionEntity.getJurisdictionFlag().equals(IdentityStatus.GOD.getName())){
                //抛给调用方 表示有最高权限查询所有菜单信息
                throw new RoleException();
            }
        }
        //关键多个权限 避免重复 用set 去重
        for (String s : split) {
            List<String> menuIds = sysJurisdictionService.getMenuIds(s);
            setId.addAll(menuIds);
        }

        return setId;
    }

    @Override
    public SysRoleEntity getInfo(String roleId) {
        SysRoleEntity roleEntity = baseMapper.selectById(roleId);
        String[] split = roleEntity.getJurisdiction().split(",");
        roleEntity.setJurisdictionArray(Arrays.asList(split));
        return roleEntity;
    }

    @Override
    public boolean delete(List<String> ids) {
        return baseMapper.delete(ids);
    }
}