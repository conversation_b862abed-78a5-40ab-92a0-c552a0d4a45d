package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysDeptEntity;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.pojo.work.SelCustomerEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.*;
import com.junl.crm_system.admin.service.SysCompanyService;
import com.junl.crm_system.admin.service.SysDeptService;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysUserVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.annotation.Log;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_system.admin.service.SysUserSoleService;
import com.junl.system.CustomerService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysUser")
@Api(tags = "系统用户接口")
@Log("用户管理模块")
public class SysUserController extends ParentController {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysDeptService sysDeptService;

    @Autowired
    private SysUserSoleService sysUserSoleService;

    @Autowired
    private CustomerService customerService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "系统用户表--分页列表查询",notes="系统用户表--分页列表查询")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysUserEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result pageList(@RequestBody SysUserVo sysUserVo){
        sysUserVo.setCompanyId(getCompanyId());
        PageEntity page = sysUserService.queryPage(sysUserVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "系统用户表--列表查询",notes="系统用户表--列表查询")
    @PostMapping("/getList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysUserEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getList(){
        List<SysUserEntity> list = sysUserService.list(new LambdaQueryWrapper<SysUserEntity>().and(x->x.eq(SysUserEntity::getCompanyId,getCompanyId())
                .eq(SysUserEntity::getStatus,Opposite.ZERO).and(i->i.ne(SysUserEntity::getDeptId,IdentityStatus.GOD.getName()).or().isNull(SysUserEntity::getDeptId))));
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "系统用户查询详情",notes="系统用户查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "userId", value = "userId",required=true))
    @GetMapping("/info/{userId}")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysUserEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result info(@PathVariable("userId") String userId){
        return Result.success(sysUserService.getInfo(userId));
    }



    /**
     * @description: 查询该用户详细信息
     * @author: daiqimeng
     */

    @ApiOperation(value = "查询当前用户详细信息")
    @ApiImplicitParams(@ApiImplicitParam(name = "userId", value = "userId",required=true))
    @GetMapping("/getDetails")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysUserEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result info(){
        return Result.success(sysUserService.getInfo(getUserId()));
    }


    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "系统用户列表--新增", notes = "系统用户列表--新增")
    @PostMapping("/save")
    @Log(type = LogType.INSERT)
    public Result save(@RequestBody SysUserEntity sysUserEntity){
        sysUserEntity.setUserId(SnowFlake.getUUId());
        sysUserEntity.setCompanyId(getCompanyId());
        sysUserEntity.setIdentityCode(IdentityStatus.USER.getCode());
        sysUserEntity.setPassword(CipherUtils.enCoderMd5(sysUserEntity.getPassword()));
        Validate.startValidate(new NotNullVerification(sysUserEntity,null));
        RuleTools.setField(sysUserEntity,getSysUserEntity(),true);


        SelCustomerEntity selCustomer = customerService.getSelCustomer(sysUserEntity.getPhone());
        if (!ObjectUtils.isEmpty(selCustomer)) {
            return Result.error("手机号已经存在，请修改后提交");
        }

        //查询同一个公司用户名是否重复
        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>()
                .and(it->it.eq(SysUserEntity::getUserName,sysUserEntity.getUserName())
                .eq(SysUserEntity::getDeleteFlag,Opposite.ZERO)
                .eq(SysUserEntity::getCompanyId,getCompanyId())))>0) {
            return Result.error("用户名已存在，请重新修改");
        }
        //查询同一个公司手机号是否重复
        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>()
                .and(it->it.eq(SysUserEntity::getPhone,sysUserEntity.getPhone())
                .eq(SysUserEntity::getDeleteFlag,Opposite.ZERO)
                .eq(SysUserEntity::getCompanyId,getCompanyId())))>0) {
            return Result.error("手机号码已被注册,请修改后提交.");
        }

        //根据用户名查询查看手机号是否一致
        SysUserEntity entity = sysUserService.getEntity(sysUserEntity.getUserName());
        if (Assert.notNull(entity)) {
            if(!entity.getPhone().equals(sysUserEntity.getPhone())){
                return Result.error("同一用户名手机号不一致,请修改后提交");
            }
        }

        //根据手机号查询查看用户名是否一致
        SysUserEntity user = sysUserService.getUser(sysUserEntity.getPhone());
        if (Assert.notNull(user)) {
            if(!user.getUserName().equals(sysUserEntity.getUserName())){
                return Result.error("同一手机号,用户名不一致,请修改后提交");
            }
        }

        //根据用户名和手机号查询是否有其他公司相同的账户
        SysUserEntity userEntity = sysUserService.getUserEntity(sysUserEntity.getUserName(), sysUserEntity.getPhone());
        if (Assert.notNull(userEntity)) {
            //查询该账户的唯一标识 绑定用户之间的关系
            SysUserSoleEntity soleId = sysUserService.getSoleId(userEntity.getUserId());
            soleId.setId(SnowFlake.getUUId());
            soleId.setUserId(sysUserEntity.getUserId());
            soleId.setSoleId(soleId.getSoleId());
            sysUserSoleService.save(soleId);

            //查询出该用户在不同公司的所有账户
            List<SysUserEntity> list = sysUserService.list(new LambdaQueryWrapper<SysUserEntity>().and(e ->
                    e.eq(SysUserEntity::getUserName, sysUserEntity.getUserName())
            .ne(SysUserEntity::getCompanyId,IdentityStatus.GOD.getName())));

            //如果密码不一样,全部更换密码
            if (!sysUserEntity.getPassword().equals(list.get(0).getPassword())) {
                List<SysUserEntity> tempUser=new ArrayList<>();
                list.forEach(x->{
                    SysUserEntity temp=new SysUserEntity();
                    temp.setUserId(x.getUserId());
                    temp.setDataId(x.getDataId());
                    temp.setPositionId(x.getPositionId());
                    temp.setPassword(sysUserEntity.getPassword());
                    temp.setDeptId(x.getDeptId());
                    tempUser.add(temp);
                });
                sysUserService.updateBatchById(tempUser);
            }

        }else {
            //新用户初始化系统唯一标识
            SysUserSoleEntity soleId =new SysUserSoleEntity();
            soleId.setSoleId(SnowFlake.getUUId());
            soleId.setId(SnowFlake.getUUId());
            soleId.setUserId(sysUserEntity.getUserId());
            sysUserSoleService.save(soleId);
        }
        sysUserService.save(sysUserEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "系统用户--修改", notes = "系统用户--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysUserEntity sysUserEntity){
        Validate.startValidate(new NotNullVerification(sysUserEntity,null));
        RuleTools.setField(sysUserEntity,getSysUserEntity(),true);

        SysDeptEntity deptEntity = sysDeptService.getOne(
                new LambdaQueryWrapper<SysDeptEntity>()
                        .eq(SysDeptEntity::getPrincipal, sysUserEntity.getUserId())
        );

        if(Assert.notNull(deptEntity)){
            if (!deptEntity.getDeptId().equals(sysUserEntity.getDeptId())) {
                throw new RuntimeException("部门负责人更换或脱离部门请先指定新的负责人");
            }
        }
        sysUserService.updateById(sysUserEntity);

        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */


    @ApiOperation(value = "系统用户--删除", notes = "系统用户--删除")
    @PostMapping("/delete")
    @Log(type = LogType.DELETE)
    public Result delete(@RequestBody String[] userIds){
        for (String userId : userIds) {
            SysUserEntity sysUserEntity=new SysUserEntity();
            sysUserEntity.setUserId(userId);
            sysUserEntity.setDeleteFlag(Opposite.SINGLE);
            sysUserService.updateById(sysUserEntity);
            //删除多账户之间的绑定关系
            sysUserSoleService.remove(new LambdaQueryWrapper<SysUserSoleEntity>()
            .eq(SysUserSoleEntity::getUserId,userId));

        }
        return Result.success();
    }

    @ApiOperation("根据用户信息 获取菜单")
    @GetMapping("/getMenu")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysMenuEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getMenu(){
        List<SysMenuEntity> menu = sysUserService.getMenu(getSysUserEntity());
        return Result.success(menu);
    }

    @ApiOperation("修改密码")
    @PostMapping("/updatePassword")
    public  Result updatePassword(@RequestBody SysUserVo sysUserVo){
        if (!Assert.notNull(sysUserVo.getUserId())) {
            sysUserVo.setUserId(getUserId());
        }
        return Result.success(sysUserService.updatePassword(sysUserVo));
    }

    @ApiOperation("管理员重置用户密码")
    @PostMapping("/adminUpdatePassword")
    public Result adminUpdatePassword(@RequestBody SysUserVo sysUserVo){
       return Result.success(sysUserService.adminUpdatePassword(sysUserVo));
    }



    @ApiOperation("修改用户名")
    @PostMapping("/updateUserName")
    public Result updateUserName(@RequestBody SysUserVo sysUserVo){
        SysUserEntity byId = sysUserService.getById(sysUserVo.getUserId());
        Assert.notNull(byId,"该用户不存在");

        if (byId.getUserName().equals(sysUserVo.getUserName())) {
            return Result.success();
        }

        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>()
        .eq(SysUserEntity::getUserName,sysUserVo.getUserName()))>0) {
            return Result.error("用户名已存在,请重新修改");
        }

        List<SysUserEntity> list = sysUserService.list(new LambdaQueryWrapper<SysUserEntity>().and(e ->
                e.eq(SysUserEntity::getUserName, byId.getUserName())
        .ne(SysUserEntity::getCompanyId,IdentityStatus.GOD.getName())));


        if (Assert.notNullCollect(list)) {
            list.forEach(x->{
                x.setUserName(sysUserVo.getUserName());
            });
        }
        byId.setUserName(sysUserVo.getUserName());
        list.add(byId);

        sysUserService.updateBatchById(list);
        return Result.success();
    }



    @ApiOperation("修改手机号")
    @PostMapping("/updatePhone")
    public Result updatePhone(@RequestBody SysUserVo sysUserVo){
        SysUserEntity byId = sysUserService.getById(sysUserVo.getUserId());
        Assert.notNull(byId,"该用户不存在");

        if (byId.getPhone().equals(sysUserVo.getPhone())) {
            return Result.success();
        }

        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getPhone,sysUserVo.getPhone()))>0) {
            return Result.error("手机号已被注册,请重新修改");
        }

        List<SysUserEntity> list = sysUserService.list(new LambdaQueryWrapper<SysUserEntity>().and(e ->
                e.eq(SysUserEntity::getPhone, byId.getPhone())
        .ne(SysUserEntity::getCompanyId,IdentityStatus.GOD.getName())));


        if (Assert.notNullCollect(list)) {
            list.forEach(x->{
                x.setPhone(sysUserVo.getPhone());
            });
        }
        byId.setPhone(sysUserVo.getPhone());
        list.add(byId);

        sysUserService.updateBatchById(list);
        return Result.success();
    }


    @ApiOperation("切换公司")
    @GetMapping("/switchCompany/{companyId}")
    public Result switchCompany(@PathVariable("companyId")String companyId){
        SysUserEntity byId = sysUserService.getById(getUserId());
        Assert.notNull(byId,"系统异常,请联系管理员");
        SysUserEntity sysUserEntity = sysUserService.getOne(new LambdaQueryWrapper<SysUserEntity>()
                .and(e -> e.eq(SysUserEntity::getCompanyId, companyId))
                .eq(SysUserEntity::getUserName,byId.getUserName()));

        Assert.notNull(sysUserEntity,"系统异常,请联系管理员");

        SysUserSoleEntity soleId = sysUserService.getSoleId(byId.getUserId());
        sysUserEntity.setSoleId(soleId.getSoleId());
        String token = JWTUtil.createToken(sysUserEntity);
        return Result.success(token);
    }

}