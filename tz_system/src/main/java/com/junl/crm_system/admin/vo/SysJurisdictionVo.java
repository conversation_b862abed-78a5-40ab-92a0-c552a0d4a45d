package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SysJurisdictionVo
* @author:      daiqimeng
**/
@ApiModel(description = "权限表表-Vo类")
@Data
public class SysJurisdictionVo extends ParentDto {

    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty(notes = "权限名称")
    private String jurisdictionName;

}