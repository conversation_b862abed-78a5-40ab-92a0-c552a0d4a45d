package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.annotation.Log;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_common.pojo.admin.SysNoticeWriteEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.*;
import com.junl.crm_system.admin.service.SysCompanyService;
import com.junl.crm_system.admin.service.SysNoticeWriteService;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysCompanyVo;
import com.junl.crm_system.admin.vo.SysNoticeWriteVo;
import com.junl.crm_system.admin.vo.SysUserVo;
import com.junl.crm_work.service.SelBiddingService;
import com.junl.crm_work.service.SelInquiryService;
import com.junl.crm_work.service.SelPlanService;
import com.junl.crm_work.vo.SelBiddingVo;
import com.junl.crm_work.vo.SelInquiryVo;
import com.junl.crm_work.vo.SelPlanVo;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @description: 总公司
 * @author: daiqimeng
 * @date: 2021/7/1318:03
 */
@RestController
@RequestMapping("/central")
@Api(tags = "总公司管理接口")
public class CentralOfficeController extends ParentController {

    @Autowired
    private SysCompanyService sysCompanyService;

    @Autowired
    private SysNoticeWriteService sysNoticeWriteService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SelBiddingService selBiddingService;

    @Autowired
    private SelInquiryService selInquiryService;

    @Autowired
    private SelPlanService selPlanService;


    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "销售竞价表--分页列表查询",notes="销售竞价表--分页列表查询")
    @PostMapping("/biddingPageList")
    public Result pageList(@RequestBody SelBiddingVo selBiddingVo){
        verify();
        PageEntity page = selBiddingService.queryPage(selBiddingVo);
        return Result.success(page);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "销售竞价查询详情",notes="销售竞价查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/biddingInfo/{id}")
    public Result biddingInfo(@PathVariable("id") String id){
        verify();
        return Result.success(selBiddingService.info(id));
    }

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "销售询价主表表--分页列表查询",notes="销售询价主表表--分页列表查询")
    @PostMapping("/inquiryPageList")
    public Result pageList(@RequestBody SelInquiryVo selInquiryVo){
        verify();
        PageEntity page = selInquiryService.queryPage(selInquiryVo);
        return Result.success(page);
    }


    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "销售询价主表查询详情",notes="销售询价主表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/inquiryInfo/{id}")
    public Result inquiryInfo(@PathVariable("id") String id){
        verify();
        return Result.success(selInquiryService.infoInquiry(id));
    }


    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "销售计划表--分页列表查询",notes="销售计划表--分页列表查询")
    @PostMapping("/planPageList")
    public Result planPageList(@RequestBody SelPlanVo selPlanVo){
        verify();
        PageEntity page = selPlanService.queryPage(selPlanVo);
        return Result.success(page);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "销售计划查询详情",notes="销售计划查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/planInfo/{id}")
    public Result planInfo(@PathVariable("id") String id){
        verify();
        return Result.success(selPlanService.getInfo(id));
    }



    @ApiOperation("新增总公司")
    @PostMapping("/saveParentCompany")
    @Log(type= LogType.INSERT)
    public Result saveParentCompany(@RequestBody SysCompanyEntity sysCompanyEntity){
        verify();
        sysCompanyEntity.setCompanyId(SnowFlake.getUUId());
        sysCompanyEntity.setFlag(sysCompanyEntity.getCompanyId());
        Validate.startValidate(new NotNullVerification(sysCompanyEntity,null));
        sysCompanyService.saveParentCompany(sysCompanyEntity);
        return Result.success();
    }

    /**
     * @description: 分页列表查询
     */
    @ApiOperation("查询所有公司")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response =SysCompanyEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result pageList(@RequestBody SysCompanyVo sysCompanyVo){
        verify();
        PageEntity page = sysCompanyService.queryPage(sysCompanyVo);
        return Result.success(page);
    }

    /**
     * @description: 修改公司信息
     * @author: daiqimeng
     */
    @ApiOperation("修改公司信息")
    @PostMapping("/update")
    @Log(type=LogType.UPDATE)
    public Result update(@RequestBody  SysCompanyEntity sysCompanyEntity){
        verify();

        if (sysCompanyService.count(new LambdaQueryWrapper<SysCompanyEntity>()
                .and(e->e.eq(SysCompanyEntity::getCompanyName,sysCompanyEntity.getCompanyName())
                .ne(SysCompanyEntity::getCompanyId,sysCompanyEntity.getCompanyId())))>0) {
            return Result.error("公司名称重复,请重新修改名称后提交.");
        }
        sysCompanyService.updateById(sysCompanyEntity);
        return Result.success();
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation("根据主键查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "companyId", value = "companyId",required=true))
    @GetMapping("/info/{companyId}")
    @Log(type = LogType.QUERY)
    @ApiResponses({@ApiResponse(code = 200,message = "success",response =SysCompanyEntity.class),
            @ApiResponse(code = 500,message = "error")})
    public Result info(@PathVariable("companyId") String companyId){
        verify();
        SysCompanyEntity byId = sysCompanyService.getById(companyId);
        byId.setBoss(sysUserService.getInfo(byId.getPrincipal()));
        return Result.success(byId);
    }



    @ApiOperation("新增管理用户")
    @PostMapping("/saveUser")
    public Result saveUser(@RequestBody SysUserEntity sysUserEntity){
        verify();
        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>().eq(SysUserEntity::getPhone,sysUserEntity.getPhone()))>0) {
            return Result.error("手机号码重复");
        }

        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>().eq(SysUserEntity::getUserName,sysUserEntity.getUserName()))>0) {
            return Result.error("账户重复");
        }

        sysUserEntity.setIdentityCode(IdentityStatus.MANAGEMENT.getCode());
        sysUserEntity.setCompanyId(IdentityStatus.GOD.getName());
        sysUserEntity.setPassword(CipherUtils.enCoderMd5(sysUserEntity.getPassword()));
        sysUserEntity.setUserId(SnowFlake.getUUId());
        sysUserService.save(sysUserEntity);
        return Result.success();
    }

    @ApiOperation("修改用户")
    @PostMapping("/updateUser")
    public Result updateUser(@RequestBody SysUserEntity sysUserEntity){
        verify();
        sysUserEntity.setUpdateTime(new Date());

        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>()
                .and(e->e.eq(SysUserEntity::getPhone,sysUserEntity.getPhone())
                .ne(SysUserEntity::getUserId,sysUserEntity.getUserId())))>0) {
            return Result.error("手机号码重复");
        }

        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>()
                .and(e->e.eq(SysUserEntity::getUserName,sysUserEntity.getUserName())
                .ne(SysUserEntity::getUserId,sysUserEntity.getUserId())))>0) {
            return Result.error("账户重复");
        }

        sysUserService.updateById(sysUserEntity);
        return Result.success();
    }

    @ApiOperation("删除用户")
    @GetMapping("/deleteUser/{id}")
    public Result delete (@PathVariable("id")String id){
        verify();
        sysUserService.removeById(id);
        return Result.success();
    }


    @ApiResponses({@ApiResponse(code = 200,message = "success",response =SysUserEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @ApiOperation("/queryUserPage")
    @PostMapping("/queryUserPage")
    public Result queryUserPage(@RequestBody SysUserVo sysUserVo){
        verify();
        sysUserVo.setIdentityStatus(IdentityStatus.MANAGEMENT.getCode());
        return Result.success(sysUserService.queryPage(sysUserVo));
    }

    @ApiResponses({@ApiResponse(code = 200,message = "success",response =SysUserEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @ApiOperation("获取用户详情")
    @GetMapping("/queryUserInfo/{id}")
    public Result queryUserInfo(@PathVariable("id")String id){
        verify();
        return Result.success(sysUserService.getById(id));
    }


    @PostMapping("/saveNotice")
    @ApiOperation("新增公告")
    public Result saveNotice(@RequestBody SysNoticeWriteEntity sysNoticeWriteEntity){
        verify();
        sysNoticeWriteEntity.setNoticeId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysNoticeWriteEntity,null));
        sysNoticeWriteEntity.setCompanyId(getCompanyId());
        sysNoticeWriteEntity.setCreateBy(getUserName());
        if (Assert.notNullCollect(sysNoticeWriteEntity.getAccessoryArray())) {
            sysNoticeWriteEntity.setAccessory(CommonUtil.collect(sysNoticeWriteEntity.getAccessoryArray(),String::toString,","));
        }



        if(sysNoticeWriteEntity.getStatus().toString().equals(Opposite.SINGLE)){
            //这里改为未发布状态 发布状态交给发布接口去处理 不然已经变更的状态无法通过校验
            sysNoticeWriteEntity.setStatus(0);
            sysNoticeWriteService.save(sysNoticeWriteEntity);
            sysNoticeWriteService.issueManagement(sysNoticeWriteEntity.getNoticeId());
        }else {
            sysNoticeWriteService.save(sysNoticeWriteEntity);
        }

        return Result.success();
    }
    @ApiOperation("修改公告")
    @PostMapping("/updateNotice")
    public Result updateNotice(@RequestBody SysNoticeWriteEntity sysNoticeWriteEntity){
        verify();
        Validate.startValidate(new NotNullVerification(sysNoticeWriteEntity,null));
        if (Assert.notNullCollect(sysNoticeWriteEntity.getAccessoryArray())) {
            sysNoticeWriteEntity.setAccessory(CommonUtil.collect(sysNoticeWriteEntity.getAccessoryArray(),String::toString,","));
        }
        sysNoticeWriteService.updateById(sysNoticeWriteEntity);
        if(sysNoticeWriteEntity.getStatus().toString().equals(Opposite.SINGLE)){
            sysNoticeWriteService.issueManagement(sysNoticeWriteEntity.getNoticeId());
        }
        return Result.success();
    }

    @ApiOperation("删除公告")
    @PostMapping("/deleteNotice/{id}")
    public Result updateNotice(@PathVariable("id")String id){
        verify();
        SysNoticeWriteEntity byId = sysNoticeWriteService.getById(id);
        if (byId.getStatus().toString().equals(Opposite.SINGLE)) {
            return Result.error("公告已发布,无法删除");
        }
        sysNoticeWriteService.removeById(id);
        return Result.success();
    }

    @ApiOperation("发布公告")
    @GetMapping("/pushNotice/{id}")
    public Result pushNotice (@PathVariable("id")String id){
        sysNoticeWriteService.issueManagement(id);
        return Result.success();
    }

    @ApiOperation("查询公告详情")
    @GetMapping("/queryNoticeInfo/{id}")
    public Result queryNoticeInfo(@PathVariable("id")String id){
        verify();
        SysNoticeWriteEntity byId = sysNoticeWriteService.getById(id);
        if (Assert.notNull(byId.getAccessory())) {
            String accessory = byId.getAccessory();
            String[] split = accessory.split(",");
            byId.setAccessoryArray(Arrays.asList(split));
        }
        return Result.success(byId);
    }

    @ApiOperation("查询公告")
    @PostMapping("/queryNoticePage")
    public Result queryNoticePage(@RequestBody SysNoticeWriteVo sysNoticeWriteVo) {
        verify();
        sysNoticeWriteVo.setCompanyId(getCompanyId());
        PageEntity pageEntity = sysNoticeWriteService.queryPage(sysNoticeWriteVo);
        return Result.success(pageEntity);
    }

    private void verify(){
        if (!getCompanyId().equals(IdentityStatus.GOD.getName())) {
            throw new RuntimeException("非法访问");
        }
    }

}
