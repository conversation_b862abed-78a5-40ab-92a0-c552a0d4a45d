package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.admin.SysRoleEntity;
import com.junl.crm_system.admin.vo.SysRoleVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;
import java.util.Set;

/**
* @description:SysRoleservice接口
* @author: daiqimeng
**/
public interface SysRoleService extends IService<SysRoleEntity> {
    PageEntity queryPage(SysRoleVo sysRoleVo);

    /**
     * 根据角色id 获取所有菜单id
     * @param roleId
     * @return
     */
    Set<String> getMenuIds(String roleId);

    SysRoleEntity getInfo(String roleId);

    boolean delete(List<String> ids);

}