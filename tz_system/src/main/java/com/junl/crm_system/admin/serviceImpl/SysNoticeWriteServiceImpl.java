package com.junl.crm_system.admin.serviceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.*;
import com.junl.crm_system.admin.dao.SysHistoryMsgDao;
import com.junl.crm_system.admin.dao.SysUserMsgDao;
import com.junl.msg.common.MsgType;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.*;
import com.junl.crm_common.common.PageEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.junl.crm_system.admin.dao.SysNoticeWriteDao;
import com.junl.crm_system.admin.service.SysCompanyService;
import com.junl.crm_system.admin.service.SysUserMsgService;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysNoticeWriteVo;
import com.junl.crm_system.admin.service.SysNoticeWriteService;
import com.junl.crm_work.status.BusCode;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
@Log4j2
public class SysNoticeWriteServiceImpl extends ServiceImpl<SysNoticeWriteDao, SysNoticeWriteEntity> implements SysNoticeWriteService {



    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserMsgService sysUserMsgService;

    @Autowired
    private SysCompanyService sysCompanyService;

    /**
     * @description: 分页查询列表
     * @param sysNoticeWriteVo
     */
    @Override
    public PageEntity queryPage(SysNoticeWriteVo sysNoticeWriteVo) {
        PageUtils.execute(sysNoticeWriteVo);
        List<SysNoticeWriteEntity> list = baseMapper.queryList(sysNoticeWriteVo);
        return PageUtils.getData(list);
    }

    @Override
    @Transactional
    public boolean issue(String id,String companyId) {
        SysNoticeWriteEntity sysNoticeWriteEntity = baseMapper.selectById(id);
        if (Assert.notNull(sysNoticeWriteEntity)) {
            throw new RuntimeException("找不到该条公告信息");
        }
        //更新状态
        sysNoticeWriteEntity.setStatus(Integer.valueOf(Opposite.SINGLE));
        baseMapper.updateById(sysNoticeWriteEntity);

        //根据发布公告的公司 查询出该公司所有正常状态的用户
        List<SysUserEntity> list = sysUserService.list(new QueryWrapper<SysUserEntity>().select("user_id")
                .and(e->e.eq("company_id", companyId).eq("status",Opposite.ZERO)
                        .eq("delete_flag",Opposite.ZERO))
        );
        //取出id
        List<String> ids = CommonUtil.collect(list, SysUserEntity::getUserId);


        //发布公告  不需要及时响应 开个线程跑
        ThreadPoolUtil.runTask(() -> {
            ids.forEach(x->{
                SysUserMsgEntity sysUserMsgEntity=new SysUserMsgEntity();
                copy(sysNoticeWriteEntity,sysUserMsgEntity);
                sysUserMsgEntity.setBusCode(BusCode.NOTICE.getCode());
                sysUserMsgEntity.setMsgType(MsgType.USER.getCode());
                sysUserMsgEntity.setReader(x);
//                rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.USER.getKey());
            });
        });

        return true;
    }

    @Override
    public boolean issueManagement(String id) {
        SysNoticeWriteEntity sysNoticeWriteEntity = baseMapper.selectById(id);

        if (!sysNoticeWriteEntity.getStatus().equals(Integer.valueOf(Opposite.ZERO))) {
            return false;
        }
        SysUserMsgEntity sysUserMsgEntity = new SysUserMsgEntity();
        copy(sysNoticeWriteEntity,sysUserMsgEntity);
        //查询出所有公司负责人
        List<SysCompanyEntity> list = sysCompanyService.list(new QueryWrapper<SysCompanyEntity>().select("company_id", "principal"));


        ThreadPoolUtil.runTask(()->{

            if (!CollectionUtils.isEmpty(list)) {
                List<SysHistoryMsgEntity> msgAll=new ArrayList<>();

                list.forEach(x->{
                    /** 发送消息给各公司负责人 */
                    String principal = x.getPrincipal();
                    sysUserMsgEntity.setReader(principal);
                    sysUserMsgEntity.setBusCode(BusCode.NOTICE.getCode());
                    sysUserMsgEntity.setMsgType(MsgType.USER.getCode());
                    //消息存储
                    SysHistoryMsgEntity temp = new SysHistoryMsgEntity();
                    BeanUtils.copyProperties(sysUserMsgEntity,temp);
                    temp.setMsgId(SnowFlake.getUUId());
                    msgAll.add(temp);
//                rabbitMqUtils.sendOnlyMsg(sysUserMsgEntity,MsgType.USER.getKey());
                });

                SqlBatchUtil.execute(
                        msgAll,
                        SysHistoryMsgDao.class,
                        (t,m)->m.insert(t)
                );
            }
        });

        if (!sysNoticeWriteEntity.getStatus().equals(Integer.valueOf(Opposite.SINGLE))) {
            sysNoticeWriteEntity.setStatus(Integer.valueOf(Opposite.SINGLE));
            baseMapper.updateById(sysNoticeWriteEntity);
        }

        return true;
    }

    private void copy(SysNoticeWriteEntity sysNoticeWriteEntity,SysUserMsgEntity sysUserMsgEntity){
        sysUserMsgEntity.setMsgContent(sysNoticeWriteEntity.getNoticeContent());
        sysUserMsgEntity.setStatus(Opposite.ZERO);
        sysUserMsgEntity.setMsgAccessory(sysNoticeWriteEntity.getAccessory());
        sysUserMsgEntity.setCompanyId(sysNoticeWriteEntity.getCompanyId());
        sysUserMsgEntity.setMsgTitle(sysNoticeWriteEntity.getNoticeTitle());
        sysUserMsgEntity.setMsgType(MsgType.USER.getCode());
        sysUserMsgEntity.setCreateDate(new Date());
        sysUserMsgEntity.setCreateBy(sysNoticeWriteEntity.getCreateBy());
    }
}