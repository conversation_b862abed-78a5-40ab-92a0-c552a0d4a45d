package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_system.admin.vo.SysUserVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description:SysUserservice接口
 * @author: daiqimeng
 **/
public interface SysUserService extends IService<SysUserEntity> {
    PageEntity queryPage(SysUserVo sysUserVo);
    List<SysMenuEntity> getMenu(SysUserEntity sysUserEntity);
    //查询公司无部门人员
    List<SysUserEntity> getUsers(String companyId);
    SysUserEntity getInfo(String userId);
    boolean updatePassword(SysUserVo sysUserVo);
    boolean adminUpdatePassword(SysUserVo sysUserVo);
    SysUserEntity getEntity(String userName);
    SysUserEntity getUser(String phone);
    SysUserEntity getUserEntity(String userName,String phone);
    SysUserSoleEntity getSoleId(String userId);

    boolean updateOpenId(String openId, String soleId);
}