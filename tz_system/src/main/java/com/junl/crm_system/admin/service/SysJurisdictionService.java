package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.admin.SysJurisdictionEntity;
import com.junl.crm_system.admin.vo.SysJurisdictionVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SysJurisdictionservice接口
* @author: daiqimeng
**/
public interface SysJurisdictionService extends IService<SysJurisdictionEntity> {
    PageEntity queryPage(SysJurisdictionVo sysJurisdictionVo);
    boolean insert(SysJurisdictionEntity sysJurisdictionEntity);
    boolean update(SysJurisdictionEntity sysJurisdictionEntity);
    boolean delete(List<String> ids);
    SysJurisdictionEntity getInfo(String id);
    List<String> getMenuIds(String jurisdictionId);
}