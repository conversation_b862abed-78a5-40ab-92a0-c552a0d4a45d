package com.junl.crm_system.admin.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysCompanyService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.annotation.Log;
import com.junl.crm_system.admin.service.SysUserService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysCompany")
@Api(tags = "公司表接口")
@Log("公司模块")
public class SysCompanyController extends ParentController {

    @Autowired
    private SysCompanyService sysCompanyService;

    @Autowired
    private SysUserService sysUserService;

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "公司表表--列表查询",notes="公司表表--列表查询")
    @PostMapping("/getList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response =SysCompanyEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getList(){
        List<SysCompanyEntity> list = sysCompanyService.list(new QueryWrapper<SysCompanyEntity>().select("company_id","company_name"));
        return Result.success(list);
    }

//    /**
//     * @description:  根据主键删除
//     * @author: daiqimeng
//     */
//    @ApiOperation(value = "公司表--删除", notes = "公司表--删除")
//    @PostMapping("/delete")
//    @Log(type=LogType.DELETE)
//    public Result delete(@RequestBody String[] companyIds){
//        sysCompanyService.removeByIds(Arrays.asList(companyIds));
//        return Result.success();
//    }



    @ApiOperation("获取公司架构和人员")
    @GetMapping("/getAllOrganization")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response =SysCompanyEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type=LogType.QUERY)
    public Result getAllOrganization(){
        SysCompanyEntity allOrganization = sysCompanyService.getAllOrganization(getCompanyId());
        return Result.success(allOrganization);
    }

    @ApiOperation("公司新增菜单 （全局 所有公司的）")
    @PostMapping("/saveMenu")
    public Result saveMenu(@RequestBody SysMenuEntity sysMenuEntity){
        sysMenuEntity.setMenuId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysMenuEntity,null));
        RuleTools.setField(sysMenuEntity,getSysUserEntity(),true);
        sysCompanyService.saveMenu(sysMenuEntity);
        return Result.success();
    }

    @ApiOperation("公司更新菜单 （全局 所有公司的）")
    @PostMapping("/updateMenu")
    public Result updateMenu(@RequestBody SysMenuEntity sysMenuEntity){
        Validate.startValidate(new NotNullVerification(sysMenuEntity,null));
        RuleTools.setField(sysMenuEntity,getSysUserEntity(),false);
        sysCompanyService.updateMenu(sysMenuEntity);
        return Result.success();
    }

    @ApiOperation("公司删除菜单 （全局 所有公司的）")
    @GetMapping("/deleteMenu/{id}")
    public Result deleteMenu(@PathVariable("id")String id){
        sysCompanyService.deleteMenu(id);
        return Result.success();
    }

    @ApiOperation("查询账户所在公司")
    @GetMapping("/queryCompanyAll")
    public Result queryCompanyAll(){
        SysUserEntity byId = sysUserService.getById(getUserId());

        List<SysUserEntity> users = sysUserService.list(new QueryWrapper<SysUserEntity>().select("company_id")
                .lambda().eq(SysUserEntity::getUserName, byId.getUserName())
                .ne(SysUserEntity::getCompanyId,getCompanyId()));

        if(Assert.notNullCollect(users)){
            List<SysCompanyEntity> list = sysCompanyService.list(new QueryWrapper<SysCompanyEntity>()
                    .select("company_id", "company_name").lambda()
                    .in(SysCompanyEntity::getCompanyId, CommonUtil.collect(users, SysUserEntity::getCompanyId)));
            return Result.success(list);
        }else{
            return Result.success(new ArrayList<>());
        }
    }
}