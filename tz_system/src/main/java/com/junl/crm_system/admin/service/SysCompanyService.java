package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_system.admin.vo.SysCompanyVo;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description:SysCompanyservice接口
 * @author: daiqimeng
 **/
public interface SysCompanyService extends IService<SysCompanyEntity> {
    PageEntity queryPage(SysCompanyVo sysCompanyVo);

    Boolean saveParentCompany(SysCompanyEntity sysCompanyEntity);

    SysCompanyEntity getAllOrganization(String companyId);
    List<SysCompanyEntity> getUserCompanyAll(SysCompanyVo sysCompanyVo);


    boolean saveMenu(SysMenuEntity sysMenuEntity);


    boolean updateMenu( SysMenuEntity sysMenuEntity);


    boolean deleteMenu(String id);
}