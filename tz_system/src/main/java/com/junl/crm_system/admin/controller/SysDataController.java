package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysDataEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_system.admin.service.SysDataService;
import com.junl.crm_system.admin.vo.SysDataVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("sys/sysData")
@Api(tags = "系统数据权限表接口")
public class SysDataController extends ParentController {

    @Autowired
    private SysDataService sysDataService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "系统数据权限表表--分页列表查询",notes="系统数据权限表表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SysDataVo sysDataVo){
        sysDataVo.setCompanyId(getCompanyId());
        PageEntity page = sysDataService.queryPage(sysDataVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "系统数据权限表表--列表查询",notes="系统数据权限表表--列表查询")
    @PostMapping("/getList")
    public Result getList(){
        List<SysDataEntity> list = sysDataService.list(new LambdaQueryWrapper<SysDataEntity>().eq(SysDataEntity::getCompanyId,getCompanyId()));
        SysDataEntity sysDataEntity=new SysDataEntity();
        sysDataEntity.setId(Opposite.ZERO);
        sysDataEntity.setDataName("系统默认");
        list.add(sysDataEntity);
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "系统数据权限表查询详情",notes="系统数据权限表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "id",required=true))
    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") String id){
        return Result.success(sysDataService.getInfo(id));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "系统数据权限表列表--新增", notes = "系统数据权限表列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SysDataEntity sysDataEntity){

        if (sysDataService.count(new LambdaQueryWrapper<SysDataEntity>().and(e->{
            e.eq(SysDataEntity::getCompanyId,getCompanyId()).eq(SysDataEntity::getDataName,sysDataEntity.getDataName());
        }))>0) {
            throw new RuntimeException("数据权限名称重复,请重新输入");
        }
        RuleTools.setField(sysDataEntity,getSysUserEntity(),true);
        sysDataService.saveData(sysDataEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "系统数据权限表--修改", notes = "系统数据权限表--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SysDataEntity sysDataEntity){

        if (sysDataService.count(new LambdaQueryWrapper<SysDataEntity>().and(e->{
            e.eq(SysDataEntity::getCompanyId,getCompanyId()).eq(SysDataEntity::getDataName,sysDataEntity.getDataName())
                    .ne(SysDataEntity::getId,sysDataEntity.getId());
        }))>0) {
            throw new RuntimeException("数据权限名称重复,请重新输入");
        }

        RuleTools.setField(sysDataEntity,getSysUserEntity(),false);
        sysDataService.updateData(sysDataEntity);
        return Result.success();
    }

//    /**
//     * @description:  根据主键删除
//     * @author: daiqimeng
//     */
//    @ApiOperation(value = "系统数据权限表--删除", notes = "系统数据权限表--删除")
//    @PostMapping("/delete")
//    public Result delete(@RequestBody List<String> ids){
//        sysDataService.removeData(ids);
//        return Result.success();
//    }
}