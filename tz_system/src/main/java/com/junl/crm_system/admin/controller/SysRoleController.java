package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysRoleEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysRoleService;
import com.junl.crm_system.admin.vo.SysRoleVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.annotation.Log;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysRole")
@Api(tags = "角色表接口")
@Log("角色管理模块")
public class SysRoleController extends ParentController {

    @Autowired
    private SysRoleService sysRoleService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "角色表表--分页列表查询",notes="角色表表--分页列表查询")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysRoleEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result pageList(@RequestBody SysRoleVo sysRoleVo){
        sysRoleVo.setCompanyId(getCompanyId());
        PageEntity page = sysRoleService.queryPage(sysRoleVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "角色表--列表查询",notes="角色表--列表查询")
    @PostMapping("/getList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysRoleEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getList(){
        List<SysRoleEntity> list = sysRoleService.list(new LambdaQueryWrapper<SysRoleEntity>().and(x->x.eq(SysRoleEntity::getCompanyId,getCompanyId()).eq(SysRoleEntity::getDeleteFlag, Opposite.ZERO)));
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "角色表查询详情",notes="角色表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "roleId", value = "roleId",required=true))
    @GetMapping("/info/{roleId}")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysRoleEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result info(@PathVariable("roleId") String roleId){
        return Result.success(sysRoleService.getInfo(roleId));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "角色表列表--新增", notes = "角色表列表--新增")
    @PostMapping("/save")
    @Log(type = LogType.INSERT)
    public Result save(@RequestBody SysRoleEntity sysRoleEntity){
        sysRoleEntity.setRoleId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysRoleEntity,null));
        RuleTools.setField(sysRoleEntity,getSysUserEntity(),true);
        sysRoleService.save(sysRoleEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "角色表--修改", notes = "角色表--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysRoleEntity sysRoleEntity){
        Validate.startValidate(new NotNullVerification(sysRoleEntity,null));
        RuleTools.setField(sysRoleEntity,getSysUserEntity(),false);
        sysRoleService.updateById(sysRoleEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @Log(type = LogType.DELETE)
    @ApiOperation(value = "角色表--删除", notes = "角色表--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] roleIds){
        sysRoleService.delete(Arrays.asList(roleIds));
        return Result.success();
    }
}