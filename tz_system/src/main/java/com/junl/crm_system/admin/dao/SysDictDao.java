package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysDictEntity;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_system.admin.vo.SysDictVo;

import java.util.List;
/**
* @description: sys_dictdao接口
* @author: daiqimeng
**/

public interface SysDictDao extends BaseMapper<SysDictEntity> {
    List<SysDictEntity> queryList(SysDictVo sysDictVo);

    List<SysDictValueEntity>queryValue(SysDictVo sysDictVo);

    List<SysDictEntity> queryDictAll(String companyId);
}