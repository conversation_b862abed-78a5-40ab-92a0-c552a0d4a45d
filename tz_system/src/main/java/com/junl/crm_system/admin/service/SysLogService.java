package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.admin.SysLogEntity;
import com.junl.crm_system.admin.vo.SysLogVo;
import com.junl.crm_common.common.PageEntity;

/**
* @description:SysLogservice接口
* @author: daiqimeng
**/
public interface SysLogService extends IService<SysLogEntity> {
    PageEntity queryPage(SysLogVo sysLogVo);

    SysLogEntity getInfo(String logId);
}