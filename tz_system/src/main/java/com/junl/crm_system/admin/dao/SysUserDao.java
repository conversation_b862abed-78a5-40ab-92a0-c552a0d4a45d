package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_system.admin.vo.SysUserVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * @description: sys_userdao接口
 * @author: daiqimeng
 **/

public interface SysUserDao extends BaseMapper<SysUserEntity> {
    List<SysUserEntity> queryList(SysUserVo sysUserVo);
    List<SysUserEntity> getUsers(String companyId);
    SysUserEntity getInfo(String userId);
    SysUserEntity getUserEntity(@Param("userName") String userName,@Param("phone") String phone);
    SysUserEntity getUser(String phone);
    SysUserEntity getEntity(String userName);
    SysUserSoleEntity getSoleId(String userId);
    boolean updateOpenId(@Param("openId") String openId,@Param("soleId") String soleId);
}