package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_system.admin.vo.SysUserMsgVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * @description:SysUserMsgservice接口
 * @author: daiqimeng
 **/
public interface SysUserMsgService extends IService<SysUserMsgEntity> {
    PageEntity queryPage(SysUserMsgVo sysUserMsgVo);

    //新增历史
    boolean saveHistory(SysUserMsgEntity sysUserMsgEntity);

    //查询某个用户的历史分页查询
    PageEntity queryHistory(SysUserMsgVo sysUserMsgVo);

    //更新数据标识
    boolean  updateStatus(String id);

    SysUserMsgEntity getInfo(String id);

    boolean updateStatus(Integer busCode, String msgAccessory);


}