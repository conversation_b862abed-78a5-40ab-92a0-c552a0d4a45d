package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
* @description: SysLogVo
* @author:      daiqimeng
**/
@ApiModel(description = "系统日志表-Vo类")
@Data
public class SysLogVo extends ParentDto {

    @ApiModelProperty(notes = "公司Id")
    private String companyId;

    @ApiModelProperty(notes = "开始时间")
    @DateTimeFormat(pattern = "YYYY-MM-dd")
    private Date startTime;

    @ApiModelProperty(notes = "结束时间")
    @DateTimeFormat(pattern = "YYYY-MM-dd")
    private Date endTime;

}