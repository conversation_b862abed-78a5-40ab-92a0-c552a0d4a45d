package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysJurisdictionEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysJurisdictionService;
import com.junl.crm_system.admin.vo.SysJurisdictionVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.annotation.Log;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysJurisdiction")
@Api(tags = "权限表接口")
@Log("权限模块")
public class SysJurisdictionController extends ParentController {

    @Autowired
    private SysJurisdictionService sysJurisdictionService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "权限表表--分页列表查询",notes="权限表表--分页列表查询")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysJurisdictionEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result pageList(@RequestBody SysJurisdictionVo sysJurisdictionVo){
        sysJurisdictionVo.setCompanyId(getCompanyId());
        PageEntity page = sysJurisdictionService.queryPage(sysJurisdictionVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation("查询该公司所有权限")
    @PostMapping("/getList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysJurisdictionEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getList(){
        List<SysJurisdictionEntity> list = sysJurisdictionService.list(new LambdaQueryWrapper<SysJurisdictionEntity>().and(x->x.eq(SysJurisdictionEntity::getCompanyId,getCompanyId()).eq(SysJurisdictionEntity::getDeleteFlag, Opposite.ZERO)));
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "权限表查询详情",notes="权限表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "jurisdictionId", value = "jurisdictionId",required=true))
    @GetMapping("/info/{jurisdictionId}")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysJurisdictionEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result info(@PathVariable("jurisdictionId") String jurisdictionId){
        return Result.success(sysJurisdictionService.getInfo(jurisdictionId));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "权限表列表--新增", notes = "权限表列表--新增")
    @PostMapping("/save")
    @Log(type = LogType.INSERT)
    public Result save(@RequestBody SysJurisdictionEntity sysJurisdictionEntity){
        sysJurisdictionEntity.setJurisdictionId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysJurisdictionEntity,null));
        RuleTools.setField(sysJurisdictionEntity,getSysUserEntity(),true);
        sysJurisdictionService.insert(sysJurisdictionEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "权限表--修改", notes = "权限表--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysJurisdictionEntity sysJurisdictionEntity){
        Validate.startValidate(new NotNullVerification(sysJurisdictionEntity,null));
        RuleTools.setField(sysJurisdictionEntity,getSysUserEntity(),false);
        sysJurisdictionService.update(sysJurisdictionEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */


    @ApiOperation(value = "权限表--删除", notes = "权限表--删除")
    @PostMapping("/delete")
    @Log(type = LogType.DELETE)
    public Result delete(@RequestBody String[] jurisdictionIds){
        sysJurisdictionService.delete(Arrays.asList(jurisdictionIds));

        return Result.success();
    }
}