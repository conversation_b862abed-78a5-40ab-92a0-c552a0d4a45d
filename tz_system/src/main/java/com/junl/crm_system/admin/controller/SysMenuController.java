package com.junl.crm_system.admin.controller;

import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.status.LogType;
import com.junl.crm_system.admin.service.SysMenuService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.annotation.Log;
import com.junl.crm_common.status.IdentityStatus;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysMenu")
@Api(tags = "菜单权限表接口")
@Log("菜单模块")
public class SysMenuController extends ParentController {

    @Autowired
    private SysMenuService sysMenuService;

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "菜单权限表查询详情",notes="菜单权限表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "menuId", value = "menuId",required=true))
    @GetMapping("/info/{menuId}")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysMenuEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result info(@PathVariable("menuId") String menuId){

        return Result.success(sysMenuService.getById(menuId));
    }


    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "菜单权限表--修改", notes = "菜单权限表--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysMenuEntity sysMenuEntity){
        sysMenuService.updateById(sysMenuEntity);
        return Result.success();
    }


    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "菜单权限表--删除", notes = "菜单权限表--删除")
    @PostMapping("/delete")
    @Log(type = LogType.DELETE)
    public Result delete(@RequestBody Long[] menuIds){
        sysMenuService.removeByIds(Arrays.asList(menuIds));
        return Result.success();
    }


    @ApiOperation("根据模板菜单获取所有菜单列表")
    @GetMapping("/getMenus")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysMenuEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getMenus(){
        List<SysMenuEntity> menus = sysMenuService.getMenus(IdentityStatus.GOD.getName());
        return Result.success(menus);
    }

    @ApiOperation("根据公司id 获取对应用户所在在公司的所有菜单")
    @GetMapping("/getCompanyMenus")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysMenuEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getCompanyMenus(){
        List<SysMenuEntity> menus = sysMenuService.getMenus(getCompanyId());
        return Result.success(menus);
    }

}