package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @description: SysRoleVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "角色表表-Vo类")
@Data
public class SysRoleVo extends ParentDto {
    @ApiModelProperty(notes = "角色名称", allowEmptyValue = true, required = false)
    private String roleName;
    @ApiModelProperty(notes = "公司id")
    private String companyId;


}