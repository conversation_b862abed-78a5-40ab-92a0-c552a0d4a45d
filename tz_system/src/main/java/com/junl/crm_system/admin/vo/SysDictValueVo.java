package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SysDictValueVo
* @author:      daiqimeng
**/
@ApiModel(description = "字典映射表 (值表)表-Vo类")
@Data
public class SysDictValueVo extends ParentDto {
    @ApiModelProperty(notes = "主表id")
    private String dictId;

}