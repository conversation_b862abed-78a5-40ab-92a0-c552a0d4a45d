package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_system.admin.dao.SysMenuConnectJurisDao;
import com.junl.crm_system.admin.vo.SysMenuConnectJurisVo;
import com.junl.crm_common.pojo.admin.SysMenuConnectJurisEntity;
import com.junl.crm_system.admin.service.SysMenuConnectJurisService;
import org.springframework.stereotype.Service;

/**
* @author: daiqimeng
* @create: 2020-07-21 11:11
**/
@Service
public class SysMenuConnectJurisServiceImpl extends ServiceImpl<SysMenuConnectJurisDao, SysMenuConnectJurisEntity> implements SysMenuConnectJurisService {
/**
* @description: 分页查询列表
* @param sysMenuConnectJurisVo
*/
@Override
public PageEntity queryPage(SysMenuConnectJurisVo sysMenuConnectJurisVo) {
    PageUtils.execute(sysMenuConnectJurisVo);
    List<SysMenuConnectJurisEntity> list = baseMapper.queryList(sysMenuConnectJurisVo);
    return PageUtils.getData(list);
  }
}