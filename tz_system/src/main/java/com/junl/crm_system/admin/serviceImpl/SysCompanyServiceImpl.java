package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.*;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.MenuType;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.*;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

import com.junl.crm_system.admin.dao.SysCompanyDao;
import com.junl.crm_system.admin.service.*;
import com.junl.crm_system.admin.vo.SysCompanyVo;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_system.util.BusinessUtil;
import com.junl.crm_system.validate.MenuValidate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysCompanyServiceImpl extends ServiceImpl<SysCompanyDao, SysCompanyEntity> implements SysCompanyService {


    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private SysDeptService sysDeptService;

    @Autowired
    private SysDictService sysDictService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SysDictValueService sysDictValueService;

    @Autowired
    private SysUserSoleService sysUserSoleService;




    /**
     * @description: 分页查询列表
     * @param sysCompanyVo
     */
    @Override
    public PageEntity queryPage(SysCompanyVo sysCompanyVo) {
        PageUtils.execute(sysCompanyVo);
        List<SysCompanyEntity> list = baseMapper.queryList(sysCompanyVo);
        return PageUtils.getData(list);
    }

    @Override
    @Transactional
    public Boolean saveParentCompany(SysCompanyEntity sysCompanyEntity) {

        //初始化新公司负责人
        SysUserEntity boss = sysCompanyEntity.getBoss();

        String phone = boss.getPhone();
        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>().eq(SysUserEntity::getPhone,phone))>0) {
            throw new RuntimeException("管理员手机号码已被注册,请修改.");
        }

        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>().eq(SysUserEntity::getUserName,boss.getUserName()))>0) {
            throw new RuntimeException("管理员账户已被注册,请重新修改");
        }

        if(baseMapper.selectCount(new LambdaQueryWrapper<SysCompanyEntity>().eq(SysCompanyEntity::getCompanyName,sysCompanyEntity.getCompanyName()))>0){
            throw new RuntimeException("公司名称已被注册,请修改公司名称后提交");
        }

        //初始化管理员信息
        String md5Str = CipherUtils.enCoderMd5(boss.getPassword());
        boss.setUserId(SnowFlake.getUUId());
        boss.setCompanyId(sysCompanyEntity.getCompanyId());
        boss.setDeptId(IdentityStatus.GOD.getName());
        boss.setPositionId(IdentityStatus.GOD.getName());
        boss.setRoleId(IdentityStatus.GOD.getName());
        boss.setIdentityCode(IdentityStatus.ADMIN.getCode());
        boss.setPassword(md5Str);
        boss.setCreateBy(IdentityStatus.GOD.getName());
        sysUserService.save(boss);

        //初始化用户唯一标识
        SysUserSoleEntity sysUserSoleEntity=new SysUserSoleEntity();
        sysUserSoleEntity.setUserId(boss.getUserId());
        sysUserSoleEntity.setId(SnowFlake.getUUId());
        sysUserSoleEntity.setSoleId(SnowFlake.getUUId());
        sysUserSoleService.save(sysUserSoleEntity);


        //新增公司信息
        sysCompanyEntity.setCreateBy(IdentityStatus.GOD.getName());
        sysCompanyEntity.setPrincipal(boss.getUserId());
        baseMapper.insert(sysCompanyEntity);

        redisUtils.set(RedisKey.COMPANY.getName()+sysCompanyEntity.getCompanyId(), sysCompanyEntity.getCompanyName());

        //初始化公司菜单
        List<SysMenuEntity> menus = sysMenuService.list(new QueryWrapper<SysMenuEntity>().lambda().eq(SysMenuEntity::getCompanyId, IdentityStatus.GOD.getName()));
        menus=sysMenuService.sort(menus);
        for (SysMenuEntity x : menus) {
            String uuId = SnowFlake.getUUId();
            String companyId = sysCompanyEntity.getCompanyId();
            x.setMenuId(uuId);
            x.setCompanyId(companyId);
            update(x.getChildMenu(),uuId,companyId);
        }

        sysMenuService.saveBatch(menus);
        for (SysMenuEntity sysMenuEntity : menus) {
            save(sysMenuEntity.getChildMenu());
        }

        //初始化公司字典
        List<SysDictEntity> dicts = sysDictService.queryDictAll(IdentityStatus.GOD.getName());
        dicts.forEach(x->{
            String uuId = SnowFlake.getUUId();
            x.setDictId(uuId);
            x.setCompanyId(sysCompanyEntity.getCompanyId());
            sysDictService.save(x);
            List<SysDictValueEntity> values = x.getValues();
            if (Assert.notNullCollect(values)) {
                values.forEach(e->{
                    e.setDictValueId(SnowFlake.getUUId());
                    e.setDictId(uuId);
                });
                sysDictValueService.saveBatch(values);
            }
        });
        return true;
    }


    @Override
    public SysCompanyEntity getAllOrganization(String companyId) {
        //查询公司信息
        SysCompanyEntity sysCompanyEntity = baseMapper.selectById(companyId);
        Assert.notNull(sysCompanyEntity,"该公司不存在");

        //查询公司负责人
        SysUserEntity userEntity = sysUserService.getOne(new LambdaQueryWrapper<SysUserEntity>().and(x -> x.eq(SysUserEntity::getCompanyId, companyId)
                .eq(SysUserEntity::getDeptId, IdentityStatus.GOD.getName()).eq(SysUserEntity::getPositionId, IdentityStatus.GOD.getName())
                .eq(SysUserEntity::getRoleId, IdentityStatus.GOD.getName())));

        sysCompanyEntity.setBoss(userEntity);
        //查询公司下无部门人员
        sysCompanyEntity.setUsers(sysUserService.getUsers(companyId));
        //查询部门岗位人员信息
        sysCompanyEntity.setDept(sysDeptService.getAllDept(companyId));

        return sysCompanyEntity;
    }

    @Override
    public List<SysCompanyEntity> getUserCompanyAll(SysCompanyVo sysCompanyVo) {

        return baseMapper.getUserCompanyAll(sysCompanyVo);
    }

    @Override
    @Transactional
    public boolean saveMenu(SysMenuEntity sysMenuEntity) {
        //菜单的公共校验
        Validate.startValidate(new MenuValidate(sysMenuEntity,sysMenuService));
        sysMenuService.save(sysMenuEntity);
        //判断子级菜单是否为null  不为null表示这是一个菜单 把菜单下的按钮 一并插入
        List<SysMenuEntity> childMenu = sysMenuEntity.getChildMenu();
        if (Assert.notNullCollect(childMenu)) {
            sysMenuService.saveBatch(childMenu);
        }

        List<SysCompanyEntity> sysCompanyEntities = baseMapper.selectList(new QueryWrapper<SysCompanyEntity>().select("company_id"));
        //查询所有公司的id
        List<String> ids = CommonUtil.collect(sysCompanyEntities, SysCompanyEntity::getCompanyId);
        //给所有公司新增该菜单

        String parentId = sysMenuEntity.getParentId();
        ids.forEach(x->{
            //更换公司id
            sysMenuEntity.setCompanyId(x);
            //更换菜单id
            sysMenuEntity.setMenuId(SnowFlake.getUUId());
            //如何有父级目录 找到对应公司的上级目录是谁
            if (!parentId.equals(IdentityStatus.GOD.getName())) {
                //查询出模板菜单的父级菜单
                SysMenuEntity menuEntity = sysMenuService.getOne(new QueryWrapper<SysMenuEntity>().select( "path", "menu_type").lambda().eq(SysMenuEntity::getMenuId,sysMenuEntity.getParentId()));
                //根据path和菜单类型的唯一性 锁定该公司的对应菜单id
                SysMenuEntity menu_id = sysMenuService.getOne(new QueryWrapper<SysMenuEntity>().select("menu_id").lambda().and(e -> {
                    e.eq(SysMenuEntity::getPath,menuEntity.getPath()).eq(SysMenuEntity::getMenuType,menuEntity.getMenuType()).eq(SysMenuEntity::getCompanyId,x);
                }));
                //对应的父级id赋值
                sysMenuEntity.setParentId(menu_id.getMenuId());
            }

            sysMenuService.save(sysMenuEntity);
            //如果是个菜单 获取系统默认按钮
//            if(sysMenuEntity.getMenuType().equals(MenuType.MENU.getType())){
//                List<SysMenuEntity> defaultButton = BusinessUtil.getDefaultButton(sysMenuEntity.getMenuId(), sysMenuEntity.getCompanyId());
//                sysMenuService.saveBatch(defaultButton);
//            }

        });

        return true;
    }

    @Override
    @Transactional
    public boolean updateMenu(SysMenuEntity sysMenuEntity) {
        //菜单的公共校验
        Validate.startValidate(new MenuValidate(sysMenuEntity,sysMenuService));
        sysMenuService.updateById(sysMenuEntity);

        if (sysMenuEntity.getMenuType().equals(MenuType.BUTTON)) {
            throw new RuntimeException("按钮禁止修改");
        }


        //查询出所有公司的id
        List<SysCompanyEntity> sysCompanyEntities = baseMapper.selectList(new QueryWrapper<SysCompanyEntity>().select("company_id"));
        //查询所有公司的id
        List<String> ids = CommonUtil.collect(sysCompanyEntities, SysCompanyEntity::getCompanyId);
        ids.forEach(x->{
            //根据path和菜单类型的唯一性 锁定该公司的对应菜单id
            SysMenuEntity temp = sysMenuService.getOne(new QueryWrapper<SysMenuEntity>().lambda().and(e -> {
                e.eq(SysMenuEntity::getPath,sysMenuEntity.getPath())
                        .eq(SysMenuEntity::getMenuType,sysMenuEntity.getMenuType())
                        .eq(SysMenuEntity::getCompanyId,x);
            }));

            //更换修改的信息 (id 公司id  和菜单类型不可改变)
            temp.setMenuName(sysMenuEntity.getMenuName());
            temp.setVisible(sysMenuEntity.getVisible());
            temp.setOrderNum(sysMenuEntity.getOrderNum());
            temp.setPath(sysMenuEntity.getPath());
            temp.setComponent(sysMenuEntity.getComponent());
            temp.setIcon(sysMenuEntity.getIcon());
            temp.setIsFrame(sysMenuEntity.getIsFrame());
            temp.setUpdateBy(sysMenuEntity.getUpdateBy());
            temp.setStatus(sysMenuEntity.getStatus());
            sysMenuService.updateById(temp);
        });

        return true;
    }

    @Override
    @Transactional
    public boolean deleteMenu(String id) {
        SysMenuEntity byId = sysMenuService.getById(id);
        Assert.notNull(byId,"该主键的信息不存在");
        sysMenuService.removeById(id);

        //递归删除所有子级目录
        List<SysMenuEntity> menu_id = sysMenuService.list(new QueryWrapper<SysMenuEntity>().select("menu_id").lambda().eq(SysMenuEntity::getParentId, byId.getMenuId()));
        deleteMenu(CommonUtil.collect(menu_id,SysMenuEntity::getMenuId));

        //查询出所有公司的id
        List<SysCompanyEntity> sysCompanyEntities = baseMapper.selectList(new QueryWrapper<SysCompanyEntity>().select("company_id"));
        //查询所有公司的id
        List<String> ids = CommonUtil.collect(sysCompanyEntities, SysCompanyEntity::getCompanyId);

        ids.forEach(x->{
            //查询对应公司的相同目录
            SysMenuEntity temp = sysMenuService.getOne(new QueryWrapper<SysMenuEntity>().select("menu_id").lambda().and(e -> {
                e.eq(SysMenuEntity::getPath,byId.getPath()).eq(SysMenuEntity::getMenuType,byId.getMenuType()).eq(SysMenuEntity::getCompanyId,x);
            }));
            sysMenuService.removeById(temp.getMenuId());

            //递归删除所有子级目录
            List<SysMenuEntity>  childId = sysMenuService.list(new QueryWrapper<SysMenuEntity>().select("menu_id").lambda().eq(SysMenuEntity::getParentId, temp.getMenuId()));
            deleteMenu(CommonUtil.collect(childId,SysMenuEntity::getMenuId));
        });
        return true;
    }

    //递归删除目录
    private void deleteMenu(List<String> ids){
        if (Assert.notNullCollect(ids)) {
            ids.forEach(x->{
                sysMenuService.removeById(x);
                //递归删除所有子级目录
                List<SysMenuEntity>  childId = sysMenuService.list(new QueryWrapper<SysMenuEntity>().select("menu_id").lambda().eq(SysMenuEntity::getParentId, x));
                deleteMenu(CommonUtil.collect(childId,SysMenuEntity::getMenuId));
            });
        }
    }

    /**
     * 递归更新子菜单信息
     * @param list
     * @param parentId
     * @param companyId
     */
    private void update(List<SysMenuEntity> list,String parentId,String companyId){
        if (Assert.notNullCollect(list)) {
            for (SysMenuEntity sysMenuEntity : list) {
                String uuId = SnowFlake.getUUId();
                sysMenuEntity.setMenuId(uuId);
                sysMenuEntity.setParentId(parentId);
                sysMenuEntity.setCompanyId(companyId);
                update(sysMenuEntity.getChildMenu(),uuId,companyId);
            }
        }

    }

    /**
     * 递归新增子菜单
     * @param list
     */
    private void save(List<SysMenuEntity> list){
        if (Assert.notNullCollect(list)) {
            sysMenuService.saveBatch(list);
            for (SysMenuEntity sysMenuEntity : list) {
                save(sysMenuEntity.getChildMenu());
            }
        }
    }
}