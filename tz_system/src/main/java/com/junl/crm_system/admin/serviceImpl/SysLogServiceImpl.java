package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_system.admin.dao.SysLogDao;
import com.junl.crm_system.admin.vo.SysLogVo;
import com.junl.crm_common.pojo.admin.SysLogEntity;
import com.junl.crm_system.admin.service.SysLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogDao, SysLogEntity> implements SysLogService {

    /**
     * @description: 分页查询列表
     * @param sysLogVo
     */
    @Override
    public PageEntity queryPage(SysLogVo sysLogVo) {
        PageUtils.execute(sysLogVo);
        List<SysLogEntity> list = baseMapper.queryList(sysLogVo);
        return PageUtils.getData(list);
    }

    @Override
    public SysLogEntity getInfo(String logId) {
        return baseMapper.getInfo(logId);
    }

}