package com.junl.crm_system.admin.controller;

import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysDictValueService;
import com.junl.crm_system.admin.vo.SysDictValueVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.annotation.Log;
import io.swagger.annotations.*;
import org.apache.ibatis.executor.ResultExtractor;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysDictValue")
@Api(tags = "字典映射表 (值表)接口")
@Log("字典子表模块")
public class SysDictValueController extends ParentController {

    @Autowired
    private SysDictValueService sysDictValueService;

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "字典映射表 (值表)列表--新增", notes = "字典映射表 (值表)列表--新增")
    @PostMapping("/save")
    @Log(type = LogType.INSERT)
    public Result save(@RequestBody SysDictValueEntity sysDictValueEntity){
        sysDictValueEntity.setDictValueId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysDictValueEntity,null));
        String userId = getSysUserEntity().getUserId();
        sysDictValueEntity.setCreateBy(userId);
        sysDictValueEntity.setUpdateBy(userId);
        sysDictValueService.save(sysDictValueEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "字典映射表 (值表)--修改", notes = "字典映射表 (值表)--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysDictValueEntity sysDictValueEntity){
        RuleTools.setField(sysDictValueEntity,getSysUserEntity(),false);
        Validate.startValidate(new NotNullVerification(sysDictValueEntity,null));
        sysDictValueService.updateById(sysDictValueEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */


    @ApiOperation(value = "字典映射表 (值表)--删除", notes = "字典映射表 (值表)--删除")
    @PostMapping("/delete")
    @Log(type = LogType.DELETE)
    public Result delete(@RequestBody String[] dictValueIds){
        sysDictValueService.deletes(Arrays.asList(dictValueIds));
        return Result.success();
    }

    @ApiOperation("查询详情")
    @GetMapping("/info/{id}")
    @Log(type = LogType.INSERT)
    public Result getInfo(@PathVariable("id")String id){
        return Result.success(sysDictValueService.getById(id));
    }

    @ApiOperation("新增字典值 （全局 所有公司）")
    @PostMapping("/saveCompanyAll")
    public Result saveCompanyAll(@RequestBody SysDictValueEntity sysDictValueEntity){
        sysDictValueEntity.setDictValueId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysDictValueEntity,null));
        String userId = getSysUserEntity().getUserId();
        sysDictValueEntity.setCreateBy(userId);
        sysDictValueEntity.setUpdateBy(userId);
        sysDictValueService.saveCompanyAll(sysDictValueEntity);
        return Result.success();
    }

    @ApiOperation("修改字典值 （全局 所有公司）")
    @PostMapping("/updateCompanyAll")
    public Result updateCompanyAll(@RequestBody SysDictValueEntity sysDictValueEntity){
        sysDictValueEntity.setDictValueId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysDictValueEntity,null));
        sysDictValueEntity.setUpdateBy(getSysUserEntity().getUserId());
        sysDictValueService.updateCompanyAll(sysDictValueEntity);
        return Result.success();
    }


    @ApiOperation("删除字典值 （全局 所有公司）")
    @GetMapping("/deleteCompanyAll/{id}")
    public Result deleteCompanyAll(@PathVariable("id") String id){
        sysDictValueService.deleteCompanyAll(id);
        return Result.success();
    }
}