package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @description: SysDeptVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "部门表表-Vo类")
@Data
public class SysDeptVo extends ParentDto {
    @ApiModelProperty(notes = "部门名称", allowEmptyValue = true, required = false)
    private String deptName;
    @ApiModelProperty(notes = "负责人", allowEmptyValue = true, required = false)
    private String principal;

    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty(notes ="父类id" )
    private String parentId;

    @ApiModelProperty(notes ="新部门id" )
    private String newDeptId;

    @ApiModelProperty(notes = "原部门id")
    private String deptId;
}