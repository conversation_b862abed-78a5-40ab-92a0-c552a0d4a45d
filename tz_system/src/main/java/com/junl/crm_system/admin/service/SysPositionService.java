package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.admin.SysPositionEntity;
import com.junl.crm_system.admin.vo.SysPositionVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SysPositionservice接口
* @author: daiqimeng
**/
public interface SysPositionService extends IService<SysPositionEntity> {
    PageEntity queryPage(SysPositionVo sysPositionVo);

    //包含岗位人员
    List<SysPositionEntity> getPositionAll(String deptId);
    //不包含岗位人员 只返回岗位
    List<SysPositionEntity> getPosition(String deptId);

    boolean deleteByIds(List<String> ids);


}