package com.junl.crm_system.admin.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SysLoginVo {

    @ApiModelProperty(notes = "账户名称")
    private String userName;
    @ApiModelProperty(notes = "手机号码")
    private String phone;
    @ApiModelProperty(notes = "验证码")
    private String code;
    @ApiModelProperty(notes = "密码")
    private String password;
    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty(notes = "登录方式  0账户密码登录  1手机号登录 100 系统管理员登录")
    private Integer status;
}
