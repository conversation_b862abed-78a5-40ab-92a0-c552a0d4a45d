package com.junl.crm_system.admin.serviceImpl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysMenuConnectJurisEntity;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.dao.SysJurisdictionDao;
import com.junl.crm_system.admin.service.SysMenuConnectJurisService;
import com.junl.crm_system.admin.service.SysMenuService;
import com.junl.crm_system.admin.vo.SysJurisdictionVo;
import com.junl.crm_common.pojo.admin.SysJurisdictionEntity;
import com.junl.crm_system.admin.service.SysJurisdictionService;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_system.exception.RoleException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysJurisdictionServiceImpl extends ServiceImpl<SysJurisdictionDao, SysJurisdictionEntity> implements SysJurisdictionService {

    @Autowired
    private SysMenuConnectJurisService sysMenuConnectJurisService;

    @Autowired
    private SysMenuService sysMenuService;



    /**
     * @description: 分页查询列表
     * @param sysJurisdictionVo
     */
    @Override
    public PageEntity queryPage(SysJurisdictionVo sysJurisdictionVo) {
        PageUtils.execute(sysJurisdictionVo);
        List<SysJurisdictionEntity> list = baseMapper.queryList(sysJurisdictionVo);
        return PageUtils.getData(list);
    }

    @Override
    @Transactional
    public boolean insert(SysJurisdictionEntity sysJurisdictionEntity) {
        //该权限不存在时才新增
        if (!Assert.notNull(baseMapper.selectById(sysJurisdictionEntity.getJurisdictionId()))){
            baseMapper.insert(sysJurisdictionEntity);
        }
        /**
         * 如果不是最高权限标识才操作 最高权限标识可以获取全部菜单 一般用*表示
         */
        if(!IdentityStatus.GOD.getName().equals(sysJurisdictionEntity.getJurisdictionFlag())){
            List<SysMenuConnectJurisEntity> list=new ArrayList<>();
            List<SysMenuEntity> menus = sysJurisdictionEntity.getMenus();
            for (SysMenuEntity menu : menus) {
                //只对显示的菜单做操作
                if (menu.getVisible().equals(Opposite.ZERO)) {
                    SysMenuConnectJurisEntity jurisEntity=new SysMenuConnectJurisEntity();
                    jurisEntity.setId(SnowFlake.getUUId());
                    jurisEntity.setJurisdictionId(sysJurisdictionEntity.getJurisdictionId());
                    jurisEntity.setMenuId(menu.getMenuId());
                    list.add(jurisEntity);
                    insertMenuAndJuris(menu.getChildMenu(),sysJurisdictionEntity.getJurisdictionId(),list);
                }
            }
            sysMenuConnectJurisService.saveBatch(list);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean update(SysJurisdictionEntity sysJurisdictionEntity) {
        String jurisdictionId = sysJurisdictionEntity.getJurisdictionId();
        sysMenuConnectJurisService.remove(new LambdaQueryWrapper<SysMenuConnectJurisEntity>().eq(SysMenuConnectJurisEntity::getJurisdictionId,jurisdictionId));
        baseMapper.updateById(sysJurisdictionEntity);
        insert(sysJurisdictionEntity);
        return true;
    }

    @Override
    @Transactional
    public boolean delete(List<String> ids) {
        baseMapper.delete(ids);
        return true;
    }

    @Override
    public SysJurisdictionEntity getInfo(String id) {
        SysJurisdictionEntity byId = baseMapper.selectById(id);
        Assert.notNull(byId,"查询不到该权限信息");
        //获取所有菜单
        List<SysMenuEntity> list=sysMenuService.getMenus(byId.getCompanyId());
        //不是最高权限再处理数据 主要把没有权限的状态按钮置为1
        if(!byId.getJurisdictionFlag().equals(IdentityStatus.GOD.getName())){
            List<String> menuIds = getMenuIds(byId.getJurisdictionId());
            recursion(list, menuIds.stream().collect(Collectors.toSet()));
        }

        byId.setMenus(list);
        return byId;
    }

    /**
     * @describe:  递归 把不该显示的menuId的值 改为不显示
     * <AUTHOR>
     * @date 2021/6/2 15:36
     * @param menuEntities
     * @param menuIds
     */
    private void recursion(List<SysMenuEntity> menuEntities, Set<String> menuIds){
        if(Assert.notNullCollect(menuEntities)){
            for (SysMenuEntity menuEntity : menuEntities) {
                if (!menuIds.contains(menuEntity.getMenuId())) {
                    menuEntity.setVisible(Opposite.SINGLE);
                }
                recursion(menuEntity.getChildMenu(),menuIds);
            }
        }
    }


    /**
     * @describe:  递归获取该权限关联的菜单
     * <AUTHOR>
     * @date 2021/5/31 11:46
     * @param menus
     * @param jurisId
     * @param list
     */
    private void insertMenuAndJuris(List<SysMenuEntity> menus,String jurisId,List<SysMenuConnectJurisEntity> list){
        if(Assert.notNullCollect(menus)){
            for (SysMenuEntity menu : menus) {
                //等于0 表示该权限可以显示
                if (menu.getVisible().equals(Opposite.ZERO)) {
                    SysMenuConnectJurisEntity jurisEntity=new SysMenuConnectJurisEntity();
                    jurisEntity.setId(SnowFlake.getUUId());
                    jurisEntity.setJurisdictionId(jurisId);
                    jurisEntity.setMenuId(menu.getMenuId());
                    list.add(jurisEntity);
                    insertMenuAndJuris(menu.getChildMenu(),jurisId,list);
                }
            }
        }

    }

    /**
     * @describe: 根据权限查询所有菜单id 全部权限抛出roleException异常
     * <AUTHOR>
     * @date 2021/5/31 13:54
     * @param jurisdictionId
     * @return: {@link List< String>}
     */
    public List<String> getMenuIds(String jurisdictionId){
        //根据权限id 查询权限信息
        SysJurisdictionEntity jurisdictionEntity = baseMapper.selectById(jurisdictionId);
        //代表顶级权限 需要查询该公司的所有菜单 抛给调用方处理
        if(jurisdictionEntity.getJurisdictionFlag().equals(IdentityStatus.GOD.getName())){
            throw new RoleException();
        }
        //根据权限获取所有菜单id
        List<SysMenuConnectJurisEntity> menu_id = sysMenuConnectJurisService.list(new QueryWrapper<SysMenuConnectJurisEntity>().select("menu_id").lambda().eq(SysMenuConnectJurisEntity::getJurisdictionId, jurisdictionId));
        List<String> collect = CommonUtil.collect(menu_id, SysMenuConnectJurisEntity::getMenuId);
        return collect;
    }
}