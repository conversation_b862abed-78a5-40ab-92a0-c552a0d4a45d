package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_system.admin.vo.SysCompanyVo;

import java.util.List;
/**
 * @description: sys_companydao接口
 * @author: daiqimeng
 **/

public interface SysCompanyDao extends BaseMapper<SysCompanyEntity> {
    List<SysCompanyEntity> queryList(SysCompanyVo sysCompanyVo);
    List<SysCompanyEntity> getUserCompanyAll(SysCompanyVo sysCompanyVo);
}