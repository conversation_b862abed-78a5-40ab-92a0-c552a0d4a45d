package com.junl.crm_system.admin.controller;

import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysNoticeWriteEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysNoticeWriteService;
import com.junl.crm_system.admin.vo.SysNoticeWriteVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysNoticeWrite")
@Api(tags = "公告写表(读写表分离)接口")
public class SysNoticeWriteController extends ParentController {

    @Autowired
    private SysNoticeWriteService sysNoticeWriteService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "公告写表(读写表分离)表--分页列表查询",notes="公告写表(读写表分离)表--分页列表查询")
    @PostMapping("/pageList")
    public Result pageList(@RequestBody SysNoticeWriteVo sysNoticeWriteVo){
        sysNoticeWriteVo.setCompanyId(getCompanyId());
        PageEntity page = sysNoticeWriteService.queryPage(sysNoticeWriteVo);
        return Result.success(page);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */
    @ApiOperation(value = "公告写表(读写表分离)查询详情",notes="公告写表(读写表分离)查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "noticeId", value = "noticeId",required=true))
    @GetMapping("/info/{noticeId}")
    public Result info(@PathVariable("noticeId") String noticeId){
        return Result.success(sysNoticeWriteService.getById(noticeId));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "公告写表(读写表分离)列表--新增", notes = "公告写表(读写表分离)列表--新增")
    @PostMapping("/save")
    public Result save(@RequestBody SysNoticeWriteEntity sysNoticeWriteEntity){
        sysNoticeWriteEntity.setNoticeId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysNoticeWriteEntity,null));
        RuleTools.setField(sysNoticeWriteEntity,getSysUserEntity(),true);
        sysNoticeWriteService.save(sysNoticeWriteEntity);
        if(sysNoticeWriteEntity.getStatus().toString().equals(Opposite.SINGLE)){
            sysNoticeWriteService.issue(sysNoticeWriteEntity.getNoticeId(),getCompanyId());
        }
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "公告写表(读写表分离)--修改", notes = "公告写表(读写表分离)--修改")
    @PostMapping("/update")
    public Result update(@RequestBody  SysNoticeWriteEntity sysNoticeWriteEntity){
        Validate.startValidate(new NotNullVerification(sysNoticeWriteEntity,null));
        RuleTools.setField(sysNoticeWriteEntity,getSysUserEntity(),false);
        sysNoticeWriteService.updateById(sysNoticeWriteEntity);
        if(sysNoticeWriteEntity.getStatus().toString().equals(Opposite.SINGLE)){
            sysNoticeWriteService.issue(sysNoticeWriteEntity.getNoticeId(),getCompanyId());
        }
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "公告写表(读写表分离)--删除", notes = "公告写表(读写表分离)--删除")
    @PostMapping("/delete")
    public Result delete(@RequestBody String[] noticeIds){
        sysNoticeWriteService.removeByIds(Arrays.asList(noticeIds));
        return Result.success();
    }

    @ApiOperation("公告发布")
    @GetMapping("/issue/{id}")
    private Result issue(@PathVariable("id")String id){
        sysNoticeWriteService.issue(id,getCompanyId());
        return Result.success();
    }
}