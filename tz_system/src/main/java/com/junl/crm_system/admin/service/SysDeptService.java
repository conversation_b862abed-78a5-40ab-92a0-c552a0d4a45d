package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.admin.SysDeptEntity;
import com.junl.crm_system.admin.vo.SysDeptVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
* @description:SysDeptservice接口
* @author: daiqimeng
**/
public interface SysDeptService extends IService<SysDeptEntity> {
    PageEntity queryPage(SysDeptVo sysDeptVo);
    boolean saveDept(SysDeptEntity sysDeptEntity);
    boolean delete(List<String> ids);
    boolean updateDept(SysDeptEntity sysDeptEntity);
    //查询公司所有部门和岗位及相关人员
    List<SysDeptEntity> getAllDept(String companyId);
    //查询公司所有部门 不包含人员信息
    List<SysDeptEntity> getAll(String companyId);
    boolean relieveGuard(SysDeptVo sysDeptVo);



}