package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysDeptEntity;
import com.junl.crm_system.admin.vo.SysDeptVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
* @description: sys_deptdao接口
* @author: daiqimeng
**/

public interface SysDeptDao extends BaseMapper<SysDeptEntity> {
    List<SysDeptEntity> queryList(SysDeptVo sysDeptVo);
    boolean delete(@Param("ids")List<String> ids);
    List<SysDeptEntity> getAllDept(@Param("companyId") String companyId,@Param("parentId") String parentId,@Param("id")String id);
    

}