package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysPositionEntity;
import com.junl.crm_system.admin.vo.SysPositionVo;

import java.util.List;
/**
* @description: sys_positiondao接口
* @author: daiqimeng
**/

public interface SysPositionDao extends BaseMapper<SysPositionEntity> {
    List<SysPositionEntity> queryList(SysPositionVo sysPositionVo);
    List<SysPositionEntity> getPositionAll(String deptId);
}