package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysDictEntity;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_system.admin.vo.SysDictVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
* @description:SysDictservice接口
* @author: daiqimeng
**/
public interface SysDictService extends IService<SysDictEntity> {
    PageEntity queryPage(SysDictVo sysDictVo);
    PageEntity getValues(SysDictVo sysDictVo);

    boolean saveCompanyAll(SysDictEntity sysDictEntity);

    boolean updateCompanyAll(SysDictEntity sysDictEntity);

    boolean deleteCompanyAll(String id);

    List<SysDictEntity> queryDictAll(String companyId);
}