package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysNoticeWriteEntity;
import com.junl.crm_system.admin.vo.SysNoticeWriteVo;
import com.junl.crm_common.common.PageEntity;
import org.springframework.web.bind.annotation.PathVariable;

/**
* @description:SysNoticeWriteservice接口
* @author: daiqimeng
**/
public interface SysNoticeWriteService extends IService<SysNoticeWriteEntity> {
    PageEntity queryPage(SysNoticeWriteVo sysNoticeWriteVo);
    /** 公司发布公告 */
    boolean issue(String id,String companyId);
    /** 管理端发布公告 */
    boolean issueManagement(String id);
}