package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.pojo.admin.SysDataEntity;
import com.junl.crm_system.admin.vo.SysDataVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
 * @description:SysDataservice接口
 * @author: daiqimeng
 **/
public interface SysDataService extends IService<SysDataEntity> {
    PageEntity queryPage(SysDataVo sysDataVo);
    boolean saveData(SysDataEntity sysDataEntity);
    boolean updateData(SysDataEntity sysDataEntity);
    boolean removeData(List<String> id);
    SysDataEntity getInfo(String id);
}