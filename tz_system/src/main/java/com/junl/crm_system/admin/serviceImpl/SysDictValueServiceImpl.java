package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysDictEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import java.util.stream.Collectors;

import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.dao.SysDictDao;
import com.junl.crm_system.admin.dao.SysDictValueDao;
import com.junl.crm_system.admin.vo.SysDictValueVo;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_system.admin.service.SysDictValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysDictValueServiceImpl extends ServiceImpl<SysDictValueDao, SysDictValueEntity> implements SysDictValueService {
    /**
     * @description: 分页查询列表
     * @param sysDictValueVo
     */
    @Autowired
    private SysDictDao sysDictDao;


    @Override
    public PageEntity queryPage(SysDictValueVo sysDictValueVo) {
        PageUtils.execute(sysDictValueVo);
        List<SysDictValueEntity> list = baseMapper.queryList(sysDictValueVo);
        return PageUtils.getData(list);
    }

    @Override
    @Transactional
    public boolean deletes(List<String> ids) {
        baseMapper.deletes(ids);
        return true;
    }

    @Override
    @Transactional
    public boolean saveCompanyAll(SysDictValueEntity sysDictValueEntity) {
        baseMapper.insert(sysDictValueEntity);

        //找出是哪个主键字典增加的字典value值
        String dictId = sysDictValueEntity.getDictId();
        SysDictEntity sysDictEntity = sysDictDao.selectById(dictId);
        //根据这个主键字典唯一标识查询所有公司对应的字典主键
        List<SysDictEntity> dict_flag = sysDictDao.selectList(new QueryWrapper<SysDictEntity>().select("dict_id").eq("dict_flag", sysDictEntity.getDictFlag()).ne("dict_id",dictId));
        List<String> ids = CommonUtil.collect(dict_flag, SysDictEntity::getDictId);
        //把该对象的主键和关联主键替换掉  为所有公司新增这个值
        for (String id : ids) {
            sysDictValueEntity.setDictValueId(SnowFlake.getUUId());
            sysDictValueEntity.setDictId(id);
            baseMapper.insert(sysDictValueEntity);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean updateCompanyAll(SysDictValueEntity sysDictValueEntity) {
        String dictName = sysDictValueEntity.getDictName();
        String oldDictName = sysDictValueEntity.getOldDictName();

        baseMapper.updateById(sysDictValueEntity);
        //找出是哪个主键字典更新的字典value值
        String dictId = sysDictValueEntity.getDictId();
        SysDictEntity sysDictEntity = sysDictDao.selectById(dictId);

        //查询所有公司的同级字典主表 (这里不根据老的值直接查询更新是因为怕不同字典下值一样 造成错改)
        List<SysDictEntity> dict_flag = sysDictDao.selectList(new QueryWrapper<SysDictEntity>().select("dict_id").eq("dict_flag", sysDictEntity.getDictFlag()));
        List<String> ids = CommonUtil.collect(dict_flag, SysDictEntity::getDictId);
        //查询出所有需要修改的值
        List<SysDictValueEntity> list = baseMapper.selectList(new LambdaQueryWrapper<SysDictValueEntity>().eq(SysDictValueEntity::getDictName, oldDictName).in(SysDictValueEntity::getDictId,ids));
        list.forEach(x->{
            x.setDictValue(sysDictValueEntity.getDictValue());
            x.setDictName(dictName);
            x.setUpdateBy(sysDictValueEntity.getUpdateBy());
            baseMapper.updateById(x);
        });

        return true;
    }

    @Override
    public boolean deleteCompanyAll(String id) {
        SysDictValueEntity sysDictValueEntity = baseMapper.selectById(id);
        if (Assert.notNull(sysDictValueEntity)) {
            SysDictEntity sysDictEntity = sysDictDao.selectById( sysDictValueEntity.getDictId());
            List<SysDictEntity> dict_flag = sysDictDao.selectList(new QueryWrapper<SysDictEntity>().select("dict_id").eq("dict_flag", sysDictEntity.getDictFlag()));
            List<String> dictIds = CommonUtil.collect(dict_flag, SysDictEntity::getDictId);
            baseMapper.delete(new LambdaQueryWrapper<SysDictValueEntity>()
            .eq(SysDictValueEntity::getDictName,sysDictValueEntity.getDictName())
            .in(SysDictValueEntity::getDictId,dictIds));
        }
        return true;
    }
}