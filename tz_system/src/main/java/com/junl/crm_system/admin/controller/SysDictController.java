package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysDeptEntity;
import com.junl.crm_common.pojo.admin.SysDictEntity;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysDictService;
import com.junl.crm_system.admin.vo.SysDictVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.annotation.Log;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysDict")
@Api(tags = "字典主表接口")
@Log("字典主表模块")
public class SysDictController  extends ParentController {

    @Autowired
    private SysDictService sysDictService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "字典主表表--分页列表查询",notes="字典主表表--分页列表查询")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysDictEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result pageList(@RequestBody SysDictVo sysDictVo){
        sysDictVo.setCompanyId(getCompanyId());
        PageEntity page = sysDictService.queryPage(sysDictVo);
        return Result.success(page);
    }


    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "字典主表列表--新增", notes = "字典主表列表--新增")
    @PostMapping("/save")
    @Log(type = LogType.INSERT)
    public Result save(@RequestBody SysDictEntity sysDictEntity){
        sysDictEntity.setDictId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysDictEntity,null));
        RuleTools.setField(sysDictEntity,getSysUserEntity(),true);
        if (sysDictService.count(new LambdaQueryWrapper<SysDictEntity>().and(x->x.eq(SysDictEntity::getCompanyId,getCompanyId()).eq(SysDictEntity::getDictFlag,sysDictEntity.getDictFlag())))>0) {
            return Result.error("字典唯一标识重复，请重新修改");
        }
        sysDictService.save(sysDictEntity);
        return Result.success();
    }

    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "字典主表--修改", notes = "字典主表--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysDictEntity sysDictEntity){
        RuleTools.setField(sysDictEntity,getSysUserEntity(),false);
        Validate.startValidate(new NotNullVerification(sysDictEntity,null));
        if (sysDictService.count(new LambdaQueryWrapper<SysDictEntity>().and(x->x.eq(SysDictEntity::getCompanyId,getCompanyId()).eq(SysDictEntity::getDictFlag,sysDictEntity.getDictFlag()).ne(SysDictEntity::getDictId,sysDictEntity.getDictId())))>0) {
            return Result.error("字典唯一标识重复，请重新修改");
        }
        sysDictService.updateById(sysDictEntity);
        return Result.success();
    }

    @ApiOperation("根据字典标识获取值列表")
    @PostMapping("/getValues")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysDictValueEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getValues(@RequestBody SysDictVo sysDictVo){
        sysDictVo.setCompanyId(getCompanyId());
        PageEntity values = sysDictService.getValues(sysDictVo);
        return Result.success(values);
    }




    @ApiOperation("查询详情")
    @GetMapping("/info/{id}")
    @Log(type = LogType.INSERT)
    public Result getInfo(@PathVariable("id")String id){
        return Result.success(sysDictService.getById(id));
    }


    @ApiOperation("根据字典标识获取值列表（全局的）")
    @PostMapping("/getCompanyValues")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysDictValueEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getCompanyValues(@RequestBody SysDictVo sysDictVo){
        sysDictVo.setCompanyId(IdentityStatus.GOD.getName());
        PageEntity values = sysDictService.getValues(sysDictVo);
        return Result.success(values);
    }


    @ApiOperation("新增字典(全局的 所有公司)")
    @PostMapping("/saveCompanyAll")
    public Result saveCompanyAll(@RequestBody SysDictEntity sysDictEntity){
        sysDictEntity.setDictId(SnowFlake.getUUId());
        RuleTools.setField(sysDictEntity,getSysUserEntity(),true);
        Validate.startValidate(new NotNullVerification(sysDictEntity,null));
        sysDictService.saveCompanyAll(sysDictEntity);
        return Result.success();
    }

    @ApiOperation("修改字典(全局 所有公司)")
    @PostMapping("/updateCompanyAll")
    public Result updateCompanyAll(@RequestBody SysDictEntity sysDictEntity){
        Validate.startValidate(new NotNullVerification(sysDictEntity,null));
        RuleTools.setField(sysDictEntity,getSysUserEntity(),false);
        sysDictService.updateCompanyAll(sysDictEntity);
        return Result.success();
    }

    @ApiOperation("删除字典(全局 所有公司)")
    @GetMapping("/deleteCompanyAll/{id}")
    public Result deleteCompanyAll(@PathVariable("id")String id){
        sysDictService.deleteCompanyAll(id);
        return Result.success();
    }

    @ApiOperation("全局字典 查询")
    @PostMapping("/queryCompanyAll")
    public Result queryCompanyAll(@RequestBody SysDictVo sysDictVo){
        sysDictVo.setCompanyId(IdentityStatus.GOD.getName());
        PageEntity pageEntity = sysDictService.queryPage(sysDictVo);
        return Result.success(pageEntity);
    }


}