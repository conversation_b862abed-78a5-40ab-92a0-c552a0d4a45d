package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: SysUserMsgVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "公告读表(读写表分离)表-Vo类")
@Data
public class SysUserMsgVo extends ParentDto {
    @ApiModelProperty(notes = "消息标题", allowEmptyValue = true, required = false)
    private  String msgTitle;
    @ApiModelProperty(notes = "公司Id", allowEmptyValue = true, required = false)
    private  String companyId;
    @ApiModelProperty(notes = "创建人", allowEmptyValue = true, required = false)
    private  String createBy;
    @ApiModelProperty(notes = "阅读标识 0否 1是", allowEmptyValue = true, required = false)
    private  String status;
    @ApiModelProperty(notes = "消息类型 暂定（0公告）", allowEmptyValue = true, required = false)
    private  String msgType;

    @ApiModelProperty(notes = "用户id")
    private List<String> userIds;

    @ApiModelProperty("busCode")
    private Integer busCode;

}