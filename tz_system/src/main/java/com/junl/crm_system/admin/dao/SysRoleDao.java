package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysRoleEntity;
import com.junl.crm_system.admin.vo.SysRoleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
* @description: sys_roledao接口
* @author: daiqimeng
**/

public interface SysRoleDao extends BaseMapper<SysRoleEntity> {
    List<SysRoleEntity> queryList(SysRoleVo sysRoleVo);
    boolean delete(@Param("ids") List<String> ids);
}