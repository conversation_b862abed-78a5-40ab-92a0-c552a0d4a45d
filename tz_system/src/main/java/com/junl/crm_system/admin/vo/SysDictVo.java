package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
* @description: SysDictVo
* @author:      daiqimeng
**/
@ApiModel(description = "字典主表表-Vo类")
@Data
public class SysDictVo extends ParentDto {
    @ApiModelProperty(notes = "字典名称", allowEmptyValue = true, required = false)
    private  String dictName;
    @ApiModelProperty(notes = "公司Id", allowEmptyValue = true, required = false)
    private  String companyId;

    @ApiModelProperty(notes = "字典标识")
    private String key;

    @ApiModelProperty(notes = "主表字典id")
    private String dictId;

    @ApiModelProperty(notes = "字典类型")
    private String dictFlag;

}