package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description: SysUserVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "系统用户表-Vo类")
@Data
public class SysUserVo extends ParentDto {
    @ApiModelProperty(notes = "用户名称", allowEmptyValue = true, required = false)
    private  String userName;
    @ApiModelProperty(notes = "公司Id", allowEmptyValue = true, required = false)
    private  String companyId;
    @ApiModelProperty(notes = "手机号码", allowEmptyValue = true, required = false)
    private  String phone;

    @ApiModelProperty(notes = "部门id")
    private String deptId;

    @ApiModelProperty(notes = "账号状态")
    private String status;

    @ApiModelProperty(notes = "开始时间")
    @DateTimeFormat(pattern = "YYYY-MM-dd")
    private Date startTime;

    @ApiModelProperty(notes = "结束时间")
    @DateTimeFormat(pattern = "YYYY-MM-dd")
    private Date endTime;

    @ApiModelProperty(notes = "原密码")
    private String password;

    @ApiModelProperty(notes = "新密码")
    private String newPassword;

    @ApiModelProperty(notes = "用户id")
    private String userId;

    @ApiModelProperty(notes = "用户权限")
    private Integer identityStatus;

}