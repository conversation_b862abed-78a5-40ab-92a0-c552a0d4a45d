package com.junl.crm_system.admin.serviceImpl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysHistoryMsgEntity;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_system.admin.dao.SysHistoryMsgDao;
import com.junl.crm_system.admin.service.SysHistoryMsgService;
import com.junl.crm_system.admin.vo.SysHistoryMsgVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* @author: daiqimeng
* @create: 2020-07-21 11:11
**/
@Service
public class SysHistoryMsgServiceImpl extends ServiceImpl<SysHistoryMsgDao, SysHistoryMsgEntity> implements SysHistoryMsgService {
/**
* @description: 分页查询列表
* @param sysHistoryMsgVo
*/
@Override
public PageEntity queryPage(SysHistoryMsgVo sysHistoryMsgVo) {
    PageUtils.execute(sysHistoryMsgVo);
    List<SysHistoryMsgEntity> list = baseMapper.queryList(sysHistoryMsgVo);
    return PageUtils.getData(list);
  }
}