package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_system.admin.vo.SysDictValueVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
* @description: sys_dict_valuedao接口
* @author: daiqimeng
**/

public interface SysDictValueDao extends BaseMapper<SysDictValueEntity> {
    List<SysDictValueEntity> queryList(SysDictValueVo sysDictValueVo);
    boolean deletes(@Param("ids") List<String> ids);
}