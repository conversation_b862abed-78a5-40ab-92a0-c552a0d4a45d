package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_system.admin.dao.SysDeptDao;
import com.junl.crm_system.admin.service.SysPositionService;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysDeptVo;
import com.junl.crm_common.pojo.admin.SysDeptEntity;
import com.junl.crm_system.admin.service.SysDeptService;
import com.junl.crm_common.status.IdentityStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptDao, SysDeptEntity> implements SysDeptService {


    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysPositionService sysPositionService;

    /**
     * @description: 分页查询列表
     * @param sysDeptVo
     */
    @Override
    public PageEntity queryPage(SysDeptVo sysDeptVo) {
        PageUtils.execute(sysDeptVo);
        sysDeptVo.setParentId(IdentityStatus.GOD.getName());
        List<SysDeptEntity> list = baseMapper.queryList(sysDeptVo);
        recursion(list);
        return PageUtils.getData(list);
    }

    private void recursion(List<SysDeptEntity> list){
        if (Assert.notNullCollect(list)) {
            list.forEach(x->{
                SysDeptVo sysDeptVo=new SysDeptVo();
                sysDeptVo.setParentId(x.getDeptId());
                x.setChildren(baseMapper.queryList(sysDeptVo));
                recursion(x.getChildren());
            });
        }
    }


    @Override
    @Transactional
    public boolean saveDept(SysDeptEntity sysDeptEntity) {
        SysUserEntity byId = sysUserService.getById(sysDeptEntity.getPrincipal());
        Assert.isNull(byId,"该负责人不存在");

        if(Assert.notNull(byId.getDeptId())){
            if (IdentityStatus.GOD.getName().equals(byId.getDeptId())) {
                throw new RuntimeException("公司管理员不能作为部门负责人");
            }

            //不是原来的部门情况下 需要判断该用户是否已经在其他部门
            if (!byId.getDeptId().equals(sysDeptEntity.getDeptId())) {
                Assert.isNull(byId.getDeptId(),"该负责人已在其他部门，请重新选择负责人");
            }
        }

        if (!Assert.notNull(baseMapper.selectById(sysDeptEntity.getDeptId()))) {
            baseMapper.insert(sysDeptEntity);
        }
        byId.setDeptId(sysDeptEntity.getDeptId());
        byId.setIdentityCode(IdentityStatus.DEPT_ADMIN.getCode());
        sysUserService.updateById(byId);
        return true;
    }


    @Override
    public boolean delete(List<String> ids) {
        for (String id : ids) {
            if (baseMapper.selectCount(new LambdaQueryWrapper<SysDeptEntity>().eq(SysDeptEntity::getParentId,id))>0) {
                SysDeptEntity sysDeptEntity = baseMapper.selectById(id);
                throw new RuntimeException(sysDeptEntity.getDeptName()+"该部门下存在其他子级部门，请先删除子级部门");
            }
        }

        if (sysUserService.count(new LambdaQueryWrapper<SysUserEntity>().in(SysUserEntity::getDeptId,ids))>0) {
            return false;
        }
        return baseMapper.delete(ids);
    }

    @Override
    @Transactional
    public boolean updateDept(SysDeptEntity sysDeptEntity) {
        saveDept(sysDeptEntity);
        baseMapper.updateById(sysDeptEntity);
        return true;
    }

    @Override
    public List<SysDeptEntity> getAllDept(String companyId) {
        //查询子部门和当前部门人员信息
        List<SysDeptEntity> allDept = baseMapper.getAllDept(companyId, IdentityStatus.GOD.getName(),null);
        allDept.forEach(x->{
            //查询部门岗位和人员信息
            x.setPosition(sysPositionService.getPositionAll(x.getDeptId()));
            recursion(x.getChildren(),companyId);
        });
        return allDept;
    }

    @Override
    public List<SysDeptEntity> getAll(String companyId) {
        SysDeptVo sysDeptVo=new SysDeptVo();
        sysDeptVo.setCompanyId(companyId);
        sysDeptVo.setParentId(IdentityStatus.GOD.getName());
        List<SysDeptEntity> sysDeptEntities = baseMapper.queryList(sysDeptVo);
        recursion(sysDeptEntities);
        return sysDeptEntities;
    }

    @Override
    @Transactional
    public boolean relieveGuard(SysDeptVo sysDeptVo) {
        List<SysUserEntity> user_id = sysUserService.list(new QueryWrapper<SysUserEntity>().select("user_id"));
        user_id.forEach(x->{
            x.setDeptId(sysDeptVo.getNewDeptId());
        });
        sysUserService.updateBatchById(user_id);
        return true;
    }

    private void recursion(List<SysDeptEntity> allDept,String companyId){
        if(Assert.notNullCollect(allDept)){
            for (SysDeptEntity sysDeptEntity : allDept) {
                //接口复用  根据主键查询外部集合永远是1长度 就是该数据本身  所有赋值孩子节点要取第一个节点的孩子节点
                List<SysDeptEntity> thisDept = baseMapper.getAllDept(companyId, null, sysDeptEntity.getDeptId());
                sysDeptEntity.setChildren(thisDept.get(0).getChildren());
                sysDeptEntity.setUsers(thisDept.get(0).getUsers());
                sysDeptEntity.setPosition(sysPositionService.getPositionAll(sysDeptEntity.getDeptId()));
                recursion(sysDeptEntity.getChildren(),companyId);
            }
        }

    }
}