package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_system.admin.dao.SysIdentityDao;
import com.junl.crm_system.admin.vo.SysIdentityVo;
import com.junl.crm_common.pojo.admin.SysIdentityEntity;
import com.junl.crm_system.admin.service.SysIdentityService;
import org.springframework.stereotype.Service;

/**
* @author: daiqimeng
* @create: 2020-07-21 11:11
**/
@Service
public class SysIdentityServiceImpl extends ServiceImpl<SysIdentityDao, SysIdentityEntity> implements SysIdentityService {
/**
* @description: 分页查询列表
* @param sysIdentityVo
*/
@Override
public PageEntity queryPage(SysIdentityVo sysIdentityVo) {
    PageUtils.execute(sysIdentityVo);
    List<SysIdentityEntity> list = baseMapper.queryList(sysIdentityVo);
    return PageUtils.getData(list);
  }
}