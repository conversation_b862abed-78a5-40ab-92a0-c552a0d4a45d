package com.junl.crm_system.admin.controller;

import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysLogEntity;
import com.junl.crm_system.admin.service.SysLogService;
import com.junl.crm_system.admin.vo.SysLogVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysLog")
@Api(tags = "系统日志接口")
public class SysLogController  extends ParentController {

    @Autowired
    private SysLogService sysLogService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "系统日志表--分页列表查询",notes="系统日志表--分页列表查询")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysLogEntity.class),
            @ApiResponse(code = 500,message = "error")})
    public Result pageList(@RequestBody SysLogVo sysLogVo){
        sysLogVo.setCompanyId(getCompanyId());
        PageEntity page = sysLogService.queryPage(sysLogVo);
        return Result.success(page);
    }


    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "系统日志参数查询")
    @ApiImplicitParams(@ApiImplicitParam(name = "logId", value = "logId",required=true))
    @GetMapping("/info/{logId}")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysLogEntity.class),
            @ApiResponse(code = 500,message = "error")})
    public Result info(@PathVariable("logId") String logId){
        return Result.success(sysLogService.getInfo(logId));
    }



    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */


    @ApiOperation(value = "系统日志--删除", notes = "系统日志--删除")
    @PostMapping("/delete")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysLogEntity.class),
            @ApiResponse(code = 500,message = "error")})
    public Result delete(@RequestBody String[] logIds){
        sysLogService.removeByIds(Arrays.asList(logIds));
        return Result.success();
    }
}