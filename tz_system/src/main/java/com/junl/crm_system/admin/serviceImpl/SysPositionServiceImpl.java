package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_system.admin.dao.SysPositionDao;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.vo.SysPositionVo;
import com.junl.crm_common.pojo.admin.SysPositionEntity;
import com.junl.crm_system.admin.service.SysPositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysPositionServiceImpl extends ServiceImpl<SysPositionDao, SysPositionEntity> implements SysPositionService {


    @Autowired
    private SysUserService sysUserService;
    /**
     * @description: 分页查询列表
     * @param sysPositionVo
     */
    @Override
    public PageEntity queryPage(SysPositionVo sysPositionVo) {
        PageUtils.execute(sysPositionVo);
        List<SysPositionEntity> list = baseMapper.queryList(sysPositionVo);
        return PageUtils.getData(list);
    }

    @Override
    public List<SysPositionEntity> getPositionAll(String deptId) {

        return baseMapper.getPositionAll(deptId);
    }

    @Override
    public List<SysPositionEntity> getPosition(String deptId) {
        List<SysPositionEntity> sysPositionEntities = baseMapper.selectList(new LambdaQueryWrapper<SysPositionEntity>().eq(SysPositionEntity::getDeptId, deptId));
        return sysPositionEntities;
    }

    @Override
    @Transactional
    public boolean deleteByIds(List<String> ids) {
        //查询删除岗位的所有人员 把人员岗位id 全部置为null
        List<SysUserEntity> list = sysUserService.list(new QueryWrapper<SysUserEntity>().select("user_id", "position_id").in("position_id", ids));
        if (Assert.notNullCollect(list)) {
            list.forEach(x->x.setPositionId(null));
            sysUserService.updateBatchById(list);
        }
        baseMapper.deleteBatchIds(ids);
        return true;
    }
}