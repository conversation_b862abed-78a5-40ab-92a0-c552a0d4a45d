package com.junl.crm_system.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.junl.crm_common.pojo.admin.SysJurisdictionEntity;
import com.junl.crm_system.admin.vo.SysJurisdictionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
* @description: sys_jurisdictiondao接口
* @author: daiqimeng
**/

public interface SysJurisdictionDao extends BaseMapper<SysJurisdictionEntity> {
    List<SysJurisdictionEntity> queryList(SysJurisdictionVo sysJurisdictionVo);
    int delete(@Param("ids") List<String> ids);
}