package com.junl.crm_system.admin.controller;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_system.admin.service.SysUserMsgService;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_system.admin.service.SysUserSoleService;
import com.junl.crm_system.admin.vo.SysUserMsgVo;
import com.junl.crm_work.status.BusCode;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysUserMsg")
@Api(tags = "用户消息表")
public class SysUserMsgController  extends ParentController {

    @Autowired
    private SysUserMsgService sysUserMsgService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserSoleService sysUserSoleService;


    @ApiOperation("获取消息")
    @GetMapping("/getMsg")
    public Result getMsg(){
        SysUserSoleEntity soleId = sysUserService.getSoleId(getUserId());
        List<SysUserSoleEntity> temp = sysUserSoleService.list(new LambdaQueryWrapper<SysUserSoleEntity>()
                .eq(SysUserSoleEntity::getSoleId, soleId.getSoleId()));

        List<String> userIds = CommonUtil.collect(temp, SysUserSoleEntity::getUserId);

        List<SysUserMsgEntity> list = sysUserMsgService.list(
                new LambdaQueryWrapper<SysUserMsgEntity>()
                        .in(SysUserMsgEntity::getReader, userIds)
        );
        sysUserMsgService.remove(new LambdaQueryWrapper<SysUserMsgEntity>().in(SysUserMsgEntity::getReader,userIds));
        return Result.success(list);
    }

    @ApiOperation("查询历史消息")
    @PostMapping("/getHistoryMsg")
    public Result getHistoryMsg(@RequestBody(required = false) SysUserMsgVo sysUserMsgVo){

        List<String> userIds=new ArrayList<>();

        SysUserSoleEntity soleId = sysUserService.getSoleId(getUserId());
        if (ObjectUtils.isEmpty(soleId)) {
            userIds.add(getUserId());
        }else {
            List<SysUserSoleEntity> temp = sysUserSoleService.list(new LambdaQueryWrapper<SysUserSoleEntity>()
                    .eq(SysUserSoleEntity::getSoleId, soleId.getSoleId()));
            userIds.addAll(
                    temp.stream()
                            .map(x->x.getUserId())
                            .collect(Collectors.toList())
            );
        }
        if (ObjectUtils.isEmpty(sysUserMsgVo)) {
            sysUserMsgVo=new SysUserMsgVo();
        }
        sysUserMsgVo.setUserIds(userIds);
        PageEntity pageEntity = sysUserMsgService.queryHistory(sysUserMsgVo);
        return Result.success(pageEntity);
    }

    @ApiOperation("更新读取标识")
    @GetMapping("/updateStatus/{id}")
    public Result updateStatus(@PathVariable("id")String id){
        sysUserMsgService.updateStatus(id);
        return Result.success();
    }

    @ApiOperation("查询历史数据详情")
    @GetMapping("/getInfo/{id}")
    public Result getInfo(@PathVariable("id")String id){
        return Result.success(sysUserMsgService.getInfo(id));
    }




}