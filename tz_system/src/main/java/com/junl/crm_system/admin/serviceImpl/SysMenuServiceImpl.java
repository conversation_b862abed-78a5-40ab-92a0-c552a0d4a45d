package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.status.MenuType;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.tools.Assert;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.junl.crm_system.admin.dao.SysMenuDao;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_system.admin.service.SysMenuService;
import com.junl.crm_common.status.IdentityStatus;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuDao, SysMenuEntity> implements SysMenuService {


    @Override
    public List<SysMenuEntity> getMenus(String companyId) {
        List<SysMenuEntity> list = baseMapper.selectList(new QueryWrapper<SysMenuEntity>().select("menu_id","menu_name","visible","parent_id").lambda().eq(SysMenuEntity::getCompanyId, companyId).orderByDesc(SysMenuEntity::getOrderNum));
        List<SysMenuEntity> sort = sort(list);
        return sort;
    }

    /**
     * 递归查询所有菜单和子级菜单
     * @param sysMenuEntities
     */
    public void queryMenu(List<SysMenuEntity> sysMenuEntities){
        if (Assert.notNullCollect(sysMenuEntities)) {
            for (SysMenuEntity sysMenuEntity : sysMenuEntities) {
                if(!sysMenuEntity.getMenuType().equals(MenuType.BUTTON.getType())){
                    List<SysMenuEntity> sysMenuEntities1 = baseMapper.selectList(new QueryWrapper<SysMenuEntity>().lambda().and(par -> par.eq(SysMenuEntity::getParentId, sysMenuEntity.getMenuId())
                            .eq(SysMenuEntity::getVisible, Opposite.ZERO)).orderByDesc(SysMenuEntity::getOrderNum));
                    sysMenuEntity.setChildMenu(sysMenuEntities1);
                    queryMenu(sysMenuEntities1);
                }

            }
        }
    }

    /**
     * 给所有菜单  按等级排列顺序
     * @param list
     * @return
     */
    public List<SysMenuEntity> sort(List<SysMenuEntity> list){
        List<SysMenuEntity> sysMenuEntities=new ArrayList<>();
        Iterator<SysMenuEntity> iterator = list.iterator();
        //取出最顶级菜单
        while (iterator.hasNext()) {
            SysMenuEntity menuEntity = iterator.next();
            if(menuEntity.getParentId().equals(IdentityStatus.GOD.getName())){
                menuEntity.setChildMenu(new ArrayList<>());
                sysMenuEntities.add(menuEntity);
                iterator.remove();
            }
        }
        sort(sysMenuEntities,list);
        return sysMenuEntities;
    }


    /**
     * 递归根据层级赋值
     * @param list
     * @param target
     */
    private void sort(List<SysMenuEntity> list,List<SysMenuEntity> target){
        for (SysMenuEntity sysMenuEntity : list) {
            if (target.size()>0) {
                Iterator<SysMenuEntity> iterator = target.iterator();
                while (iterator.hasNext()) {
                    SysMenuEntity next = iterator.next();
                    if(next.getParentId().equals(sysMenuEntity.getMenuId())){
                        sysMenuEntity.getChildMenu().add(next);
                        next.setChildMenu(new ArrayList<>());
                        iterator.remove();
                    }
                }

            }else{
                break;
            }
            sort(sysMenuEntity.getChildMenu(),target);
        }

    }

}