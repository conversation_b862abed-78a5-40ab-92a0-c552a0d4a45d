package com.junl.crm_system.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.config.ParentController;
import com.junl.crm_common.pojo.admin.SysPositionEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.service.SysPositionService;
import com.junl.crm_system.admin.vo.SysPositionVo;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.common.PageEntity;
import com.junl.crm_common.annotation.Log;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

/**
 * @author: daiqimeng
 **/
@RestController
@RequestMapping("crm/sysPosition")
@Api(tags = "岗位表接口")
@Log("岗位管理模块")
public class SysPositionController extends ParentController {

    @Autowired
    private SysPositionService sysPositionService;

    /**
     * @description: 分页列表查询
     */
    @ApiOperation(value = "岗位表表--分页列表查询",notes="岗位表表--分页列表查询")
    @PostMapping("/pageList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysPositionEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result pageList(@RequestBody SysPositionVo sysPositionVo){
        sysPositionVo.setCompanyId(getCompanyId());
        PageEntity page = sysPositionService.queryPage(sysPositionVo);
        return Result.success(page);
    }

    /**
     * @description: 查询所有列表
     */
    @ApiOperation(value = "岗位表表--列表查询",notes="岗位表表--列表查询")
    @PostMapping("/getList")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysPositionEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getList(){
        List<SysPositionEntity> list = sysPositionService.list(new LambdaQueryWrapper<SysPositionEntity>().eq(SysPositionEntity::getCompanyId,getCompanyId()));
        return Result.success(list);
    }

    /**
     * @description: 根据主键查询
     * @author: daiqimeng
     */

    @ApiOperation(value = "岗位表查询详情",notes="岗位表查询详情")
    @ApiImplicitParams(@ApiImplicitParam(name = "positionId", value = "positionId",required=true))
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysPositionEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    @GetMapping("/info/{positionId}")
    public Result info(@PathVariable("positionId") String positionId){
        return Result.success(sysPositionService.getById(positionId));
    }

    /**
     * @description: 保存
     * @author: daiqimeng
     */
    @ApiOperation(value = "岗位表列表--新增", notes = "岗位表列表--新增")
    @Log(type = LogType.INSERT)
    @PostMapping("/save")
    public Result save(@RequestBody SysPositionEntity sysPositionEntity){
        sysPositionEntity.setPositionId(SnowFlake.getUUId());
        Validate.startValidate(new NotNullVerification(sysPositionEntity,null));
        RuleTools.setField(sysPositionEntity,getSysUserEntity(),true);
        sysPositionService.save(sysPositionEntity);
        return Result.success();
    }
    /**
     * @description: 修改
     * @author: daiqimeng
     */
    @ApiOperation(value = "岗位表--修改", notes = "岗位表--修改")
    @PostMapping("/update")
    @Log(type = LogType.UPDATE)
    public Result update(@RequestBody  SysPositionEntity sysPositionEntity){
        Validate.startValidate(new NotNullVerification(sysPositionEntity,null));
        RuleTools.setField(sysPositionEntity,getSysUserEntity(),true);
        sysPositionService.updateById(sysPositionEntity);
        return Result.success();
    }

    /**
     * @description:  根据主键删除
     * @author: daiqimeng
     */
    @ApiOperation(value = "岗位表--删除", notes = "岗位表--删除")
    @PostMapping("/delete")
    @Log(type = LogType.DELETE)
    public Result delete(@RequestBody String[] positionIds){
        sysPositionService.removeByIds(Arrays.asList(positionIds));
        return Result.success();
    }

    @ApiOperation("根据部门id 查询所有岗位 (不包括岗位人员信息)")
    @GetMapping("/getPosition/{deptId}")
    @ApiResponses({@ApiResponse(code = 200,message = "success",response = SysPositionEntity.class),
            @ApiResponse(code = 500,message = "error")})
    @Log(type = LogType.QUERY)
    public Result getPosition(@PathVariable("deptId")String deptId){
        List<SysPositionEntity> position = sysPositionService.getPosition(deptId);
        return Result.success(position);
    }
}