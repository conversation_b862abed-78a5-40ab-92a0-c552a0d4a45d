package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: SysPositionVo
 * @author:      daiqimeng
 **/
@ApiModel(description = "岗位表表-Vo类")
@Data
public class SysPositionVo extends ParentDto {
    @ApiModelProperty(notes = "岗位名称", allowEmptyValue = true, required = false)
    private  String positionName;

    @ApiModelProperty(notes = "公司Id")
    private String companyId;

}