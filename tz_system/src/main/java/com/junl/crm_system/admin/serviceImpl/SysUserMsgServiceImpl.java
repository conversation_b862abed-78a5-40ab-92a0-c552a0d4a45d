package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;

import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_system.admin.dao.SysUserMsgDao;
import com.junl.crm_system.admin.vo.SysUserMsgVo;
import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_system.admin.service.SysUserMsgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysUserMsgServiceImpl extends ServiceImpl<SysUserMsgDao, SysUserMsgEntity> implements SysUserMsgService {
    /**
     * @description: 分页查询列表
     * @param sysUserMsgVo
     */
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public PageEntity queryPage(SysUserMsgVo sysUserMsgVo) {
        PageUtils.execute(sysUserMsgVo);
        List<SysUserMsgEntity> list = baseMapper.queryList(sysUserMsgVo);
        return PageUtils.getData(list);
    }

    @Override
    public boolean saveHistory(SysUserMsgEntity sysUserMsgEntity) {
        return baseMapper.saveHistory(sysUserMsgEntity);
    }

    @Override
    public PageEntity queryHistory(SysUserMsgVo sysUserMsgVo) {
        PageUtils.execute(sysUserMsgVo);
        List<SysUserMsgEntity> msgEntities = baseMapper.queryHistory(sysUserMsgVo);
        msgEntities.forEach(x->{
            x.setCompanyName(redisUtils.get(RedisKey.COMPANY.getName()+x.getCompanyId()));
        });
        return PageUtils.getData(msgEntities);
    }

    @Override
    public boolean updateStatus(String id) {

        return baseMapper.updateStatus(id);
    }

    @Override
    public SysUserMsgEntity getInfo(String id) {
        return baseMapper.getInfo(id);
    }

    @Override
    public boolean updateStatus(Integer busCode, String msgAccessory) {

        return baseMapper.updateStatusAccessory(busCode,msgAccessory)>0;
    }
}