package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import com.junl.crm_system.admin.dao.SysDataRoleDao;
import com.junl.crm_system.admin.vo.SysDataRoleVo;
import com.junl.crm_common.pojo.admin.SysDataRoleEntity;
import com.junl.crm_system.admin.service.SysDataRoleService;
import org.springframework.stereotype.Service;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
public class SysDataRoleServiceImpl extends ServiceImpl<SysDataRoleDao, SysDataRoleEntity> implements SysDataRoleService {
    /**
     * @description: 分页查询列表
     * @param sysDataRoleVo
     */
    @Override
    public PageEntity queryPage(SysDataRoleVo sysDataRoleVo) {
        PageUtils.execute(sysDataRoleVo);
        List<SysDataRoleEntity> list = baseMapper.queryList(sysDataRoleVo);
        return PageUtils.getData(list);
    }
}