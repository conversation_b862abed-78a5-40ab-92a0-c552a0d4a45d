package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysDictValueEntity;
import com.junl.crm_common.restrain.NotNullVerification;
import com.junl.crm_common.restrain.Validate;
import com.junl.crm_common.tools.RuleTools;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_system.admin.vo.SysDictValueVo;
import com.junl.crm_common.common.PageEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
* @description:SysDictValueservice接口
* @author: daiqimeng
**/
public interface SysDictValueService extends IService<SysDictValueEntity> {
    PageEntity queryPage(SysDictValueVo sysDictValueVo);

    boolean deletes (List<String> ids);


    boolean saveCompanyAll( SysDictValueEntity sysDictValueEntity);


    boolean updateCompanyAll(SysDictValueEntity sysDictValueEntity);



    boolean deleteCompanyAll(String id);
}