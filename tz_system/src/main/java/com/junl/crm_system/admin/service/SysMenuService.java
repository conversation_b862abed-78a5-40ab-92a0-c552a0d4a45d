package com.junl.crm_system.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.junl.crm_common.common.Result;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_system.admin.vo.SysMenuVo;
import com.junl.crm_common.common.PageEntity;

import java.util.List;

/**
* @description:SysMenuservice接口
* @author: daiqimeng
**/
public interface SysMenuService extends IService<SysMenuEntity> {

    List<SysMenuEntity> getMenus(String companyId);
    void queryMenu(List<SysMenuEntity> sysMenuEntities);
    List<SysMenuEntity> sort(List<SysMenuEntity> list);
}