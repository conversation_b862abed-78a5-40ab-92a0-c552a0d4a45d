package com.junl.crm_system.admin.vo;

import com.junl.crm_common.common.ParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

/**
* @description: SysNoticeWriteVo
* @author:      daiqimeng
**/
@ApiModel(description = "公告写表(读写表分离)表-Vo类")
@Data
public class SysNoticeWriteVo extends ParentDto {
    @ApiModelProperty(notes = "公司id")
    private String companyId;

    @ApiModelProperty(notes = "公告标题")
    private String noticeTitle;
}