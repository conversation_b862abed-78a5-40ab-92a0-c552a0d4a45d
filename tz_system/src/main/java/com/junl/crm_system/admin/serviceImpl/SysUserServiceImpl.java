package com.junl.crm_system.admin.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.status.Opposite;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CipherUtils;
import com.junl.crm_common.tools.PageUtils;
import com.junl.crm_common.common.PageEntity;
import java.util.List;
import java.util.Set;

import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_system.admin.dao.SysUserDao;
import com.junl.crm_system.admin.service.SysMenuService;
import com.junl.crm_system.admin.service.SysRoleService;
import com.junl.crm_system.admin.vo.SysUserVo;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_system.exception.RoleException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: daiqimeng
 * @create: 2020-07-21 11:11
 **/
@Service
@Log4j2
public class SysUserServiceImpl extends ServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
    /**
     * @description: 分页查询列表
     * @param sysUserVo
     */

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public PageEntity queryPage(SysUserVo sysUserVo) {
        PageUtils.execute(sysUserVo);
        List<SysUserEntity> list = baseMapper.queryList(sysUserVo);
        return PageUtils.getData(list);
    }

    @Override
    public List<SysMenuEntity> getMenu(SysUserEntity sysUserEntity) {
        List<SysMenuEntity> sysMenuEntities=null;
        String userId = sysUserEntity.getUserId();
        if(userId.equals(IdentityStatus.GOD.getName())){
            sysMenuEntities = sysMenuService.list(new QueryWrapper<SysMenuEntity>().lambda().and(par -> par.eq(SysMenuEntity::getCompanyId, IdentityStatus.GOD.getName())
            .eq(SysMenuEntity::getParentId, IdentityStatus.GOD.getName())).orderByDesc(SysMenuEntity::getOrderNum));
            sysMenuService.queryMenu(sysMenuEntities);
            return sysMenuEntities;
        }


        SysUserEntity userEntity = baseMapper.selectById(userId);
        String companyId = userEntity.getCompanyId();
        //该权限表示公司管理员 可以获取所有菜单
        if (IdentityStatus.GOD.getName().equals(userEntity.getRoleId())) {
            sysMenuEntities = sysMenuService.list(new QueryWrapper<SysMenuEntity>().lambda().and(par -> par.eq(SysMenuEntity::getCompanyId, companyId)
                    .eq(SysMenuEntity::getParentId, IdentityStatus.GOD.getName()).eq(SysMenuEntity::getVisible, Opposite.ZERO))
                    .orderByDesc(SysMenuEntity::getOrderNum));
            sysMenuService.queryMenu(sysMenuEntities);
            return sysMenuEntities;
        }
        try {
            Set<String> menuIds = sysRoleService.getMenuIds(userEntity.getRoleId());
            sysMenuEntities=sysMenuService.list(new QueryWrapper<SysMenuEntity>().lambda().and(x->{
                x.eq(SysMenuEntity::getVisible,Opposite.ZERO)
                        .in(SysMenuEntity::getMenuId, menuIds);
            }).orderByDesc(SysMenuEntity::getOrderNum));
        }catch (RoleException e){
            /**
             * 抛出此异常 代表是最高权限 查询该公司所有菜单
             */
            sysMenuEntities =sysMenuService.list(new QueryWrapper<SysMenuEntity>().lambda().eq(SysMenuEntity::getCompanyId, companyId).orderByDesc(SysMenuEntity::getOrderNum));
        }
        List<SysMenuEntity> sortList = sysMenuService.sort(sysMenuEntities);
        return sortList;
    }

    @Override
    public List<SysUserEntity> getUsers(String companyId) {

        return baseMapper.getUsers(companyId);
    }

    @Override
    public SysUserEntity getInfo(String userId) {
        SysUserEntity info = baseMapper.getInfo(userId);
        try {
            if (Assert.notNull(info)) {
                info.setCompanyName(redisUtils.get(RedisKey.COMPANY.getName()+info.getCompanyId()));
            }
        }catch (Exception e){
            log.error("缓存获取公司名称错误:{}",e.getMessage());
        }
        return info;
    }

    @Override
    @Transactional
    public boolean updatePassword(SysUserVo sysUserVo) {
        SysUserEntity userEntity = baseMapper.selectById(sysUserVo.getUserId());
        Assert.notNull(userEntity,"查询不到该用户");

        String password = sysUserVo.getPassword();
        if (!userEntity.getPassword().equals(CipherUtils.enCoderMd5(password))) {
            throw new RuntimeException("原密码错误,请重新输入");
        }

        userEntity.setPassword(CipherUtils.enCoderMd5(sysUserVo.getNewPassword()));

        //修改全部关联关系的账户密码
        List<SysUserEntity> sysUserEntities = baseMapper.selectList(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getUserName, userEntity.getUserName())
        .ne(SysUserEntity::getCompanyId,IdentityStatus.GOD.getName()));

        if (Assert.notNullCollect(sysUserEntities)) {
            sysUserEntities.forEach(x->{
                x.setPassword(userEntity.getPassword());
                baseMapper.updateById(x);
            });
        }
        baseMapper.updateById(userEntity);

        return true;
    }

    @Override
    public boolean adminUpdatePassword(SysUserVo sysUserVo) {
        SysUserEntity userEntity = baseMapper.selectById(sysUserVo.getUserId());
        Assert.notNull(userEntity,"查询不到该用户");

        userEntity.setPassword(CipherUtils.enCoderMd5(sysUserVo.getPassword()));

        //修改全部关联关系的账户密码
        List<SysUserEntity> sysUserEntities = baseMapper.selectList(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getUserName, userEntity.getUserName())
                .ne(SysUserEntity::getCompanyId,IdentityStatus.GOD.getName()));

        if (Assert.notNullCollect(sysUserEntities)) {
            sysUserEntities.forEach(x->{
                x.setPassword(userEntity.getPassword());
                baseMapper.updateById(x);
            });
        }
        baseMapper.updateById(userEntity);

        return true;
    }

    @Override
    public SysUserEntity getEntity(String userName) {
        return baseMapper.getEntity(userName);
    }

    @Override
    public SysUserEntity getUser(String phone) {
        return baseMapper.getUser(phone);
    }

    @Override
    public SysUserEntity getUserEntity(String userName, String phone) {
        return baseMapper.getUserEntity(userName,phone);
    }

    @Override
    public SysUserSoleEntity getSoleId(String userId) {
        return baseMapper.getSoleId(userId);
    }

    @Override
    public boolean updateOpenId(String openId, String soleId) {
        return baseMapper.updateOpenId(openId,soleId);
    }
}