package com.junl.crm_system;

import com.junl.msg.socket.WebSocketServer;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication(scanBasePackages = {"com.junl"})
@MapperScan({"com.junl.quartz.dao","com.junl.crm_system.admin.dao","com.junl.crm_work.dao"})
//开启事务管理
@EnableTransactionManagement
//开启dubbo扫描
@DubboComponentScan(basePackages = "com.junl")
//开启cglib代理
@EnableAspectJAutoProxy(proxyTargetClass = true)
//开启定时任务
@EnableScheduling
public class CrmSystemApplication {

	public static void main(String[] args){
		/**
		 * log4j 打印彩色日志 系统参数设置  喜欢黑白的可以注释掉
		 */
		System.setProperty("log4j.skipJansi","false");
		/**
		 * log4j 异步日志配置
		 */
		System.setProperty("dubbo.application.logger","log4j2");
		System.setProperty("log4j2.contextSelector","org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");

		/** socket 启动 */
		Thread thread = new Thread(()->{
			new WebSocketServer(8002).start();
		});
		thread.setDaemon(true);
		thread.start();


		SpringApplication.run(CrmSystemApplication.class, args);

	}

}
