package com.junl.crm_system.listener;

import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_common.status.RedisKey;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.RedisUtils;
import com.junl.crm_system.admin.service.SysCompanyService;
import com.junl.crm_work.wx.WxUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 容器加载完的启动类
 * @author: Mr Dai
 * @date: 2021/5/20 9:55
 */
@Component
@Log4j2
public class VesselStartListener  implements ApplicationListener<ContextRefreshedEvent> {


    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private WxUtils wxUtils;

    public static ApplicationContext applicationContexts = null;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        /**
         * 容器上下文对象  初始化时候使用
         */
        ApplicationContext applicationContext = contextRefreshedEvent.getApplicationContext();
        if (!Assert.notNull(applicationContext)) {
            applicationContexts=applicationContext;
        }

        //初始化公司信息到缓存
        SysCompanyService sysCompanyService = applicationContext.getBean(SysCompanyService.class);
        if(Assert.notNull(sysCompanyService)){
            List<SysCompanyEntity> list = sysCompanyService.list();
            list.forEach(x->{
                redisUtils.set(RedisKey.COMPANY.getName()+x.getCompanyId(), x.getCompanyName());
            });
        }

//        List<String> ipList = wxUtils.getIpList();
//        redisUtils.set(RedisKey.IP.getName()+ Opposite.SINGLE, JSONObject.toJSONString(ipList));
        log.info("spring 容器初始化完成");

    }
}