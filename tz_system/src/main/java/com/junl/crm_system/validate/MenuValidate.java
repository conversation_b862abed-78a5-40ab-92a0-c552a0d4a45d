package com.junl.crm_system.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.restrain.Calibrator;
import com.junl.crm_common.status.MenuType;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_system.admin.service.SysMenuService;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_system.util.BusinessUtil;

/**
 * @description: 菜单统一验证
 * @author: daiqimeng
 * @date: 2021/6/1716:04
 */
public class MenuValidate extends Calibrator<SysMenuEntity, SysMenuService> {

    public MenuValidate(SysMenuEntity sysMenuEntity, SysMenuService service) {
        super(sysMenuEntity, service);
    }

    @Override
    protected void validate(SysMenuEntity sysMenuEntity, SysMenuService sysMenuService) {
        //第一个操作的公司id 必须是模块菜单
        sysMenuEntity.setCompanyId(IdentityStatus.GOD.getName());

        //表示该菜单为目录
        if (sysMenuEntity.getMenuType().equals(MenuType.CATALOG.getType())) {
            Assert.notNull(sysMenuEntity.getPath(),"目录前缀路径不能为空");
            if (!sysMenuEntity.getParentId().equals(IdentityStatus.GOD.getName())) {
                throw new RuntimeException("菜单类型不能修改为目录");
            }

            if (sysMenuService.count(
                    new LambdaQueryWrapper<SysMenuEntity>()
                            .and(
                                    x->x.eq(SysMenuEntity::getCompanyId, IdentityStatus.GOD.getName())
                                            .eq(SysMenuEntity::getPath,sysMenuEntity.getPath())
                                            .ne(SysMenuEntity::getMenuId,sysMenuEntity.getMenuId())
                                            .eq(SysMenuEntity::getMenuType,sysMenuEntity.getMenuType())))>0) {
                throw new RuntimeException("目录路径前缀重复,请重新修改.");
            }

        }else if(sysMenuEntity.getMenuType().equals(MenuType.MENU.getType())){
            Assert.notNull(sysMenuEntity.getPath(),"菜单前缀路径不能为空");
            Assert.notNull(sysMenuEntity.getComponent(),"菜单文件映射路径不能为空");

            if (sysMenuService.count(
                    new LambdaQueryWrapper<SysMenuEntity>()
                            .and(x->x.eq(SysMenuEntity::getCompanyId, IdentityStatus.GOD.getName())
                                    .eq(SysMenuEntity::getPath,sysMenuEntity.getPath())
                                    .eq(SysMenuEntity::getMenuType,sysMenuEntity.getMenuType())
                                    .ne(SysMenuEntity::getMenuId,sysMenuEntity.getMenuId())))>0) {
                throw new RuntimeException("菜单路径前缀重复,请重新修改.");
            }

            if (sysMenuService.count(
                    new LambdaQueryWrapper<SysMenuEntity>()
                            .and(
                                    x->x.eq(SysMenuEntity::getCompanyId, IdentityStatus.GOD.getName())
                                            .eq(SysMenuEntity::getComponent,sysMenuEntity.getComponent())
                                            .ne(SysMenuEntity::getMenuId,sysMenuEntity.getMenuId()))
            )>0) {
                throw new RuntimeException("菜单文件映射路径重复,请重新修改.");
            }

            /**
             * 如国是菜单 需要配置系统6个按钮  增、删、改、导入、导出、下载
             */
            //sysMenuEntity.setChildMenu(BusinessUtil.getDefaultButton(sysMenuEntity.getMenuId(),sysMenuEntity.getCompanyId()));
        }

        if (!sysMenuEntity.getParentId().equals(IdentityStatus.GOD.getName())) {
            String parentId = sysMenuEntity.getParentId();
            SysMenuEntity byId = sysMenuService.getById(parentId);
            if(byId.getMenuType().equals(MenuType.BUTTON.getType())){
                throw new RuntimeException("按钮不允许增加子级");
            }
        }

    }
}
