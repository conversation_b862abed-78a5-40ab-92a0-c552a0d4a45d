package com.junl.crm_system.expose;

import com.junl.crm_common.pojo.admin.SysUserMsgEntity;
import com.junl.crm_system.admin.service.SysUserMsgService;
import com.junl.system.UserMsgService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: daiqimeng
 * @date: 2021/7/2321:41
 */
@Component
@Service
public class UserMsgServiceImpl  implements UserMsgService {

    @Autowired
    private SysUserMsgService sysUserMsgService;

//    @Override
//    public boolean saveMsg(SysUserMsgEntity sysUserMsgEntity) {
//        return sysUserMsgService.save(sysUserMsgEntity);
//    }

    @Override
    public boolean saveHistory(SysUserMsgEntity sysUserMsgEntity) {
        return sysUserMsgService.saveHistory(sysUserMsgEntity);
    }

    @Override
    public boolean updateStatus(String id) {
        return sysUserMsgService.updateStatus(id);
    }

    @Override
    public boolean updateStatus(Integer busCode, String msgAccessory) {
        return sysUserMsgService.updateStatus(busCode,msgAccessory);
    }
}
