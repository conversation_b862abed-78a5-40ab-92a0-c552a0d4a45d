package com.junl.crm_system.expose;


import com.junl.crm_common.pojo.admin.SysCompanyEntity;
import com.junl.crm_system.admin.service.SysCompanyService;
import com.junl.system.CompanyService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 实现对外提供的公司接口实现
 * @author: daiqimeng
 * @date: 2021/6/117:04
 */
@Component
@Service
public class CompanyServiceImpl implements CompanyService {

    @Autowired
    private SysCompanyService sysCompanyService;

    @Override
    //获取公司组织架构的方法
    public SysCompanyEntity getCompany(String companyId) {
        return sysCompanyService.getAllOrganization(companyId);
    }
}
