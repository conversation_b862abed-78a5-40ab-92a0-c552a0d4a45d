package com.junl.crm_system.expose;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.junl.crm_common.pojo.admin.SysDataRoleEntity;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.pojo.admin.SysUserSoleEntity;
import com.junl.crm_common.status.IdentityStatus;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.CommonUtil;
import com.junl.crm_system.admin.service.SysDataRoleService;
import com.junl.crm_system.admin.service.SysUserService;
import com.junl.system.UserService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 实现对外提供用户查询的接口
 * @author: daiqimeng
 * @date: 2021/6/911:04
 */
@Component
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysDataRoleService sysDataRoleService;


    @Override
    public SysUserEntity getUserInfoByTel(String phone) {
        SysUserEntity user = sysUserService.getUser(phone);
        return user;
    }

    @Override
    public SysUserEntity getUserInfo(String userId) {
        return sysUserService.getInfo(userId);
    }

    @Override
    public List<String> getDeptUsers(String deptId) {
        List<SysUserEntity> list = sysUserService.list(new QueryWrapper<SysUserEntity>().select("user_id").eq("dept_id", deptId));
        List<String> collect = CommonUtil.collect(list, SysUserEntity::getUserId);
        return collect;
    }

    @Override
    public List<String> getUsers(String dataId) {
        List<SysDataRoleEntity> list = sysDataRoleService.list(new LambdaQueryWrapper<SysDataRoleEntity>().eq(SysDataRoleEntity::getDataId, dataId));
        List<String> deptIds = CommonUtil.collect(list, SysDataRoleEntity::getDeptId);

        List<SysUserEntity> userList = sysUserService.list(new QueryWrapper<SysUserEntity>().select("user_id").in("dept_id", deptIds));
        List<String> userIds = CommonUtil.collect(userList, SysUserEntity::getUserId);

        return userIds;
    }

    @Override
    public String getName(String userId) {
        SysUserEntity real_name = sysUserService.getOne(new QueryWrapper<SysUserEntity>().select("real_name").lambda().eq(SysUserEntity::getUserId, userId));
        if (Assert.notNull(real_name)) {
            return real_name.getRealName();
        }
        return null;
    }

    @Override
    public boolean updateOpenId(String openId, String soleId) {
        return sysUserService.updateOpenId(openId,soleId);
    }

    @Override
    public SysUserSoleEntity getSoleId(String phone) {
        List<SysUserEntity> list = sysUserService.list(new LambdaQueryWrapper<SysUserEntity>()
                .and(e->e.eq(SysUserEntity::getPhone, phone)
                .ne(SysUserEntity::getCompanyId, IdentityStatus.GOD.getName())));

        if (!Assert.notNullCollect(list)) {
            return null;
        }
        SysUserSoleEntity soleId = sysUserService.getSoleId(list.get(0).getUserId());
        return soleId;
    }

    @Override
    public SysUserSoleEntity getSole(String userid) {
        return sysUserService.getSoleId(userid);
    }

}
