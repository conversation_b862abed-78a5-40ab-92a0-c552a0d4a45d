package com.junl.crm_system.util;

import com.junl.crm_common.pojo.admin.SysMenuEntity;
import com.junl.crm_common.status.MenuType;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_common.status.IdentityStatus;

import java.util.*;

/**
 * @description: 业务工具
 * @author: daiqimeng
 * @date: 2021/6/911:54
 */
public class BusinessUtil {


    /**
     * @describe:  获取系统默认的按钮
     * <AUTHOR>
     * @date 2021/6/17 16:29
     * @param
     * @return: {@link List< SysMenuEntity>}
     */
    public static List<SysMenuEntity> getDefaultButton(String parentId,String companyId){
        List<SysMenuEntity> list=new ArrayList<>();

        SysMenuEntity insert=new SysMenuEntity();
        insert.setMenuId(SnowFlake.getUUId());
        insert.setMenuType(MenuType.BUTTON.getType());
        insert.setParentId(parentId);
        insert.setMenuName("新增");
        insert.setCreateBy(IdentityStatus.GOD.getName());
        insert.setCompanyId(companyId);
        list.add(insert);

        SysMenuEntity update=new SysMenuEntity();
        update.setMenuId(SnowFlake.getUUId());
        update.setMenuType(MenuType.BUTTON.getType());
        update.setParentId(parentId);
        update.setMenuName("修改");
        update.setCreateBy(IdentityStatus.GOD.getName());
        update.setCompanyId(companyId);
        list.add(update);


        SysMenuEntity delete=new SysMenuEntity();
        delete.setMenuId(SnowFlake.getUUId());
        delete.setMenuType(MenuType.BUTTON.getType());
        delete.setParentId(parentId);
        delete.setMenuName("删除");
        delete.setCreateBy(IdentityStatus.GOD.getName());
        delete.setCompanyId(companyId);
        list.add(delete);



        SysMenuEntity imports=new SysMenuEntity();
        imports.setMenuId(SnowFlake.getUUId());
        imports.setMenuType(MenuType.BUTTON.getType());
        imports.setParentId(parentId);
        imports.setMenuName("导入");
        imports.setCreateBy(IdentityStatus.GOD.getName());
        imports.setCompanyId(companyId);
        list.add(imports);


        SysMenuEntity export=new SysMenuEntity();
        export.setMenuId(SnowFlake.getUUId());
        export.setMenuType(MenuType.BUTTON.getType());
        export.setParentId(parentId);
        export.setMenuName("导出");
        export.setCreateBy(IdentityStatus.GOD.getName());
        export.setCompanyId(companyId);
        list.add(export);


        SysMenuEntity download=new SysMenuEntity();
        download.setMenuId(SnowFlake.getUUId());
        download.setMenuType(MenuType.BUTTON.getType());
        download.setParentId(parentId);
        download.setMenuName("下载");
        download.setCreateBy(IdentityStatus.GOD.getName());
        download.setCompanyId(companyId);
        list.add(download);

        return list;
    }

    public static void main(String[] args) {


    }


}
