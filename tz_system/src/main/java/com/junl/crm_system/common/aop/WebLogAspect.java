package com.junl.crm_system.common.aop;


import com.alibaba.fastjson.JSONObject;
import com.junl.crm_common.pojo.admin.SysUserEntity;
import com.junl.crm_common.status.LogType;
import com.junl.crm_common.tools.Assert;
import com.junl.crm_common.tools.JWTUtil;
import com.junl.crm_common.tools.SnowFlake;
import com.junl.crm_common.pojo.admin.SysLogEntity;
import com.junl.crm_system.admin.service.SysLogService;
import com.junl.crm_common.annotation.Log;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

/**
 * 日志切面类
 */
@Aspect
//@Component
@Log4j2
public class WebLogAspect {

    @Autowired
    private SysLogService sysLogService;


    @Pointcut(value ="@annotation(com.junl.crm_common.annotation.Log)")
    public void pointcut(){};


    @SneakyThrows
    @Before("pointcut()")
    public void before(JoinPoint jp){

        /**
         * 通过工具类获取token 解析成系统对象
         */
        SysLogEntity sysLogEntity=new SysLogEntity();
        String principal = (String) SecurityUtils.getSubject().getPrincipal();
        if (Assert.notNull(principal)) {
            SysUserEntity username = JWTUtil.getUsername(principal);
            sysLogEntity.setCreateBy(username.getUserId());
            sysLogEntity.setCompanyId(username.getCompanyId());
        }

        sysLogEntity.setLogId(SnowFlake.getUUId());
        Signature signature = jp.getSignature();
        MethodSignature msig = null;
        if (!(signature instanceof MethodSignature)) {
            throw new IllegalArgumentException("该注解只能用于方法");
        }else{
            msig = (MethodSignature) signature;
            Object target = jp.getTarget();
            Class<?> aClass = target.getClass();
            /**
             * 切面就根据注解来的 所以进来无需再判断是否有这个注解了
             */
            try {
                //获取类上面的注释 类上面是整个项目模块的描述
                Log declaredAnnotation = aClass.getDeclaredAnnotation(Log.class);
                String value = declaredAnnotation.value();
                sysLogEntity.setLogDescribe(value);


                //方法上获取类型
                Method currentMethod = aClass.getMethod(msig.getName(), msig.getParameterTypes());
                Log methodLog = currentMethod.getDeclaredAnnotation(Log.class);
                LogType type = methodLog.type();
                sysLogEntity.setLogType(type.getType());

                //获取参数转为json
                Object[] args = jp.getArgs();
                if (args!=null&&args.length>0) {
                    Object arg = args[0];
                    String s = JSONObject.toJSONString(arg);
                    sysLogEntity.setLogParam(s);
                }
            }catch (Exception e){
                log.error("切面日志信息发生错误："+e.getMessage());
            }

            log.info("系统日志：  {}",JSONObject.toJSONString(sysLogEntity));
            //插入数据库
            sysLogService.save(sysLogEntity);
        }

    }


}
